{"name": "@underwriting/frontend", "version": "1.0.0", "private": true, "description": "AI Underwriting Platform Frontend", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "clean": "rm -rf .next dist"}, "dependencies": {"@underwriting/config": "*", "@underwriting/shared": "*", "@underwriting/ui": "*", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@types/socket.io-client": "^3.0.0", "date-fns": "^4.1.0", "framer-motion": "^12.23.6", "lucide-react": "^0.525.0", "next": "15.4.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "recharts": "^3.1.0", "socket.io-client": "^4.8.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.0.15", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^9.31.0", "eslint-config-next": "15.4.2", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3"}}