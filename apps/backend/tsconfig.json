{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": false, "noUncheckedIndexedAccess": true, "skipLibCheck": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["src/*"]}, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests", "**/__tests__/**/*", "**/*.test.ts", "**/*.spec.ts"]}