{"name": "@underwriting/backend", "version": "1.0.0", "private": true, "description": "AI Underwriting Platform Backend API", "main": "dist/index.js", "scripts": {"postinstall": "prisma generate", "dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest --passWithNoTests", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "typecheck": "tsc --noEmit && tsc --noEmit --project tsconfig.test.json", "clean": "rm -rf dist", "db:generate": "npx prisma generate", "db:push": "npx prisma db push", "db:seed": "tsx prisma/seed.ts", "db:studio": "npx prisma studio", "test:unit": "jest --config jest.unit.config.js", "test:integration": "jest --config jest.integration.config.js", "test:all": "npm run test:unit && npm run test:integration", "test:watch:unit": "jest --config jest.unit.config.js --watch", "test:watch:integration": "jest --config jest.integration.config.js --watch"}, "dependencies": {"@prisma/client": "^6.12.0", "@types/bcrypt": "^6.0.0", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/uuid": "^10.0.0", "@underwriting/config": "*", "@underwriting/shared": "*", "axios": "^1.7.9", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "compression": "^1.8.1", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^17.2.0", "express": "^4.21.2", "express-validator": "^7.2.1", "helmet": "^8.1.0", "ioredis": "^5.4.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.14", "puppeteer": "^22.15.0", "qrcode": "^1.5.4", "redis": "^4.7.0", "reflect-metadata": "^0.2.2", "sharp": "^0.33.5", "socket.io": "^4.8.1", "speakeasy": "^2.0.0", "stripe": "^17.4.0", "turbo": "^2.5.5", "uuid": "^11.1.0", "winston": "^3.17.0"}, "devDependencies": {"@eslint/js": "^9.31.0", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.23", "@types/express-validator": "^2.20.33", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^20.11.1", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.15", "@types/puppeteer": "^5.4.7", "@types/reflect-metadata": "^0.0.5", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "eslint": "^9.31.0", "husky": "^9.1.7", "jest": "^29.7.0", "nodemon": "^3.1.10", "prisma": "^6.12.0", "supertest": "^7.1.3", "ts-jest": "^29.4.0", "ts-node": "^10.9.1", "tsx": "^4.19.2", "typescript": "^5.3.3"}}