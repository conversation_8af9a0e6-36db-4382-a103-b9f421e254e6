import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface ValidationResult {
  testName: string;
  success: boolean;
  error?: string;
  duration: number;
  details?: any;
}

class SchemaValidator {
  private results: ValidationResult[] = [];

  async runTest(testName: string, testFn: () => Promise<any>): Promise<void> {
    const startTime = Date.now();
    try {
      console.log(`🧪 Running test: ${testName}`);
      const result = await testFn();
      const duration = Date.now() - startTime;

      this.results.push({
        testName,
        success: true,
        duration,
        details: result,
      });

      console.log(`✅ ${testName} - Passed (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;

      this.results.push({
        testName,
        success: false,
        error: error instanceof Error ? error.message : String(error),
        duration,
      });

      console.log(`❌ ${testName} - Failed (${duration}ms): ${error}`);
    }
  }

  async validateBasicCRUD(): Promise<void> {
    console.log('\n📊 Validating Basic CRUD Operations...');

    // Test 1: Create Tenant
    await this.runTest('Create Tenant', async () => {
      const tenant = await prisma.tenant.create({
        data: {
          name: 'Test Validation Tenant',
          slug: 'test-validation-tenant',
          domain: 'test.validation.com',
          status: 'ACTIVE',
          subscriptionTier: 'STARTER',
        },
      });
      return { tenantId: tenant.id };
    });

    // Test 2: Create User
    await this.runTest('Create User', async () => {
      const testTenant = await prisma.tenant.findUnique({
        where: { slug: 'test-validation-tenant' },
      });
      if (!testTenant) throw new Error('Test tenant not found');

      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
          tenantId: testTenant.id,
          role: 'USER',
          emailVerified: true,
        },
      });
      return { userId: user.id };
    });

    // Test 3: Create Application
    await this.runTest('Create Application', async () => {
      const testTenant = await prisma.tenant.findUnique({
        where: { slug: 'test-validation-tenant' },
      });
      if (!testTenant) throw new Error('Test tenant not found');

      const application = await prisma.application.create({
        data: {
          applicationNumber: 'VAL-TEST-001',
          tenantId: testTenant.id,
          businessName: 'Test Business LLC',
          businessType: 'LLC',
          industry: 'TECHNOLOGY',
          businessEmail: '<EMAIL>',
          businessAddress: {
            street: '123 Test St',
            city: 'Test City',
            state: 'TS',
            zip: '12345',
            country: 'US',
          },
        },
      });
      return { applicationId: application.id };
    });

    // Test 4: Update Application
    await this.runTest('Update Application', async () => {
      const application = await prisma.application.findUnique({
        where: { applicationNumber: 'VAL-TEST-001' },
      });
      if (!application) throw new Error('Test application not found');

      const updated = await prisma.application.update({
        where: { id: application.id },
        data: {
          status: 'SUBMITTED',
          stage: 'DOCUMENT_UPLOAD',
          priority: 'HIGH',
        },
      });
      return { status: updated.status, stage: updated.stage };
    });

    // Test 5: Delete Test Data
    await this.runTest('Cleanup Test Data', async () => {
      const testTenant = await prisma.tenant.findUnique({
        where: { slug: 'test-validation-tenant' },
      });
      if (!testTenant) throw new Error('Test tenant not found');

      // Delete in proper order due to foreign key constraints
      await prisma.application.deleteMany({
        where: { tenantId: testTenant.id },
      });
      await prisma.user.deleteMany({ where: { tenantId: testTenant.id } });
      await prisma.tenant.delete({ where: { id: testTenant.id } });

      return { deleted: true };
    });
  }

  async validateRelationships(): Promise<void> {
    console.log('\n🔗 Validating Relationships...');

    // Test 1: Complex relationship queries
    await this.runTest('Tenant-Application-User Relationships', async () => {
      const tenantsWithData = await prisma.tenant.findMany({
        include: {
          applications: {
            take: 5,
            include: {
              documents: { take: 3 },
              conversations: { take: 2 },
            },
          },
          users: { take: 10 },
        },
        take: 3,
      });

      return {
        tenantsFound: tenantsWithData.length,
        totalApplications: tenantsWithData.reduce(
          (sum, t) => sum + t.applications.length,
          0
        ),
        totalUsers: tenantsWithData.reduce((sum, t) => sum + t.users.length, 0),
      };
    });

    // Test 2: Document-Application relationship
    await this.runTest('Document-Application Relationships', async () => {
      const documentsWithApps = await prisma.document.findMany({
        include: {
          application: {
            select: {
              applicationNumber: true,
              businessName: true,
              status: true,
            },
          },
        },
        take: 10,
      });

      return { documentsFound: documentsWithApps.length };
    });

    // Test 3: AI Model Usage tracking
    await this.runTest('AI Usage Tracking Relationships', async () => {
      const aiUsage = await prisma.aiModelUsage.findMany({
        include: {
          conversation: {
            include: {
              application: {
                select: { applicationNumber: true, businessName: true },
              },
            },
          },
        },
        take: 5,
      });

      return { usageRecords: aiUsage.length };
    });

    // Test 4: Workflow relationships
    await this.runTest('Workflow Execution Relationships', async () => {
      const workflows = await prisma.workflowExecution.findMany({
        include: {
          workflowDefinition: true,
          application: {
            select: { applicationNumber: true, businessName: true },
          },
          stepExecutions: {
            include: {
              tasks: { take: 3 },
            },
          },
        },
        take: 5,
      });

      return { workflowExecutions: workflows.length };
    });
  }

  async validateIndexPerformance(): Promise<void> {
    console.log('\n⚡ Validating Index Performance...');

    // Test 1: Tenant-based queries (should be fast with tenant_id indexes)
    await this.runTest(
      'Tenant-scoped Application Query Performance',
      async () => {
        const startTime = Date.now();

        const applications = await prisma.application.findMany({
          where: {
            tenantId: { not: undefined },
            status: 'APPROVED',
          },
          orderBy: { createdAt: 'desc' },
          take: 100,
        });

        const queryTime = Date.now() - startTime;

        return {
          applications: applications.length,
          queryTime,
          performanceRating:
            queryTime < 100
              ? 'Excellent'
              : queryTime < 500
                ? 'Good'
                : 'Needs Optimization',
        };
      }
    );

    // Test 2: User authentication query
    await this.runTest('User Authentication Query Performance', async () => {
      const startTime = Date.now();

      const users = await prisma.user.findMany({
        where: {
          email: { contains: '@' },
          emailVerified: true,
        },
        take: 50,
      });

      const queryTime = Date.now() - startTime;

      return {
        users: users.length,
        queryTime,
        performanceRating:
          queryTime < 50
            ? 'Excellent'
            : queryTime < 200
              ? 'Good'
              : 'Needs Optimization',
      };
    });

    // Test 3: Document search query
    await this.runTest('Document Search Query Performance', async () => {
      const startTime = Date.now();

      const documents = await prisma.document.findMany({
        where: {
          documentType: 'BUSINESS_LICENSE',
          status: 'PROCESSED',
        },
        take: 50,
      });

      const queryTime = Date.now() - startTime;

      return {
        documents: documents.length,
        queryTime,
        performanceRating:
          queryTime < 100
            ? 'Excellent'
            : queryTime < 300
              ? 'Good'
              : 'Needs Optimization',
      };
    });
  }

  async validateBusinessLogic(): Promise<void> {
    console.log('\n💼 Validating Business Logic Constraints...');

    // Test 1: Unique constraints
    await this.runTest('Unique Constraint Validation', async () => {
      try {
        // Try to create tenant with duplicate slug (should fail)
        await prisma.tenant.create({
          data: {
            name: 'Duplicate Test',
            slug: 'demo-fintech', // This should already exist
            subscriptionTier: 'STARTER',
          },
        });
        throw new Error(
          'Unique constraint not working - duplicate tenant created'
        );
      } catch (error) {
        // This should fail with unique constraint error
        if (
          error instanceof Error &&
          error.message.includes('Unique constraint')
        ) {
          return { uniqueConstraintWorking: true };
        }
        throw error;
      }
    });

    // Test 2: Foreign key constraints
    await this.runTest('Foreign Key Constraint Validation', async () => {
      try {
        // Try to create application with non-existent tenant (should fail)
        await prisma.application.create({
          data: {
            applicationNumber: 'FK-TEST-001',
            tenantId: 'non-existent-tenant-id',
            businessName: 'FK Test Business',
            businessType: 'LLC',
            industry: 'TECHNOLOGY',
            businessEmail: '<EMAIL>',
            businessAddress: {
              street: '123 FK St',
              city: 'FK City',
              state: 'FK',
              zip: '12345',
              country: 'US',
            },
          },
        });
        throw new Error(
          'Foreign key constraint not working - application with invalid tenant created'
        );
      } catch (error) {
        // This should fail with foreign key error
        if (
          error instanceof Error &&
          (error.message.includes('Foreign key') ||
            error.message.includes('constraint'))
        ) {
          return { foreignKeyConstraintWorking: true };
        }
        throw error;
      }
    });

    // Test 3: Enum constraints
    await this.runTest('Enum Constraint Validation', async () => {
      const tenant = await prisma.tenant.findFirst();
      if (!tenant) throw new Error('No tenant found for enum test');

      try {
        // Try to create application with invalid status (should fail at Prisma level)
        const invalidData = {
          applicationNumber: 'ENUM-TEST-001',
          tenantId: tenant.id,
          businessName: 'Enum Test Business',
          businessType: 'INVALID_TYPE' as any, // Invalid enum value
          industry: 'TECHNOLOGY',
          businessEmail: '<EMAIL>',
          businessAddress: {
            street: '123 Enum St',
            city: 'Enum City',
            state: 'EN',
            zip: '12345',
            country: 'US',
          },
        };

        await prisma.application.create({ data: invalidData });
        throw new Error(
          'Enum constraint not working - invalid enum value accepted'
        );
      } catch (error) {
        // This should fail with enum validation error
        if (
          error instanceof Error &&
          (error.message.includes('Invalid') || error.message.includes('enum'))
        ) {
          return { enumConstraintWorking: true };
        }
        throw error;
      }
    });
  }

  async validateDataConsistency(): Promise<void> {
    console.log('\n🔍 Validating Data Consistency...');

    // Test 1: Check for orphaned records
    await this.runTest('Orphaned Records Check', async () => {
      const orphanedDocuments = await prisma.document.findMany({
        where: {
          application: null,
        },
      });

      const orphanedConversations = await prisma.conversation.findMany({
        where: {
          application: null,
        },
      });

      return {
        orphanedDocuments: orphanedDocuments.length,
        orphanedConversations: orphanedConversations.length,
        hasOrphanedRecords:
          orphanedDocuments.length > 0 || orphanedConversations.length > 0,
      };
    });

    // Test 2: Data type consistency
    await this.runTest('Data Type Consistency Check', async () => {
      const applicationsWithInvalidData = await prisma.application.findMany({
        where: {
          OR: [
            { riskScore: { lt: 0 } },
            { riskScore: { gt: 1 } },
            { monthlyRevenue: { lt: 0 } },
          ],
        },
      });

      return {
        invalidDataRecords: applicationsWithInvalidData.length,
        dataConsistency: applicationsWithInvalidData.length === 0,
      };
    });

    // Test 3: Referential integrity
    await this.runTest('Referential Integrity Check', async () => {
      const usersWithInvalidTenants = await prisma.user.findMany({
        where: {
          tenant: null,
        },
      });

      const applicationsWithInvalidTenants = await prisma.application.findMany({
        where: {
          tenant: null,
        },
      });

      return {
        usersWithInvalidTenants: usersWithInvalidTenants.length,
        applicationsWithInvalidTenants: applicationsWithInvalidTenants.length,
        referentialIntegrity:
          usersWithInvalidTenants.length === 0 &&
          applicationsWithInvalidTenants.length === 0,
      };
    });
  }

  async validateComplexQueries(): Promise<void> {
    console.log('\n🧮 Validating Complex Analytical Queries...');

    // Test 1: Multi-tenant analytics query
    await this.runTest('Multi-Tenant Analytics Query', async () => {
      const analytics = await prisma.tenant.findMany({
        select: {
          id: true,
          name: true,
          _count: {
            applications: true,
            users: true,
          },
          applications: {
            select: {
              status: true,
              riskLevel: true,
              monthlyRevenue: true,
              requestedAmount: true,
            },
          },
        },
        take: 5,
      });

      const summary = analytics.map((tenant) => ({
        tenantName: tenant.name,
        totalApplications: tenant._count.applications,
        totalUsers: tenant._count.users,
        approvedCount: tenant.applications.filter(
          (app) => app.status === 'APPROVED'
        ).length,
        averageRevenue:
          tenant.applications.reduce(
            (sum, app) => sum + Number(app.monthlyRevenue || 0),
            0
          ) / Math.max(tenant.applications.length, 1),
      }));

      return { tenantAnalytics: summary };
    });

    // Test 2: Risk assessment aggregation
    await this.runTest('Risk Assessment Aggregation Query', async () => {
      const riskStats = await prisma.riskAssessment.groupBy({
        by: ['riskLevel'],
        _count: {
          id: true,
        },
        _avg: {
          overallScore: true,
          confidence: true,
        },
        _max: {
          overallScore: true,
        },
        _min: {
          overallScore: true,
        },
      });

      return { riskStatistics: riskStats };
    });

    // Test 3: Document processing statistics
    await this.runTest('Document Processing Statistics Query', async () => {
      const docStats = await prisma.document.groupBy({
        by: ['documentType', 'status'],
        _count: {
          id: true,
        },
        _avg: {
          ocrConfidence: true,
          fraudScore: true,
        },
      });

      return { documentStatistics: docStats };
    });
  }

  async generateReport(): Promise<void> {
    console.log('\n📋 Generating Validation Report...');

    const totalTests = this.results.length;
    const passedTests = this.results.filter((r) => r.success).length;
    const failedTests = totalTests - passedTests;
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
    const averageDuration = totalDuration / totalTests;

    console.log('\n' + '='.repeat(80));
    console.log('🎯 DATABASE SCHEMA VALIDATION REPORT');
    console.log('='.repeat(80));
    console.log(`📊 Total Tests: ${totalTests}`);
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    console.log(
      `📈 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`
    );
    console.log(`⏱️  Total Duration: ${totalDuration}ms`);
    console.log(`⚡ Average Duration: ${averageDuration.toFixed(1)}ms`);
    console.log('='.repeat(80));

    if (failedTests > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.results
        .filter((r) => !r.success)
        .forEach((result) => {
          console.log(`   • ${result.testName}: ${result.error}`);
        });
    }

    console.log('\n✅ VALIDATION SUMMARY:');
    const categories = [
      'Basic CRUD Operations',
      'Relationships',
      'Index Performance',
      'Business Logic Constraints',
      'Data Consistency',
      'Complex Analytical Queries',
    ];

    categories.forEach((category) => {
      const categoryTests = this.results.filter(
        (r) =>
          r.testName.includes(category) ||
          r.testName
            .toLowerCase()
            .includes(category.toLowerCase().split(' ')[0])
      );
      const passed = categoryTests.filter((r) => r.success).length;
      const total = categoryTests.length;

      if (total > 0) {
        const status =
          passed === total ? '✅' : passed > total * 0.5 ? '⚠️' : '❌';
        console.log(
          `   ${status} ${category}: ${passed}/${total} tests passed`
        );
      }
    });

    console.log('\n🎉 Schema validation completed!');
  }

  async runAllValidations(): Promise<void> {
    console.log('🚀 Starting comprehensive database schema validation...');
    console.log('📅 ' + new Date().toISOString());

    try {
      await this.validateBasicCRUD();
      await this.validateRelationships();
      await this.validateIndexPerformance();
      await this.validateBusinessLogic();
      await this.validateDataConsistency();
      await this.validateComplexQueries();

      await this.generateReport();
    } catch (error) {
      console.error('💥 Critical error during validation:', error);
      throw error;
    }
  }
}

async function main() {
  const validator = new SchemaValidator();

  try {
    await validator.runAllValidations();
  } catch (error) {
    console.error('❌ Validation failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run validation if called directly
if (require.main === module) {
  main();
}

export { SchemaValidator };
