module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  displayName: 'Unit Tests',
  testMatch: ['<rootDir>/src/__tests__/unit/**/*.test.ts'],
  setupFilesAfterEnv: ['<rootDir>/src/__tests__/setup.unit.ts'],
  transform: {
    '^.+\\.tsx?$': ['ts-jest', {
      tsconfig: 'tsconfig.test.json',
    }],
  },
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/__tests__/**',
    '!src/prisma/**',
  ],
  coverageReporters: ['text', 'lcov'],
  testTimeout: 10000,
};
