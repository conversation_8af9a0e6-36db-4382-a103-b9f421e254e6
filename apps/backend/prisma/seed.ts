import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Seed Subscription Tiers
  console.log('📊 Seeding subscription tiers...');
  const subscriptionTiers = [
    {
      tier: 'STARTER',
      displayName: 'Starter',
      aiQuotaLimit: 10000,
      featuresIncluded: {
        applications: 100,
        users: 5,
        integrations: 3,
        advancedAnalytics: false,
        customBranding: false,
      },
      monthlyCost: 99.0,
      isActive: true,
    },
    {
      tier: 'PROFESSIONAL',
      displayName: 'Professional',
      aiQuotaLimit: 50000,
      featuresIncluded: {
        applications: 500,
        users: 25,
        integrations: 10,
        advancedAnalytics: true,
        customBranding: true,
      },
      monthlyCost: 299.0,
      isActive: true,
    },
    {
      tier: 'ENTERPRISE',
      displayName: 'Enterprise',
      aiQuotaLimit: 200000,
      featuresIncluded: {
        applications: -1, // unlimited
        users: -1, // unlimited
        integrations: -1, // unlimited
        advancedAnalytics: true,
        customBranding: true,
        dedicatedSupport: true,
      },
      monthlyCost: 999.0,
      isActive: true,
    },
  ];

  // Note: Subscription tiers might be handled as enums, so we'll skip creating them as records

  // Seed Demo Tenant
  console.log('🏢 Seeding demo tenant...');
  const demoTenant = await prisma.tenant.upsert({
    where: { slug: 'demo-fintech' },
    update: {},
    create: {
      name: 'Demo FinTech Company',
      slug: 'demo-fintech',
      domain: 'demo.underwriting.ai',
      status: 'ACTIVE',
      subscriptionTier: 'PROFESSIONAL',
      branding: {
        primaryColor: '#2563eb',
        secondaryColor: '#f59e0b',
        logo: '/assets/logos/demo-fintech.png',
        companyName: 'Demo FinTech',
      },
      customDomain: 'underwriting.demofintech.com',
      aiQuotaMonthly: 50000,
      aiUsageCurrentMonth: 12450,
      hitlRulesEnabled: true,
      features: {
        aiConversations: true,
        documentOcr: true,
        riskAssessment: true,
        workflowAutomation: true,
        customIntegrations: true,
        advancedAnalytics: true,
        shadowMode: true,
      },
      settings: {
        defaultLanguage: 'en',
        timezone: 'America/New_York',
        autoAssignTasks: true,
        emailNotifications: true,
        slackIntegration: true,
      },
    },
  });

  // Seed Users
  console.log('👥 Seeding users...');
  const demoPassword = process.env.SEED_DEMO_PASSWORD || 'demo123!';
  const passwordHash = await bcrypt.hash(demoPassword, 12);

  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Sarah Admin',
      passwordHash,
      tenantId: demoTenant.id,
      role: 'ADMIN',
      permissions: {
        applications: ['read', 'write', 'delete'],
        users: ['read', 'write', 'delete'],
        settings: ['read', 'write'],
        analytics: ['read'],
        workflows: ['read', 'write'],
      },
      emailVerified: true,
      emailVerifiedAt: new Date(),
      avatar: '/assets/avatars/sarah-admin.jpg',
      timezone: 'America/New_York',
      locale: 'en',
    },
  });

  const underwriterUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Michael Underwriter',
      passwordHash,
      tenantId: demoTenant.id,
      role: 'UNDERWRITER',
      permissions: {
        applications: ['read', 'write'],
        riskAssessments: ['read', 'write'],
        documents: ['read', 'write'],
        decisions: ['read', 'write'],
      },
      emailVerified: true,
      emailVerifiedAt: new Date(),
      avatar: '/assets/avatars/michael-underwriter.jpg',
      timezone: 'America/New_York',
      locale: 'en',
    },
  });

  const reviewerUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Jessica Reviewer',
      passwordHash,
      tenantId: demoTenant.id,
      role: 'REVIEWER',
      permissions: {
        applications: ['read'],
        documents: ['read', 'review'],
        quality: ['read', 'write'],
      },
      emailVerified: true,
      emailVerifiedAt: new Date(),
      avatar: '/assets/avatars/jessica-reviewer.jpg',
      timezone: 'America/New_York',
      locale: 'en',
    },
  });

  // Seed Plugins
  console.log('🔌 Seeding plugins...');
  const jumioPlugin = await prisma.plugin.upsert({
    where: { name: 'jumio-kyc' },
    update: {},
    create: {
      name: 'jumio-kyc',
      displayName: 'Jumio Identity Verification',
      description: 'AI-powered identity verification and KYC compliance',
      category: 'KYC',
      provider: 'Jumio',
      version: '2.0.0',
      configSchema: {
        type: 'object',
        properties: {
          apiKey: { type: 'string', description: 'Jumio API Key' },
          apiSecret: { type: 'string', description: 'Jumio API Secret' },
          datacenter: {
            type: 'string',
            enum: ['US', 'EU', 'SG'],
            default: 'US',
          },
          webhookUrl: { type: 'string', format: 'uri' },
        },
        required: ['apiKey', 'apiSecret'],
      },
      isActive: true,
      isMockable: true,
      baseUrl: 'https://netverify.com/api',
      authType: 'BASIC_AUTH',
    },
  });

  const experianPlugin = await prisma.plugin.upsert({
    where: { name: 'experian-credit' },
    update: {},
    create: {
      name: 'experian-credit',
      displayName: 'Experian Credit Reports',
      description: 'Business and personal credit reports and scores',
      category: 'CREDIT_BUREAU',
      provider: 'Experian',
      version: '1.5.0',
      configSchema: {
        type: 'object',
        properties: {
          clientId: { type: 'string', description: 'Experian Client ID' },
          clientSecret: {
            type: 'string',
            description: 'Experian Client Secret',
          },
          environment: {
            type: 'string',
            enum: ['sandbox', 'production'],
            default: 'sandbox',
          },
          webhookUrl: { type: 'string', format: 'uri' },
        },
        required: ['clientId', 'clientSecret'],
      },
      isActive: true,
      isMockable: true,
      baseUrl: 'https://api.experian.com/businessinformation',
      authType: 'OAUTH2',
    },
  });

  const plaidPlugin = await prisma.plugin.upsert({
    where: { name: 'plaid-banking' },
    update: {},
    create: {
      name: 'plaid-banking',
      displayName: 'Plaid Bank Verification',
      description: 'Bank account verification and financial data',
      category: 'BANK_VERIFICATION',
      provider: 'Plaid',
      version: '9.1.0',
      configSchema: {
        type: 'object',
        properties: {
          clientId: { type: 'string', description: 'Plaid Client ID' },
          secret: { type: 'string', description: 'Plaid Secret' },
          environment: {
            type: 'string',
            enum: ['sandbox', 'development', 'production'],
            default: 'sandbox',
          },
          webhookUrl: { type: 'string', format: 'uri' },
        },
        required: ['clientId', 'secret'],
      },
      isActive: true,
      isMockable: true,
      baseUrl: 'https://production.plaid.com',
      authType: 'CUSTOM',
    },
  });

  // Configure Tenant Plugins
  console.log('⚙️ Configuring tenant plugins...');
  await prisma.tenantPlugin.upsert({
    where: {
      tenantId_pluginId: {
        tenantId: demoTenant.id,
        pluginId: jumioPlugin.id,
      },
    },
    update: {},
    create: {
      tenantId: demoTenant.id,
      pluginId: jumioPlugin.id,
      config: {
        apiKey: process.env.DEMO_JUMIO_KEY || 'demo_jumio_key',
        apiSecret: process.env.DEMO_JUMIO_SECRET || 'demo_jumio_secret',
        datacenter: 'US',
        webhookUrl: 'https://demo.underwriting.ai/webhooks/jumio',
      },
      isEnabled: true,
      useMock: true,
      usageLimit: 1000,
      currentUsage: 234,
    },
  });

  await prisma.tenantPlugin.upsert({
    where: {
      tenantId_pluginId: {
        tenantId: demoTenant.id,
        pluginId: experianPlugin.id,
      },
    },
    update: {},
    create: {
      tenantId: demoTenant.id,
      pluginId: experianPlugin.id,
      config: {
        clientId: process.env.DEMO_EXPERIAN_CLIENT || 'demo_experian_client',
        clientSecret:
          process.env.DEMO_EXPERIAN_SECRET || 'demo_experian_secret',
        environment: 'sandbox',
        webhookUrl: 'https://demo.underwriting.ai/webhooks/experian',
      },
      isEnabled: true,
      useMock: true,
      usageLimit: 500,
      currentUsage: 89,
    },
  });

  await prisma.tenantPlugin.upsert({
    where: {
      tenantId_pluginId: {
        tenantId: demoTenant.id,
        pluginId: plaidPlugin.id,
      },
    },
    update: {},
    create: {
      tenantId: demoTenant.id,
      pluginId: plaidPlugin.id,
      config: {
        clientId: process.env.DEMO_PLAID_CLIENT || 'demo_plaid_client',
        secret: process.env.DEMO_PLAID_SECRET || 'demo_plaid_secret',
        environment: 'sandbox',
        webhookUrl: 'https://demo.underwriting.ai/webhooks/plaid',
      },
      isEnabled: true,
      useMock: false, // Using live sandbox
      usageLimit: 2000,
      currentUsage: 456,
    },
  });

  // Seed Sample Applications
  console.log('📄 Seeding sample applications...');
  const application1 = await prisma.application.create({
    data: {
      applicationNumber: 'APP-2024-001',
      tenantId: demoTenant.id,
      status: 'APPROVED',
      stage: 'COMPLETED',
      priority: 'NORMAL',
      businessName: 'TechStart Solutions LLC',
      businessType: 'LLC',
      industry: 'TECHNOLOGY',
      businessEmail: '<EMAIL>',
      businessPhone: '******-0123',
      businessWebsite: 'https://techstart.com',
      monthlyRevenue: 85000.0,
      monthlyTransactions: 450,
      averageTicket: 188.89,
      requestedAmount: 250000.0,
      businessAddress: {
        street: '123 Innovation Drive',
        city: 'San Francisco',
        state: 'CA',
        zip: '94105',
        country: 'US',
      },
      riskScore: 0.75,
      riskLevel: 'MEDIUM',
      finalDecision: 'APPROVED',
      decisionReason: 'Strong financials and good credit history',
      decisionMadeAt: new Date('2024-01-15T10:30:00Z'),
      decisionMadeBy: underwriterUser.id,
      assignedTo: underwriterUser.id,
      source: 'website',
      submittedAt: new Date('2024-01-10T14:22:00Z'),
      completedAt: new Date('2024-01-15T10:30:00Z'),
      metadata: {
        leadSource: 'organic_search',
        campaignId: 'tech-startups-q1',
        referrer: 'google.com',
      },
    },
  });

  const application2 = await prisma.application.create({
    data: {
      applicationNumber: 'APP-2024-002',
      tenantId: demoTenant.id,
      status: 'IN_REVIEW',
      stage: 'RISK_ANALYSIS',
      priority: 'HIGH',
      businessName: 'Coastal Restaurant Group',
      businessType: 'CORPORATION',
      industry: 'RESTAURANT',
      businessEmail: '<EMAIL>',
      businessPhone: '******-0456',
      businessWebsite: 'https://coastalrestaurants.com',
      monthlyRevenue: 320000.0,
      monthlyTransactions: 12500,
      averageTicket: 25.6,
      requestedAmount: 500000.0,
      businessAddress: {
        street: '456 Ocean Boulevard',
        city: 'Miami Beach',
        state: 'FL',
        zip: '33139',
        country: 'US',
      },
      riskScore: 0.45,
      riskLevel: 'LOW',
      assignedTo: underwriterUser.id,
      source: 'partner_referral',
      referralCode: 'PARTNER-MIA-001',
      submittedAt: new Date('2024-01-18T09:15:00Z'),
      metadata: {
        leadSource: 'partner_referral',
        partnerName: 'Miami Business Network',
        urgentProcessing: true,
      },
    },
  });

  // Seed Conversations
  console.log('💬 Seeding conversations...');
  await prisma.conversation.create({
    data: {
      applicationId: application1.id,
      tenantId: demoTenant.id,
      title: 'Application Review Discussion',
      language: 'en',
      interface: 'CHAT',
      aiProvider: 'openrouter',
      aiModel: 'anthropic/claude-3-sonnet',
      systemPrompt:
        'You are an expert underwriting assistant helping to review business loan applications.',
      status: 'COMPLETED',
      context: {
        applicationStage: 'RISK_ANALYSIS',
        previousDecisions: [],
        riskFactors: ['credit_score', 'cash_flow', 'industry_stability'],
      },
      summary:
        'Reviewed financial documents and credit history. Recommended approval with standard terms.',
      totalTokens: 3420,
      totalCost: 0.0855,
      sessionId: 'sess_app1_conv1',
      lastActiveAt: new Date('2024-01-14T16:45:00Z'),
      completedAt: new Date('2024-01-14T16:45:00Z'),
    },
  });

  // Seed Risk Assessments
  console.log('🎯 Seeding risk assessments...');
  await prisma.riskAssessment.create({
    data: {
      applicationId: application1.id,
      version: '2.1.0',
      modelName: 'underwriting-ai-v2',
      overallScore: 0.75,
      riskLevel: 'MEDIUM',
      creditScore: 0.82,
      businessScore: 0.71,
      documentScore: 0.88,
      behaviorScore: 0.65,
      recommendation: 'APPROVED',
      confidence: 0.89,
      reasoning:
        'Strong credit profile with consistent revenue growth. Minor concerns about industry volatility offset by strong management team.',
      factors: {
        positive: [
          { name: 'Credit Score', weight: 0.25, value: 748 },
          { name: 'Revenue Growth', weight: 0.2, value: '15% YoY' },
          { name: 'Cash Flow', weight: 0.2, value: 'Positive' },
        ],
        negative: [
          { name: 'Industry Risk', weight: 0.15, value: 'Medium' },
          { name: 'Debt-to-Income', weight: 0.1, value: '0.45' },
        ],
      },
      features: {
        monthlyRevenue: 85000,
        debtToIncome: 0.45,
        yearsInBusiness: 4,
        creditScore: 748,
        industryRiskScore: 0.35,
      },
      humanReviewed: true,
      reviewedBy: underwriterUser.id,
      reviewedAt: new Date('2024-01-14T15:30:00Z'),
    },
  });

  // Seed ML Models
  console.log('🤖 Seeding ML models...');
  await prisma.mlModel.create({
    data: {
      name: 'underwriting-ai-v2',
      version: '2.1.0',
      modelType: 'gradient_boosting',
      configuration: {
        algorithm: 'XGBoost',
        features: 47,
        treeDepth: 8,
        learningRate: 0.1,
        regularization: 0.01,
        trainingDataSize: 50000,
      },
      accuracy: 0.89,
      precision: 0.87,
      recall: 0.91,
      performanceMetrics: {
        f1Score: 0.89,
        auc: 0.94,
        specificity: 0.85,
        negativePredictiveValue: 0.92,
      },
      isActive: true,
      trainedAt: new Date('2024-01-01T00:00:00Z'),
      deployedAt: new Date('2024-01-05T00:00:00Z'),
    },
  });

  // Seed Workflow Definitions
  console.log('⚡ Seeding workflow definitions...');
  await prisma.workflowDefinition.create({
    data: {
      tenantId: demoTenant.id,
      name: 'Standard Application Review',
      description:
        'Standard workflow for processing business loan applications',
      steps: {
        document_collection: {
          name: 'Document Collection',
          type: 'automated',
          timeout: 3600,
          requirements: ['business_license', 'bank_statements', 'tax_returns'],
        },
        kyc_verification: {
          name: 'KYC Verification',
          type: 'external_api',
          provider: 'jumio-kyc',
          timeout: 300,
        },
        credit_check: {
          name: 'Credit Check',
          type: 'external_api',
          provider: 'experian-credit',
          timeout: 600,
        },
        risk_assessment: {
          name: 'AI Risk Assessment',
          type: 'ml_model',
          model: 'underwriting-ai-v2',
          timeout: 120,
        },
        manual_review: {
          name: 'Manual Review',
          type: 'human_task',
          assignedRole: 'UNDERWRITER',
          timeout: 86400,
        },
        final_decision: {
          name: 'Final Decision',
          type: 'decision_gate',
          timeout: 300,
        },
      },
      triggers: {
        start: ['application_submitted'],
        restart: ['additional_documents_uploaded'],
      },
      conditions: {
        skip_manual_review: 'risk_score < 0.3 AND credit_score > 700',
        require_manager_approval:
          'risk_score > 0.8 OR requested_amount > 1000000',
      },
      isActive: true,
      version: 1,
    },
  });

  // Seed System Settings
  console.log('⚙️ Seeding system settings...');
  await prisma.systemSetting.createMany({
    data: [
      {
        key: 'ai.default_provider',
        value: { provider: 'openrouter', fallback: 'openai' },
        description: 'Default AI provider for conversations',
        category: 'ai',
        isPublic: false,
      },
      {
        key: 'risk.default_thresholds',
        value: {
          low: 0.3,
          medium: 0.7,
          high: 0.9,
          auto_approve_threshold: 0.2,
          auto_reject_threshold: 0.95,
        },
        description: 'Default risk score thresholds',
        category: 'risk',
        isPublic: false,
      },
      {
        key: 'documents.max_file_size',
        value: { bytes: 50000000, display: '50MB' },
        description: 'Maximum file size for document uploads',
        category: 'documents',
        isPublic: true,
      },
      {
        key: 'notifications.email_templates',
        value: {
          application_approved: 'email_approved_template',
          application_rejected: 'email_rejected_template',
          documents_required: 'email_documents_required_template',
        },
        description: 'Email template mappings',
        category: 'notifications',
        isPublic: false,
      },
    ],
  });

  // Seed System Users
  console.log('👤 Seeding system users...');
  const systemPassword = process.env.SEED_SYSTEM_PASSWORD || 'system123!';
  const systemPasswordHash = await bcrypt.hash(systemPassword, 12);

  const superAdminUser = await prisma.systemUser.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Platform Super Admin',
      passwordHash: systemPasswordHash,
      role: 'SUPER_ADMIN',
      permissions: {
        tenants: ['read', 'write', 'delete'],
        billing: ['read', 'write', 'delete'],
        analytics: ['read'],
        systemSettings: ['read', 'write'],
        users: ['read', 'write', 'delete'],
      },
      emailVerified: true,
      emailVerifiedAt: new Date(),
      timezone: 'America/New_York',
      locale: 'en',
      isActive: true,
    },
  });

  const financeAdminUser = await prisma.systemUser.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Finance Administrator',
      passwordHash: systemPasswordHash,
      role: 'FINANCE_ADMIN',
      permissions: {
        billing: ['read', 'write'],
        invoices: ['read', 'write'],
        payments: ['read', 'write'],
        analytics: ['read'],
        tenants: ['read'],
      },
      emailVerified: true,
      emailVerifiedAt: new Date(),
      timezone: 'America/New_York',
      locale: 'en',
      isActive: true,
    },
  });

  // Seed Subscription for Demo Tenant
  console.log('💳 Seeding billing data...');
  const currentDate = new Date();
  const periodStart = new Date(
    currentDate.getFullYear(),
    currentDate.getMonth(),
    1
  );
  const periodEnd = new Date(
    currentDate.getFullYear(),
    currentDate.getMonth() + 1,
    0
  );
  const nextBilling = new Date(
    currentDate.getFullYear(),
    currentDate.getMonth() + 1,
    1
  );

  const demoSubscription = await prisma.subscription.create({
    data: {
      tenantId: demoTenant.id,
      tier: 'PROFESSIONAL',
      status: 'ACTIVE',
      billingCycle: 'MONTHLY',
      monthlyFee: 999.0,
      setupFee: 3000.0,
      includedUsage: 300,
      overageRate: 6.0,
      currentPeriodStart: periodStart,
      currentPeriodEnd: periodEnd,
      nextBillingDate: nextBilling,
    },
  });

  // Seed Payment Method
  const demoPaymentMethod = await prisma.paymentMethod.create({
    data: {
      tenantId: demoTenant.id,
      type: 'CARD',
      provider: 'stripe',
      providerMethodId: 'pm_demo_card_visa',
      lastFour: '4242',
      brand: 'visa',
      expiryMonth: 12,
      expiryYear: 2027,
      isDefault: true,
      isActive: true,
      isVerified: true,
      verifiedAt: new Date(),
      billingAddress: {
        street: '123 Innovation Drive',
        city: 'San Francisco',
        state: 'CA',
        zip: '94105',
        country: 'US',
      },
    },
  });

  // Seed Usage Events for Current Period
  const usageEvents = [
    { eventType: 'APPLICATION_PROCESSING', quantity: 1, cost: 6.5 },
    { eventType: 'APPLICATION_PROCESSING', quantity: 1, cost: 6.5 },
    { eventType: 'APPLICATION_PROCESSING', quantity: 1, cost: 6.5 },
    { eventType: 'API_CALL', quantity: 15, cost: 0.1 },
    { eventType: 'PREMIUM_FEATURE', quantity: 1, cost: 25.0 },
  ];

  await prisma.usageEvent.createMany({
    data: usageEvents.map((event, index) => ({
      tenantId: demoTenant.id,
      applicationId: index < 2 ? application1.id : application2.id,
      eventType: event.eventType,
      quantity: event.quantity,
      unitCost: event.cost,
      totalCost: event.quantity * event.cost,
      description: `${event.eventType.toLowerCase().replace('_', ' ')} event`,
      billingPeriod: `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`,
      timestamp: new Date(
        currentDate.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000
      ), // Random time in last 30 days
    })),
  });

  // Seed Usage Aggregation
  const totalUsage = 185; // Applications processed this period
  const overageUsage = Math.max(0, totalUsage - demoSubscription.includedUsage);
  const overageFee =
    overageUsage * parseFloat(demoSubscription.overageRate.toString());

  const usageAggregation = await prisma.usageAggregation.create({
    data: {
      tenantId: demoTenant.id,
      subscriptionId: demoSubscription.id,
      billingPeriod: `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`,
      periodStart: periodStart,
      periodEnd: periodEnd,
      totalUsage: totalUsage,
      includedUsage: demoSubscription.includedUsage,
      overageUsage: overageUsage,
      subscriptionFee: parseFloat(demoSubscription.monthlyFee.toString()),
      overageFee: overageFee,
      additionalFees: 0,
      totalCost:
        parseFloat(demoSubscription.monthlyFee.toString()) + overageFee,
      isFinalized: false,
    },
  });

  // Seed Previous Month's Invoice (Paid)
  const lastMonth = new Date(
    currentDate.getFullYear(),
    currentDate.getMonth() - 1,
    1
  );
  const lastMonthEnd = new Date(
    currentDate.getFullYear(),
    currentDate.getMonth(),
    0
  );
  const lastMonthTotal = 999.0 + 50 * 6.0; // Base fee + 50 overage applications

  const previousInvoice = await prisma.invoice.create({
    data: {
      tenantId: demoTenant.id,
      subscriptionId: demoSubscription.id,
      invoiceNumber: `INV-${currentDate.getFullYear()}-${String(currentDate.getMonth()).padStart(2, '0')}-001`,
      status: 'PAID',
      billingPeriod: `${lastMonth.getFullYear()}-${String(lastMonth.getMonth() + 1).padStart(2, '0')}`,
      periodStart: lastMonth,
      periodEnd: lastMonthEnd,
      subtotal: lastMonthTotal,
      taxRate: 0.0875, // 8.75% tax
      taxAmount: lastMonthTotal * 0.0875,
      discountAmount: 0,
      total: lastMonthTotal + lastMonthTotal * 0.0875,
      dueDate: new Date(lastMonth.getTime() + 30 * 24 * 60 * 60 * 1000), // 30 days
      paidAt: new Date(lastMonth.getTime() + 15 * 24 * 60 * 60 * 1000), // Paid in 15 days
      generatedAt: lastMonth,
      sentAt: lastMonth,
    },
  });

  // Seed Invoice Line Items
  await prisma.invoiceLineItem.createMany({
    data: [
      {
        invoiceId: previousInvoice.id,
        description: 'Professional Plan - Monthly Subscription',
        quantity: 1,
        unitPrice: 999.0,
        totalPrice: 999.0,
        itemType: 'SUBSCRIPTION',
      },
      {
        invoiceId: previousInvoice.id,
        description: 'Application Processing Overage (50 applications)',
        quantity: 50,
        unitPrice: 6.0,
        totalPrice: 300.0,
        itemType: 'USAGE_OVERAGE',
      },
    ],
  });

  // Seed Successful Payment
  const successfulPayment = await prisma.payment.create({
    data: {
      invoiceId: previousInvoice.id,
      paymentMethodId: demoPaymentMethod.id,
      amount: parseFloat(previousInvoice.total.toString()),
      currency: 'USD',
      status: 'SUCCEEDED',
      provider: 'stripe',
      providerPaymentId: 'pi_demo_payment_successful',
      providerFee: 2.9 + parseFloat(previousInvoice.total.toString()) * 0.029, // Stripe fees
      processedAt: new Date(lastMonth.getTime() + 15 * 24 * 60 * 60 * 1000),
      settledAt: new Date(lastMonth.getTime() + 16 * 24 * 60 * 60 * 1000),
      retryCount: 0,
      maxRetries: 3,
    },
  });

  // Seed Billing Analytics
  await prisma.billingAnalytics.create({
    data: {
      period: `${lastMonth.getFullYear()}-${String(lastMonth.getMonth() + 1).padStart(2, '0')}`,
      periodType: 'monthly',
      totalRevenue: lastMonthTotal + lastMonthTotal * 0.0875,
      subscriptionRevenue: 999.0,
      usageRevenue: 300.0,
      setupFeeRevenue: 0,
      totalTenants: 1,
      newTenants: 1,
      churnedTenants: 0,
      starterTenants: 0,
      professionalTenants: 1,
      enterpriseTenants: 0,
      successfulPayments: 1,
      failedPayments: 0,
      retryPayments: 0,
      totalApplications: 350,
      averageUsagePerTenant: 350.0,
    },
  });

  // Seed Billing Notifications
  await prisma.billingNotification.createMany({
    data: [
      {
        tenantId: demoTenant.id,
        type: 'INVOICE_GENERATED',
        title: 'New Invoice Available',
        message: `Your invoice ${previousInvoice.invoiceNumber} for $${previousInvoice.total} is ready for payment.`,
        deliveryMethod: 'EMAIL',
        recipientEmail: '<EMAIL>',
        status: 'SENT',
        scheduledFor: lastMonth,
        sentAt: lastMonth,
        invoiceId: previousInvoice.id,
      },
      {
        tenantId: demoTenant.id,
        type: 'PAYMENT_SUCCEEDED',
        title: 'Payment Processed Successfully',
        message: `Your payment of $${previousInvoice.total} has been processed successfully.`,
        deliveryMethod: 'EMAIL',
        recipientEmail: '<EMAIL>',
        status: 'SENT',
        scheduledFor: new Date(lastMonth.getTime() + 15 * 24 * 60 * 60 * 1000),
        sentAt: new Date(lastMonth.getTime() + 15 * 24 * 60 * 60 * 1000),
        invoiceId: previousInvoice.id,
        paymentId: successfulPayment.id,
      },
    ],
  });

  // Seed Billing Jobs (for demonstration)
  await prisma.billingJob.createMany({
    data: [
      {
        jobType: 'INVOICE_GENERATION',
        status: 'COMPLETED',
        scheduledFor: lastMonth,
        startedAt: lastMonth,
        completedAt: new Date(lastMonth.getTime() + 5 * 60 * 1000), // 5 minutes later
        parameters: {
          billingPeriod: `${lastMonth.getFullYear()}-${String(lastMonth.getMonth() + 1).padStart(2, '0')}`,
        },
        successCount: 1,
        errorCount: 0,
        retryCount: 0,
        maxRetries: 3,
      },
      {
        jobType: 'PAYMENT_PROCESSING',
        status: 'COMPLETED',
        scheduledFor: new Date(lastMonth.getTime() + 30 * 24 * 60 * 60 * 1000),
        startedAt: new Date(lastMonth.getTime() + 30 * 24 * 60 * 60 * 1000),
        completedAt: new Date(
          lastMonth.getTime() + 30 * 24 * 60 * 60 * 1000 + 2 * 60 * 1000
        ),
        parameters: {
          dueDate: new Date(
            lastMonth.getTime() + 30 * 24 * 60 * 60 * 1000
          ).toISOString(),
        },
        successCount: 1,
        errorCount: 0,
        retryCount: 0,
        maxRetries: 3,
      },
      {
        jobType: 'USAGE_AGGREGATION',
        status: 'COMPLETED',
        scheduledFor: lastMonthEnd,
        startedAt: lastMonthEnd,
        completedAt: new Date(lastMonthEnd.getTime() + 30 * 1000), // 30 seconds later
        parameters: {
          billingPeriod: `${lastMonth.getFullYear()}-${String(lastMonth.getMonth() + 1).padStart(2, '0')}`,
        },
        successCount: 1,
        errorCount: 0,
        retryCount: 0,
        maxRetries: 2,
      },
    ],
  });

  console.log('✅ Database seeding completed successfully!');
  console.log('📊 Seeded data summary:');
  console.log(`   • 1 tenant: ${demoTenant.name}`);
  console.log(`   • 3 tenant users: Admin, Underwriter, Reviewer`);
  console.log(`   • 2 system users: Super Admin, Finance Admin`);
  console.log(`   • 3 plugins: Jumio, Experian, Plaid`);
  console.log(`   • 2 sample applications`);
  console.log(`   • 1 conversation thread`);
  console.log(`   • 1 risk assessment`);
  console.log(`   • 1 ML model`);
  console.log(`   • 1 workflow definition`);
  console.log(`   • 4 system settings`);
  console.log(`   • 1 subscription (Professional tier)`);
  console.log(`   • 1 payment method (Visa ending in 4242)`);
  console.log(`   • 5 usage events (current period)`);
  console.log(`   • 1 usage aggregation (185 applications processed)`);
  console.log(`   • 1 paid invoice ($${previousInvoice.total})`);
  console.log(`   • 1 successful payment`);
  console.log(`   • 1 billing analytics record`);
  console.log(`   • 2 billing notifications`);
  console.log(`   • 3 billing jobs (completed)`);
  console.log('');
  console.log('🎯 Ready for BMAD agent implementation!');
  console.log('💳 Billing system foundation complete with sample data');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error('❌ Error during seeding:', e);
    await prisma.$disconnect();
    process.exit(1);
  });
