// AI-Powered Merchant Underwriting Platform - Database Schema
// BMad Database Architect: Alex - Comprehensive multi-tenant underwriting schema
// learn more about Prisma: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

// generator prismaJson {
//   provider = "prisma-json-schema-generator"
//   output   = "./json-schema"
// }

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ============================================================================
// MULTI-TENANCY & ORGANIZATION
// ============================================================================

model Tenant {
  id               String           @id @default(cuid())
  name             String
  slug             String           @unique
  domain           String?          @unique
  status           TenantStatus     @default(ACTIVE)
  subscriptionTier SubscriptionTier @default(STARTER)

  // White-label branding
  branding     Json? // Logo, colors, themes, custom CSS
  customDomain String?

  // AI & Usage Configuration
  aiQuotaMonthly      Int     @default(10000) // Token quota per month
  aiUsageCurrentMonth Int     @default(0)
  hitlRulesEnabled    Boolean @default(true)

  // Features & Settings
  features Json? // Feature flags and tenant-specific configs
  settings Json? // Dynamic configuration settings

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  users         User[]
  applications  Application[]
  conversations Conversation[]
  plugins       TenantPlugin[]
  auditLogs     AuditLog[]

  // Enhanced relationships for new models
  workflowDefinitions   WorkflowDefinition[]
  notificationTemplates NotificationTemplate[]
  providerPerformance   ProviderPerformance[]
  tenantMetrics         TenantMetrics[]
  dataSubjectRequests   DataSubjectRequest[]
  retentionPolicies     RetentionPolicy[]

  // Billing relationships
  subscriptions     Subscription[]
  invoices          Invoice[]
  paymentMethods    PaymentMethod[]
  usageEvents       UsageEvent[]
  usageAggregations UsageAggregation[]

  // AI Model relationships
  tenantModelConfigs TenantModelConfiguration[]

  // Decision Pipeline relationships
  decisionPipelines  DecisionPipeline[]
  pipelineExecutions PipelineExecution[]
  pipelineMetrics    PipelineMetrics[]

  @@map("tenants")
}

enum TenantStatus {
  ACTIVE
  SUSPENDED
  TRIAL
  CHURNED
}

enum SubscriptionTier {
  STARTER
  PROFESSIONAL
  ENTERPRISE
  CUSTOM
}

// ============================================================================
// USER MANAGEMENT & AUTHENTICATION
// ============================================================================

model User {
  id           String  @id @default(cuid())
  email        String  @unique
  name         String?
  passwordHash String?

  // Multi-tenant association
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Role & Permissions
  role        UserRole @default(USER)
  permissions Json? // Granular permissions array

  // Authentication
  emailVerified   Boolean   @default(false)
  emailVerifiedAt DateTime?
  mfaEnabled      Boolean   @default(false)
  mfaSecret       String?

  // Profile
  avatar      String?
  timezone    String?   @default("UTC")
  locale      String?   @default("en")
  lastLoginAt DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  sessions      UserSession[]
  applications  Application[]
  messages      ConversationMessage[]
  assignedTasks Task[]
  auditLogs     AuditLog[]

  // Enhanced relationships
  communicationPreferences CommunicationPreference[]

  @@unique([email, tenantId])
  @@map("users")
}

enum UserRole {
  USER
  ADMIN
  UNDERWRITER
  REVIEWER
  MANAGER
  VIEWER
  // System-level roles for platform administration
  SYSTEM_ADMIN
  SYSTEM_FINANCE
  SYSTEM_SUPPORT
  SYSTEM_ANALYTICS
}

model UserSession {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  sessionToken String  @unique
  refreshToken String? @unique
  device       String?
  ipAddress    String?
  userAgent    String?

  expiresAt    DateTime
  lastActiveAt DateTime @default(now())
  createdAt    DateTime @default(now())

  @@map("user_sessions")
}

// ============================================================================
// CORE BUSINESS ENTITIES - APPLICATIONS
// ============================================================================

model Application {
  id                String @id @default(cuid())
  applicationNumber String @unique // Human-readable app number

  // Multi-tenant
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Application Lifecycle
  status   ApplicationStatus @default(DRAFT)
  stage    ApplicationStage  @default(DATA_COLLECTION)
  priority Priority          @default(NORMAL)

  // Merchant Information
  businessName    String
  businessType    BusinessType
  industry        Industry
  businessEmail   String
  businessPhone   String?
  businessWebsite String?

  // Financial Information
  monthlyRevenue      Decimal? @db.Decimal(15, 2)
  monthlyTransactions Int?
  averageTicket       Decimal? @db.Decimal(10, 2)
  requestedAmount     Decimal? @db.Decimal(15, 2)

  // Address Information
  businessAddress Json // Street, city, state, zip, country
  mailingAddress  Json? // If different from business

  // Risk & Decision
  riskScore      Float?     @db.Real
  riskLevel      RiskLevel?
  finalDecision  Decision?
  decisionReason String?
  decisionMadeAt DateTime?
  decisionMadeBy String? // User ID

  // Assignment & Workflow
  assignedTo   String?
  assignedUser User?   @relation(fields: [assignedTo], references: [id])

  // Metadata
  source       String? // Source of application
  referralCode String?
  metadata     Json? // Additional flexible data

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  submittedAt DateTime?
  completedAt DateTime?

  // Relationships
  applicationData   ApplicationData[]
  documents         Document[]
  conversations     Conversation[]
  riskAssessments   RiskAssessment[]
  statusHistory     ApplicationStatusHistory[]
  kycVerifications  KycVerification[]
  creditReports     CreditReport[]
  bankVerifications BankVerification[]
  tasks             Task[]

  // Enhanced relationships
  workflowExecutions   WorkflowExecution[]
  shadowModeExecutions ShadowModeExecution[]
  applicationMetrics   ApplicationMetrics?

  // Decision Pipeline relationships
  pipelineExecutions PipelineExecution[]

  @@index([tenantId, status])
  @@index([tenantId, businessName])
  @@index([applicationNumber])
  @@map("applications")
}

enum ApplicationStatus {
  DRAFT
  SUBMITTED
  IN_REVIEW
  PENDING_DOCUMENTS
  UNDER_VERIFICATION
  RISK_ASSESSMENT
  MANUAL_REVIEW
  APPROVED
  REJECTED
  WITHDRAWN
  EXPIRED
}

enum ApplicationStage {
  DATA_COLLECTION
  DOCUMENT_UPLOAD
  VERIFICATION
  RISK_ANALYSIS
  DECISION
  COMPLETED
}

enum BusinessType {
  SOLE_PROPRIETORSHIP
  PARTNERSHIP
  LLC
  CORPORATION
  NON_PROFIT
  OTHER
}

enum Industry {
  RETAIL
  RESTAURANT
  E_COMMERCE
  PROFESSIONAL_SERVICES
  HEALTHCARE
  TECHNOLOGY
  MANUFACTURING
  REAL_ESTATE
  AUTOMOTIVE
  ENTERTAINMENT
  EDUCATION
  NON_PROFIT
  OTHER
}

enum Priority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum RiskLevel {
  LOW
  MEDIUM
  HIGH
  VERY_HIGH
}

enum Decision {
  APPROVED
  REJECTED
  CONDITIONAL_APPROVAL
}

model ApplicationData {
  id            String      @id @default(cuid())
  applicationId String
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  fieldName  String // Dynamic field identifier
  fieldType  String // text, number, date, boolean, json
  value      Json // Flexible value storage
  source     DataSource @default(MANUAL)
  confidence Float?     @db.Real // AI confidence score if applicable

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([applicationId, fieldName])
  @@map("application_data")
}

enum DataSource {
  MANUAL
  AI_EXTRACTED
  API_PREFILL
  OCR_EXTRACTED
  IMPORTED
}

model ApplicationStatusHistory {
  id            String      @id @default(cuid())
  applicationId String
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  fromStatus ApplicationStatus?
  toStatus   ApplicationStatus
  reason     String?
  notes      String?

  changedBy String? // User ID
  changedAt DateTime @default(now())

  @@map("application_status_history")
}

// ============================================================================
// DOCUMENT MANAGEMENT
// ============================================================================

model Document {
  id            String      @id @default(cuid())
  applicationId String
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  // File Information
  filename     String
  originalName String
  fileSize     Int
  mimeType     String
  fileHash     String // SHA-256 for integrity
  storagePath  String

  // Document Classification
  documentType DocumentType
  category     DocumentCategory

  // Processing Status
  status        DocumentStatus   @default(UPLOADED)
  ocrStatus     ProcessingStatus @default(PENDING)
  ocrText       String?
  ocrConfidence Float?           @db.Real

  // AI Analysis
  fraudScore    Float? @db.Real
  qualityScore  Float? @db.Real
  extractedData Json? // Structured data extracted from document

  // Review & Approval
  reviewStatus ReviewStatus @default(PENDING)
  reviewedBy   String? // User ID
  reviewedAt   DateTime?
  reviewNotes  String?

  // Metadata
  metadata Json?
  tags     String[]

  uploadedAt DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relationships
  annotations DocumentAnnotation[]
  versions    DocumentVersion[]

  @@index([applicationId, documentType])
  @@map("documents")
}

enum DocumentType {
  BUSINESS_LICENSE
  TAX_RETURN
  BANK_STATEMENT
  FINANCIAL_STATEMENT
  PROOF_OF_ADDRESS
  IDENTITY_DOCUMENT
  INSURANCE_CERTIFICATE
  LEASE_AGREEMENT
  OPERATING_AGREEMENT
  OTHER
}

enum DocumentCategory {
  IDENTITY
  FINANCIAL
  BUSINESS
  LEGAL
  SUPPORTING
}

enum DocumentStatus {
  UPLOADED
  PROCESSING
  PROCESSED
  FAILED
  REJECTED
}

enum ProcessingStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
}

enum ReviewStatus {
  PENDING
  APPROVED
  REJECTED
  NEEDS_CLARIFICATION
}

model DocumentAnnotation {
  id         String   @id @default(cuid())
  documentId String
  document   Document @relation(fields: [documentId], references: [id], onDelete: Cascade)

  userId String

  // Annotation Details
  type        AnnotationType
  content     String
  coordinates Json? // For image annotations (x, y, width, height)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("document_annotations")
}

enum AnnotationType {
  COMMENT
  HIGHLIGHT
  QUESTION
  CORRECTION
  APPROVAL
  REJECTION
}

// ============================================================================
// AI CONVERSATION & MODEL MANAGEMENT
// ============================================================================

model Conversation {
  id            String      @id @default(cuid())
  applicationId String
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Conversation Metadata
  title     String?
  language  String                @default("en")
  interface ConversationInterface @default(CHAT)

  // AI Configuration
  aiProvider   String  @default("openrouter")
  aiModel      String  @default("gpt-4")
  systemPrompt String?

  // State Management
  status  ConversationStatus @default(ACTIVE)
  context Json? // Conversation context and state
  summary String? // AI-generated conversation summary

  // Cost Tracking
  totalTokens Int     @default(0)
  totalCost   Decimal @default(0) @db.Decimal(10, 4)

  // Session Management
  sessionId    String?
  lastActiveAt DateTime @default(now())

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  completedAt DateTime?

  // Relationships
  messages ConversationMessage[]

  @@index([applicationId])
  @@index([tenantId, status])
  @@map("conversations")
}

enum ConversationInterface {
  CHAT
  FORM
  HYBRID
}

enum ConversationStatus {
  ACTIVE
  PAUSED
  COMPLETED
  ABANDONED
  ERROR
}

model ConversationMessage {
  id             String       @id @default(cuid())
  conversationId String
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  // Message Details
  role     MessageRole
  content  String
  metadata Json? // Additional message data

  // AI Model Information
  model      String?
  tokenCount Int?
  cost       Decimal? @db.Decimal(8, 4)

  // User Information (for human messages)
  userId String?
  user   User?   @relation(fields: [userId], references: [id])

  // Response Quality
  confidence   Float? @db.Real
  responseTime Int? // Milliseconds

  createdAt DateTime @default(now())

  @@index([conversationId, createdAt])
  @@map("conversation_messages")
}

enum MessageRole {
  USER
  ASSISTANT
  SYSTEM
  FUNCTION
}

model AiModelUsage {
  id       String @id @default(cuid())
  tenantId String

  // Usage Details
  provider String // openrouter, ollama, etc.
  model    String
  endpoint String?

  // Request/Response
  inputTokens  Int
  outputTokens Int
  totalTokens  Int
  cost         Decimal @db.Decimal(8, 4)

  // Context
  conversationId String?
  applicationId  String?
  userId         String?

  // Performance
  responseTime Int // Milliseconds
  successful   Boolean @default(true)
  errorMessage String?

  createdAt DateTime @default(now())

  @@index([tenantId, createdAt])
  @@index([provider, model])
  @@map("ai_model_usage")
}

// ============================================================================
// DECISION PIPELINE ENGINE
// ============================================================================

model DecisionPipeline {
  id String @id @default(cuid())

  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Pipeline Identity
  name        String // Human-readable pipeline name
  description String? // Pipeline description
  version     String  @default("1.0")

  // Pipeline Configuration
  stages Json // Array of pipeline stage configurations
  config Json // Pipeline-level configuration settings

  // State Management
  isActive  Boolean @default(true)
  isDefault Boolean @default(false)

  // Application Types
  applicableTypes String[] // Types of applications this pipeline handles

  // Metadata
  tags      String[] // Pipeline categorization tags
  createdBy String? // User who created the pipeline

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  executions PipelineExecution[]
  metrics    PipelineMetrics[]

  @@unique([tenantId, name, version])
  @@index([tenantId, isActive])
  @@index([applicableTypes])
  @@map("decision_pipelines")
}

model PipelineExecution {
  id String @id @default(cuid())

  pipelineId String
  pipeline   DecisionPipeline @relation(fields: [pipelineId], references: [id], onDelete: Cascade)

  applicationId String
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Execution Context
  version     String // Pipeline version used
  trigger     String  @default("manual") // manual, automatic, retry
  initiatedBy String? // User or system that triggered execution

  // State Management
  status       PipelineStatus @default(PENDING)
  currentStage String? // Current executing stage

  // Input/Output
  inputData  Json // Initial application data and context
  outputData Json? // Final pipeline results

  // Timing
  startedAt   DateTime  @default(now())
  completedAt DateTime?
  timeoutAt   DateTime?

  // Error Handling
  errorCode    String?
  errorMessage String?
  retryCount   Int     @default(0)
  maxRetries   Int     @default(3)

  // Cost & Performance
  totalCost     Decimal @default(0) @db.Decimal(10, 4)
  totalDuration Int? // Total execution time in milliseconds

  // Relationships
  stageExecutions PipelineStageExecution[]

  @@index([applicationId])
  @@index([pipelineId, status])
  @@index([tenantId, startedAt])
  @@index([status, startedAt])
  @@map("pipeline_executions")
}

model PipelineStageExecution {
  id String @id @default(cuid())

  executionId String
  execution   PipelineExecution @relation(fields: [executionId], references: [id], onDelete: Cascade)

  // Stage Identity
  stageName  String // Name of the executed stage
  stageType  String // Type of stage (validation, enrichment, ai_analysis, etc.)
  stageIndex Int // Order in pipeline (0-based)

  // State Management
  status StageStatus @default(PENDING)

  // Input/Output
  inputData  Json? // Data passed to this stage
  outputData Json? // Data produced by this stage

  // Timing
  startedAt   DateTime  @default(now())
  completedAt DateTime?
  duration    Int? // Execution time in milliseconds

  // Error Handling
  errorCode    String?
  errorMessage String?
  retryCount   Int     @default(0)

  // Cost Tracking
  cost Decimal @default(0) @db.Decimal(10, 4)

  // Stage Configuration
  config Json? // Stage-specific configuration used

  // Performance Metrics
  metrics Json? // Stage-specific performance data

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([executionId, stageIndex])
  @@index([stageName, status])
  @@map("pipeline_stage_executions")
}

model PipelineMetrics {
  id String @id @default(cuid())

  pipelineId String
  pipeline   DecisionPipeline @relation(fields: [pipelineId], references: [id], onDelete: Cascade)

  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Time Period
  periodStart DateTime
  periodEnd   DateTime
  granularity String   @default("daily") // hourly, daily, weekly, monthly

  // Execution Metrics
  totalExecutions      Int  @default(0)
  successfulExecutions Int  @default(0)
  failedExecutions     Int  @default(0)
  averageDuration      Int? // Average execution time in milliseconds

  // Performance Metrics
  averageCost Decimal @default(0) @db.Decimal(10, 4)
  totalCost   Decimal @default(0) @db.Decimal(10, 4)

  // Stage-Level Metrics
  stageMetrics Json // Per-stage performance breakdown

  // Error Analysis
  errorBreakdown Json? // Error types and frequencies

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([pipelineId, tenantId, periodStart, granularity])
  @@index([tenantId, periodStart])
  @@map("pipeline_metrics")
}

enum PipelineStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
  TIMEOUT
}

enum StageStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  SKIPPED
  TIMEOUT
}

// ============================================================================
// RISK ASSESSMENT & DECISION ENGINE
// ============================================================================

model RiskAssessment {
  id            String      @id @default(cuid())
  applicationId String
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  // Assessment Details
  version      String    @default("1.0")
  modelName    String // AI model used for assessment
  overallScore Float     @db.Real
  riskLevel    RiskLevel

  // Score Breakdown
  creditScore   Float? @db.Real
  businessScore Float? @db.Real
  documentScore Float? @db.Real
  behaviorScore Float? @db.Real

  // Decision Support
  recommendation Decision
  confidence     Float    @db.Real
  reasoning      String? // AI-generated explanation

  // Factors & Features
  factors  Json // Detailed factor analysis
  features Json // Feature importance scores

  // Validation
  humanReviewed Boolean   @default(false)
  reviewedBy    String? // User ID
  reviewedAt    DateTime?

  createdAt DateTime @default(now())

  // Relationships
  decisionFactors DecisionFactor[]

  @@index([applicationId])
  @@map("risk_assessments")
}

model DecisionFactor {
  id               String         @id @default(cuid())
  riskAssessmentId String
  riskAssessment   RiskAssessment @relation(fields: [riskAssessmentId], references: [id], onDelete: Cascade)

  factorName String
  factorType FactorType
  value      Json // Factor value (could be number, string, object)
  weight     Float        @db.Real
  impact     FactorImpact

  explanation String? // Human-readable explanation
  confidence  Float?  @db.Real

  @@map("decision_factors")
}

enum FactorType {
  FINANCIAL
  CREDIT
  BUSINESS
  DOCUMENT
  BEHAVIORAL
  EXTERNAL
}

enum FactorImpact {
  POSITIVE
  NEGATIVE
  NEUTRAL
}

// ============================================================================
// EXTERNAL INTEGRATIONS & VERIFICATIONS
// ============================================================================

model Plugin {
  id          String         @id @default(cuid())
  name        String         @unique
  displayName String
  description String?
  category    PluginCategory
  provider    String
  version     String

  // Configuration
  configSchema Json // JSON schema for configuration
  isActive     Boolean @default(true)
  isMockable   Boolean @default(true)

  // API Information
  baseUrl  String?
  authType AuthType @default(API_KEY)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  tenantPlugins       TenantPlugin[]
  providerPerformance ProviderPerformance[]

  @@map("plugins")
}

enum PluginCategory {
  KYC
  CREDIT_BUREAU
  BANK_VERIFICATION
  FRAUD_DETECTION
  DOCUMENT_OCR
  COMPLIANCE
  COMMUNICATION
  ANALYTICS
}

enum AuthType {
  API_KEY
  OAUTH2
  BASIC_AUTH
  CUSTOM
}

model TenantPlugin {
  id       String @id @default(cuid())
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  pluginId String
  plugin   Plugin @relation(fields: [pluginId], references: [id], onDelete: Cascade)

  // Configuration
  config    Json // Tenant-specific configuration
  isEnabled Boolean @default(true)
  useMock   Boolean @default(false)

  // Usage & Limits
  usageLimit   Int? // Monthly usage limit
  currentUsage Int  @default(0)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([tenantId, pluginId])
  @@map("tenant_plugins")
}

model ExternalProviderCall {
  id            String  @id @default(cuid())
  tenantId      String
  pluginId      String
  applicationId String?

  // Request Details
  endpoint       String
  method         String
  requestBody    Json?
  requestHeaders Json?

  // Response Details
  statusCode      Int
  responseBody    Json?
  responseHeaders Json?

  // Performance & Cost
  responseTime Int // Milliseconds
  cost         Decimal? @db.Decimal(8, 4)
  successful   Boolean
  errorMessage String?

  // Retry Logic
  retryCount Int @default(0)
  maxRetries Int @default(3)

  createdAt DateTime @default(now())

  @@index([tenantId, pluginId])
  @@index([applicationId])
  @@map("external_provider_calls")
}

// KYC Verification Results
model KycVerification {
  id            String      @id @default(cuid())
  applicationId String
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  provider       String // jumio, onfido, etc.
  verificationId String // Provider's verification ID

  // Identity Information
  documentType   String // passport, license, etc.
  documentNumber String?
  firstName      String?
  lastName       String?
  dateOfBirth    DateTime?
  address        Json?

  // Verification Results
  status        VerificationStatus
  overallResult VerificationResult
  faceMatch     VerificationResult?
  documentAuth  VerificationResult?

  // Scores & Details
  confidence Float? @db.Real
  fraudScore Float? @db.Real
  details    Json? // Provider-specific details

  // Processing
  webhookData Json? // Raw webhook data
  processedAt DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([applicationId])
  @@map("kyc_verifications")
}

enum VerificationStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
  REJECTED
}

enum VerificationResult {
  PASS
  FAIL
  REVIEW
  NOT_AVAILABLE
}

// Credit Bureau Reports
model CreditReport {
  id            String      @id @default(cuid())
  applicationId String
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  provider   String // experian, equifax, transunion
  reportId   String // Provider's report ID
  reportType CreditReportType

  // Credit Scores
  creditScore Int?
  scoreModel  String? // FICO, VantageScore, etc.
  scoreRange  String? // 300-850, etc.

  // Business Information
  businessName    String?
  businessAddress Json?
  yearsInBusiness Int?

  // Financial Summary
  totalCreditLines Int?
  totalCreditLimit Decimal? @db.Decimal(15, 2)
  totalBalance     Decimal? @db.Decimal(15, 2)
  utilizationRate  Float?   @db.Real

  // Payment History
  paymentHistory Json? // Detailed payment history
  delinquencies  Int?
  bankruptcies   Int?
  judgments      Int?
  liens          Int?

  // Raw Data
  rawReport Json // Complete report data

  generatedAt DateTime
  createdAt   DateTime @default(now())

  @@index([applicationId])
  @@map("credit_reports")
}

enum CreditReportType {
  PERSONAL
  BUSINESS
  MERGED
}

// Bank Account Verification
model BankVerification {
  id            String      @id @default(cuid())
  applicationId String
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  provider  String // plaid, yodlee, etc.
  accountId String // Provider's account ID

  // Account Information
  accountType   BankAccountType
  accountNumber String? // Masked
  routingNumber String?
  bankName      String?

  // Verification Results
  status            VerificationStatus
  balanceVerified   Boolean            @default(false)
  ownershipVerified Boolean            @default(false)

  // Balance Information
  currentBalance   Decimal? @db.Decimal(15, 2)
  availableBalance Decimal? @db.Decimal(15, 2)
  averageBalance   Decimal? @db.Decimal(15, 2)
  minimumBalance   Decimal? @db.Decimal(15, 2)

  // Transaction Analysis
  monthlyDeposits    Decimal? @db.Decimal(15, 2)
  monthlyWithdrawals Decimal? @db.Decimal(15, 2)
  transactionCount   Int?
  nsfCount           Int? // Non-sufficient funds

  // Analysis Period
  analysisStartDate DateTime?
  analysisEndDate   DateTime?

  // Raw Data
  rawData Json? // Transaction data, account details

  verifiedAt DateTime?
  createdAt  DateTime  @default(now())

  @@index([applicationId])
  @@map("bank_verifications")
}

enum BankAccountType {
  CHECKING
  SAVINGS
  BUSINESS_CHECKING
  BUSINESS_SAVINGS
  MONEY_MARKET
  OTHER
}

// ============================================================================
// WORKFLOW & TASK MANAGEMENT
// ============================================================================

model Task {
  id            String       @id @default(cuid())
  applicationId String?
  application   Application? @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  // Task Details
  title       String
  description String?
  type        TaskType
  priority    Priority   @default(NORMAL)
  status      TaskStatus @default(PENDING)

  // Assignment
  assignedTo   String?
  assignedUser User?     @relation(fields: [assignedTo], references: [id])
  assignedAt   DateTime?

  // Timing
  dueDate        DateTime?
  estimatedHours Float?    @db.Real
  actualHours    Float?    @db.Real

  // Workflow
  workflowStep String? // Step in automated workflow
  dependencies String[] // Task IDs this task depends on

  // Workflow Step Execution
  workflowStepExecutionId String?
  workflowStepExecution   WorkflowStepExecution? @relation("WorkflowStepTasks", fields: [workflowStepExecutionId], references: [id])

  // Completion
  completedBy String? // User ID
  completedAt DateTime?
  result      Json? // Task completion result
  notes       String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([assignedTo, status])
  @@index([applicationId, status])
  @@map("tasks")
}

enum TaskType {
  MANUAL_REVIEW
  DOCUMENT_REVIEW
  VERIFICATION_CALL
  DECISION_REVIEW
  DATA_ENTRY
  QUALITY_CHECK
  CUSTOMER_CONTACT
  SYSTEM_TASK
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  BLOCKED
  COMPLETED
  CANCELLED
  OVERDUE
}

// ============================================================================
// COMMUNICATION & MESSAGING
// ============================================================================

model Message {
  id            String  @id @default(cuid())
  applicationId String? // Optional - system-wide messages

  // Message Details
  subject     String?
  content     String
  messageType MessageType
  channel     MessageChannel

  // Participants
  senderId    String // User ID
  recipientId String? // User ID (null for broadcast)

  // Delivery
  status      MessageStatus @default(SENT)
  deliveredAt DateTime?
  readAt      DateTime?

  // Attachments
  attachments Json? // File attachments metadata

  // Threading
  threadId  String? // For message threading
  replyToId String? // For replies

  createdAt DateTime @default(now())

  @@index([senderId])
  @@index([recipientId])
  @@index([applicationId])
  @@map("messages")
}

enum MessageType {
  EMAIL
  SMS
  IN_APP
  SYSTEM_ALERT
}

enum MessageChannel {
  INTERNAL
  EXTERNAL
  SYSTEM
}

enum MessageStatus {
  DRAFT
  SENT
  DELIVERED
  READ
  FAILED
}

// ============================================================================
// ANALYTICS & REPORTING
// ============================================================================

model AnalyticsEvent {
  id       String @id @default(cuid())
  tenantId String

  // Event Details
  eventType String // user_login, application_submitted, etc.
  eventName String
  category  String?

  // Context
  userId        String?
  applicationId String?
  sessionId     String?

  // Event Data
  properties Json? // Event-specific properties

  // Request Context
  ipAddress String?
  userAgent String?
  referer   String?

  timestamp DateTime @default(now())

  @@index([tenantId, eventType])
  @@index([timestamp])
  @@map("analytics_events")
}

// ============================================================================
// COMPLIANCE & AUDIT
// ============================================================================

model AuditLog {
  id       String @id @default(cuid())
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Event Details
  action     String // CREATE, UPDATE, DELETE, LOGIN, etc.
  entityType String // Application, User, Document, etc.
  entityId   String? // ID of the affected entity

  // User Context
  userId    String?
  user      User?   @relation(fields: [userId], references: [id])
  ipAddress String?
  userAgent String?

  // Change Details
  oldValues Json? // Previous state
  newValues Json? // New state
  metadata  Json? // Additional context

  // Compliance
  reason    String? // Reason for change
  riskLevel AuditRiskLevel @default(LOW)

  timestamp DateTime @default(now())

  @@index([tenantId, timestamp])
  @@index([userId, timestamp])
  @@index([entityType, entityId])
  @@map("audit_logs")
}

enum AuditRiskLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

model ComplianceEvent {
  id       String @id @default(cuid())
  tenantId String

  // Compliance Details
  eventType   ComplianceEventType
  regulation  String // GDPR, PCI DSS, SOX, etc.
  description String

  // Risk & Impact
  riskLevel AuditRiskLevel
  impact    String?

  // Response
  status     ComplianceStatus @default(OPEN)
  assignedTo String? // User ID
  resolution String?
  resolvedAt DateTime?

  // Related Entities
  applicationId String?
  userId        String?
  documentId    String?

  // Metadata
  metadata Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([tenantId, eventType])
  @@index([status])
  @@map("compliance_events")
}

enum ComplianceEventType {
  DATA_ACCESS
  DATA_EXPORT
  DATA_DELETION
  CONSENT_GIVEN
  CONSENT_WITHDRAWN
  SECURITY_INCIDENT
  POLICY_VIOLATION
  AUDIT_FINDING
}

enum ComplianceStatus {
  OPEN
  INVESTIGATING
  RESOLVED
  CLOSED
}

// ============================================================================
// SYSTEM CONFIGURATION & SETTINGS
// ============================================================================

model SystemSetting {
  id          String  @id @default(cuid())
  key         String  @unique
  value       Json
  description String?
  category    String?
  isPublic    Boolean @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("system_settings")
}

// ============================================================================
// INDEXES & OPTIMIZATIONS
// ============================================================================

// Additional composite indexes for performance
// These are defined inline above with @@index() directives

// ============================================================================
// ADVANCED WORKFLOW AUTOMATION
// ============================================================================

model WorkflowDefinition {
  id       String @id @default(cuid())
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  name        String
  description String?
  steps       Json // Workflow steps definition
  triggers    Json // Event triggers
  conditions  Json? // Execution conditions
  isActive    Boolean @default(true)
  version     Int     @default(1)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  executions WorkflowExecution[]

  @@map("workflow_definitions")
}

model WorkflowExecution {
  id                   String             @id @default(cuid())
  workflowDefinitionId String
  workflowDefinition   WorkflowDefinition @relation(fields: [workflowDefinitionId], references: [id], onDelete: Cascade)
  applicationId        String?
  application          Application?       @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  status  WorkflowStatus @default(PENDING)
  context Json? // Execution context

  startedAt   DateTime?
  completedAt DateTime?
  createdAt   DateTime  @default(now())

  // Relationships
  stepExecutions WorkflowStepExecution[]

  @@map("workflow_executions")
}

enum WorkflowStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
}

model WorkflowStepExecution {
  id                  String            @id @default(cuid())
  workflowExecutionId String
  workflowExecution   WorkflowExecution @relation(fields: [workflowExecutionId], references: [id], onDelete: Cascade)

  stepName     String
  status       StepStatus @default(PENDING)
  inputData    Json?
  outputData   Json?
  errorMessage String?

  startedAt   DateTime?
  completedAt DateTime?
  createdAt   DateTime  @default(now())

  // Relationships
  tasks Task[] @relation("WorkflowStepTasks")

  @@map("workflow_step_executions")
}

enum StepStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  SKIPPED
}

// ============================================================================
// ENHANCED NOTIFICATION & COMMUNICATION
// ============================================================================

model NotificationTemplate {
  id       String @id @default(cuid())
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  name        String
  template    String // Template content with variables
  variables   Json // Template variable definitions
  messageType MessageType
  isActive    Boolean     @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  queuedNotifications NotificationQueue[]

  @@map("notification_templates")
}

model NotificationQueue {
  id          String               @id @default(cuid())
  templateId  String
  template    NotificationTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
  recipientId String

  templateData Json // Data to populate template
  priority     Priority           @default(NORMAL)
  status       NotificationStatus @default(PENDING)
  retryCount   Int                @default(0)
  maxRetries   Int                @default(3)

  scheduledAt DateTime?
  sentAt      DateTime?
  createdAt   DateTime  @default(now())

  @@map("notification_queue")
}

enum NotificationStatus {
  PENDING
  SENT
  DELIVERED
  FAILED
  CANCELLED
}

model CommunicationPreference {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  messageType MessageType
  isEnabled   Boolean     @default(true)
  preferences Json? // Type-specific preferences

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, messageType])
  @@map("communication_preferences")
}

// ============================================================================
// AI MODEL PERFORMANCE & OPTIMIZATION
// ============================================================================

model AiModelPerformance {
  id       String @id @default(cuid())
  provider String
  model    String

  avgResponseTime Float   @db.Real // Average response time in ms
  successRate     Float   @db.Real // Success rate percentage
  avgCostPerToken Decimal @db.Decimal(10, 6)
  accuracyScore   Float?  @db.Real // Model accuracy if available

  performanceMetrics Json // Additional performance data

  periodStart DateTime // Measurement period start
  periodEnd   DateTime // Measurement period end
  createdAt   DateTime @default(now())

  @@unique([provider, model, periodStart])
  @@map("ai_model_performance")
}

model ShadowModeExecution {
  id            String      @id @default(cuid())
  applicationId String
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  shadowDecision Json // AI decision in shadow mode
  liveDecision   Json? // Manual/production decision
  accuracy       Float?   @db.Real
  isCorrect      Boolean?
  varianceReason String? // Reason for difference

  executedAt DateTime
  createdAt  DateTime @default(now())

  @@map("shadow_mode_executions")
}

model MlModel {
  id            String @id @default(cuid())
  name          String
  version       String
  modelType     String // classification, regression, etc.
  configuration Json // Model configuration

  accuracy           Float? @db.Real
  precision          Float? @db.Real
  recall             Float? @db.Real
  performanceMetrics Json? // Additional metrics

  isActive Boolean @default(false)

  trainedAt  DateTime?
  deployedAt DateTime?
  createdAt  DateTime  @default(now())

  @@unique([name, version])
  @@map("ml_models")
}

// ============================================================================
// AI MODEL CONFIGURATION & MANAGEMENT
// ============================================================================

model AiModelConfiguration {
  id String @id @default(cuid())

  // Model Identity
  name     String // Human-readable name
  provider String // openrouter, ollama, azure, aws, etc.
  modelId  String // Provider-specific model identifier
  version  String @default("1.0")

  // Configuration Parameters
  temperature      Float? @db.Real // 0.0 - 2.0, creativity/randomness
  maxTokens        Int? // Maximum tokens in response
  topP             Float? @db.Real // 0.0 - 1.0, nucleus sampling
  topK             Int? // Top-k sampling
  frequencyPenalty Float? @db.Real // -2.0 - 2.0, reduce repetition
  presencePenalty  Float? @db.Real // -2.0 - 2.0, encourage new topics

  // System Behavior
  timeoutMs     Int @default(30000) // Request timeout
  retryAttempts Int @default(3) // Retry attempts on failure
  retryDelayMs  Int @default(1000) // Delay between retries

  // Cost & Usage
  costPerInputToken  Decimal  @db.Decimal(10, 8) // Cost per input token
  costPerOutputToken Decimal  @db.Decimal(10, 8) // Cost per output token
  dailyCostLimit     Decimal? @db.Decimal(8, 2) // Daily cost limit
  monthlyTokenLimit  Int? // Monthly token limit

  // Usage Rules
  usageScenarios String[] // What scenarios this model is used for
  priority       Int      @default(0) // Priority for model selection
  isActive       Boolean  @default(true)
  isDefault      Boolean  @default(false) // Default model for scenarios

  // Advanced Configuration
  customParameters Json? // Provider-specific custom parameters
  systemPrompt     String? // Default system prompt for this model
  stopSequences    String[] // Custom stop sequences

  // Metadata
  description String?
  tags        String[] // For categorization and filtering

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String? // User ID who created this configuration

  // Relationships
  tenantConfigs   TenantModelConfiguration[]
  auditLogs       ModelConfigurationAudit[]
  usageStats      ModelUsageStats[]
  fallbackTargets ModelFallbackRule[]        @relation("SourceModel")
  fallbackSources ModelFallbackRule[]        @relation("FallbackModel")

  @@unique([provider, modelId, version])
  @@index([provider, isActive])
  @@index([usageScenarios])
  @@map("ai_model_configurations")
}

model TenantModelConfiguration {
  id String @id @default(cuid())

  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  modelConfigId String
  modelConfig   AiModelConfiguration @relation(fields: [modelConfigId], references: [id], onDelete: Cascade)

  // Tenant-specific overrides
  isEnabled Boolean @default(true)
  priority  Int? // Override global priority

  // Tenant-specific parameters
  customConfig Json? // Tenant-specific parameter overrides

  // Usage limits for this tenant
  dailyCostLimit    Decimal? @db.Decimal(8, 2)
  monthlyTokenLimit Int?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([tenantId, modelConfigId])
  @@map("tenant_model_configurations")
}

model ModelFallbackRule {
  id String @id @default(cuid())

  sourceModelId String
  sourceModel   AiModelConfiguration @relation("SourceModel", fields: [sourceModelId], references: [id], onDelete: Cascade)

  fallbackModelId String
  fallbackModel   AiModelConfiguration @relation("FallbackModel", fields: [fallbackModelId], references: [id], onDelete: Cascade)

  // Fallback conditions
  scenario    String // When to fallback (timeout, error, cost_limit, etc.)
  priority    Int    @default(0) // Order of fallback attempts
  maxAttempts Int    @default(3) // Max attempts before trying fallback

  // Conditional fallback
  conditions Json? // Complex conditions for fallback (JSON rules)

  isActive Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([sourceModelId, fallbackModelId])
  @@index([scenario, priority])
  @@map("model_fallback_rules")
}

model ModelConfigurationAudit {
  id String @id @default(cuid())

  modelConfigId String
  modelConfig   AiModelConfiguration @relation(fields: [modelConfigId], references: [id], onDelete: Cascade)

  // Audit details
  action String // CREATE, UPDATE, DELETE, ACTIVATE, DEACTIVATE

  // What changed
  oldValues Json? // Previous configuration values
  newValues Json? // New configuration values
  changes   String[] // List of changed fields

  // Context
  userId String? // Who made the change
  reason String? // Reason for the change
  source String  @default("manual") // manual, api, automated

  // Request context
  ipAddress String?
  userAgent String?

  createdAt DateTime @default(now())

  @@index([modelConfigId, createdAt])
  @@index([action, createdAt])
  @@map("model_configuration_audit")
}

model ModelUsageStats {
  id String @id @default(cuid())

  modelConfigId String
  modelConfig   AiModelConfiguration @relation(fields: [modelConfigId], references: [id], onDelete: Cascade)

  tenantId String?

  // Time period
  periodStart DateTime
  periodEnd   DateTime
  granularity String   @default("hourly") // hourly, daily, weekly, monthly

  // Usage metrics
  totalRequests      Int @default(0)
  successfulRequests Int @default(0)
  failedRequests     Int @default(0)

  totalInputTokens  Int @default(0)
  totalOutputTokens Int @default(0)
  totalTokens       Int @default(0)

  totalCost Decimal @default(0) @db.Decimal(10, 4)

  // Performance metrics
  avgResponseTime Float  @db.Real
  minResponseTime Float? @db.Real
  maxResponseTime Float? @db.Real

  // Quality metrics
  avgQualityScore Float? @db.Real
  errorRate       Float  @default(0) @db.Real

  // Additional metrics
  metrics Json? // Custom metrics and data

  createdAt DateTime @default(now())

  @@unique([modelConfigId, tenantId, periodStart, granularity])
  @@index([periodStart, granularity])
  @@map("model_usage_stats")
}

enum ModelScenario {
  UNDERWRITING_ANALYSIS
  RISK_ASSESSMENT
  DOCUMENT_PROCESSING
  CONVERSATION_CHAT
  DATA_EXTRACTION
  DECISION_EXPLANATION
  COMPLIANCE_CHECK
  FRAUD_DETECTION
  CUSTOM
}

// ============================================================================
// PROVIDER PERFORMANCE & OPTIMIZATION
// ============================================================================

model ProviderPerformance {
  id       String @id @default(cuid())
  pluginId String
  plugin   Plugin @relation(fields: [pluginId], references: [id], onDelete: Cascade)
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  avgResponseTime Float   @db.Real // Average response time in ms
  successRate     Float   @db.Real // Success rate percentage
  costPerRequest  Decimal @db.Decimal(8, 4)
  uptimePercent   Float   @db.Real // Uptime percentage
  slaViolations   Int     @default(0)

  periodStart DateTime // Measurement period start
  periodEnd   DateTime // Measurement period end
  createdAt   DateTime @default(now())

  @@unique([pluginId, tenantId, periodStart])
  @@map("provider_performance")
}

// ============================================================================
// ENHANCED ANALYTICS & METRICS
// ============================================================================

model TenantMetrics {
  id       String @id @default(cuid())
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  metricDate           DateTime @db.Date
  totalApplications    Int      @default(0)
  approvedApplications Int      @default(0)
  rejectedApplications Int      @default(0)
  approvalRate         Float    @db.Real

  totalAiCost       Decimal @default(0) @db.Decimal(10, 4)
  totalAiTokens     Int     @default(0)
  avgProcessingTime Float   @db.Real // Hours
  avgRiskScore      Float   @db.Real

  createdAt DateTime @default(now())

  @@unique([tenantId, metricDate])
  @@map("tenant_metrics")
}

model ApplicationMetrics {
  id            String      @id @default(cuid())
  applicationId String      @unique
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  conversationMessages Int     @default(0)
  aiCost               Decimal @default(0) @db.Decimal(8, 4)
  aiTokens             Int     @default(0)
  documentsUploaded    Int     @default(0)
  externalApiCalls     Int     @default(0)
  externalApiCost      Decimal @default(0) @db.Decimal(8, 4)
  processingTimeHours  Float   @db.Real

  createdAt DateTime @default(now())

  @@map("application_metrics")
}

// ============================================================================
// COMPLIANCE & DATA GOVERNANCE
// ============================================================================

model DataSubjectRequest {
  id       String @id @default(cuid())
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  requestType    DataRequestType
  subjectEmail   String // Email of data subject
  status         RequestStatus   @default(PENDING)
  requestDetails Json // Details of the request
  responseData   Json? // Response/export data

  submittedAt     DateTime
  completedAt     DateTime?
  completionNotes String?

  createdAt DateTime @default(now())

  @@map("data_subject_requests")
}

enum DataRequestType {
  EXPORT
  DELETE
  CORRECT
  RESTRICT
}

enum RequestStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  REJECTED
}

model RetentionPolicy {
  id       String @id @default(cuid())
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  entityType       String // Table/entity name
  retentionDays    Int // Days to retain data
  autoDelete       Boolean @default(false)
  deletionCriteria Json? // Additional deletion criteria
  isActive         Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([tenantId, entityType])
  @@map("retention_policies")
}

// ============================================================================
// DOCUMENT VERSIONING
// ============================================================================

model DocumentVersion {
  id         String   @id @default(cuid())
  documentId String
  document   Document @relation(fields: [documentId], references: [id], onDelete: Cascade)

  versionNumber Int
  storagePath   String
  changeReason  String?
  createdBy     String // User ID

  createdAt DateTime @default(now())

  @@unique([documentId, versionNumber])
  @@map("document_versions")
}

// ============================================================================
// ENHANCED RELATIONSHIPS & UPDATES TO EXISTING MODELS
// ============================================================================

// Enhanced relationships are included in the original model definitions above

// Views and materialized views would be defined here for reporting
// (These would be created via raw SQL migrations)

// ============================================================================
// BILLING & SUBSCRIPTION MANAGEMENT
// ============================================================================

model Subscription {
  id       String @id @default(cuid())
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Subscription Details
  tier         SubscriptionTier
  status       SubscriptionStatus @default(ACTIVE)
  billingCycle BillingCycle       @default(MONTHLY)

  // Pricing Configuration
  monthlyFee    Decimal  @db.Decimal(10, 2)
  setupFee      Decimal? @db.Decimal(10, 2)
  includedUsage Int // Applications included in monthly fee
  overageRate   Decimal  @db.Decimal(6, 2) // Per-application overage rate

  // Billing Periods
  currentPeriodStart DateTime
  currentPeriodEnd   DateTime
  nextBillingDate    DateTime

  // Proration & Adjustments
  proratedAmount Decimal? @db.Decimal(10, 2)
  pendingChanges Json? // Pending tier changes, etc.

  // Lifecycle
  trialEndsAt        DateTime?
  cancelledAt        DateTime?
  cancellationReason String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  invoices            Invoice[]
  usageAggregations   UsageAggregation[]
  subscriptionHistory SubscriptionHistory[]

  @@unique([tenantId]) // One active subscription per tenant
  @@index([status, nextBillingDate])
  @@map("subscriptions")
}

enum SubscriptionStatus {
  ACTIVE
  TRIALING
  PAST_DUE
  CANCELLED
  SUSPENDED
}

enum BillingCycle {
  MONTHLY
  YEARLY
}

model SubscriptionHistory {
  id             String       @id @default(cuid())
  subscriptionId String
  subscription   Subscription @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)

  action     SubscriptionAction
  fromTier   SubscriptionTier?
  toTier     SubscriptionTier?
  fromStatus SubscriptionStatus?
  toStatus   SubscriptionStatus?
  reason     String?
  metadata   Json?

  changedBy String? // User ID who made the change
  changedAt DateTime @default(now())

  @@map("subscription_history")
}

enum SubscriptionAction {
  CREATED
  TIER_CHANGED
  STATUS_CHANGED
  CANCELLED
  REACTIVATED
  TRIAL_STARTED
  TRIAL_ENDED
}

// ============================================================================
// USAGE TRACKING & METERING
// ============================================================================

model UsageEvent {
  id            String  @id @default(cuid())
  tenantId      String
  tenant        Tenant  @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  applicationId String? // Optional - some usage might not be application-specific

  // Event Details
  eventType UsageEventType
  quantity  Int            @default(1)
  unitCost  Decimal        @db.Decimal(8, 4)
  totalCost Decimal        @db.Decimal(10, 4)

  // Context
  description String?
  metadata    Json? // Additional event data

  // Billing Period Assignment
  billingPeriod String // YYYY-MM format for aggregation

  timestamp DateTime @default(now())

  @@index([tenantId, billingPeriod])
  @@index([eventType, timestamp])
  @@map("usage_events")
}

enum UsageEventType {
  APPLICATION_PROCESSING
  API_CALL
  STORAGE_USAGE
  PREMIUM_FEATURE
  PROFESSIONAL_SERVICES
}

model UsageAggregation {
  id             String       @id @default(cuid())
  tenantId       String
  tenant         Tenant       @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  subscriptionId String
  subscription   Subscription @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)

  // Billing Period
  billingPeriod String // YYYY-MM format
  periodStart   DateTime
  periodEnd     DateTime

  // Usage Summary
  totalUsage    Int @default(0)
  includedUsage Int // From subscription tier
  overageUsage  Int @default(0) // Computed: totalUsage - includedUsage (if positive)

  // Cost Calculation
  subscriptionFee Decimal @db.Decimal(10, 2)
  overageFee      Decimal @default(0) @db.Decimal(10, 2)
  additionalFees  Decimal @default(0) @db.Decimal(10, 2)
  totalCost       Decimal @db.Decimal(10, 2)

  // Status
  isFinalized Boolean   @default(false)
  finalizedAt DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([tenantId, billingPeriod])
  @@index([billingPeriod, isFinalized])
  @@map("usage_aggregations")
}

// ============================================================================
// INVOICING & BILLING
// ============================================================================

model Invoice {
  id             String       @id @default(cuid())
  tenantId       String
  tenant         Tenant       @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  subscriptionId String
  subscription   Subscription @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)

  // Invoice Details
  invoiceNumber String        @unique
  status        InvoiceStatus @default(DRAFT)

  // Billing Period
  billingPeriod String // YYYY-MM format
  periodStart   DateTime
  periodEnd     DateTime

  // Amounts
  subtotal       Decimal  @db.Decimal(10, 2)
  taxRate        Decimal? @db.Decimal(5, 4)
  taxAmount      Decimal  @default(0) @db.Decimal(10, 2)
  discountAmount Decimal  @default(0) @db.Decimal(10, 2)
  total          Decimal  @db.Decimal(10, 2)

  // Payment Terms
  dueDate DateTime
  paidAt  DateTime?

  // Invoice Generation
  generatedAt DateTime  @default(now())
  sentAt      DateTime?

  // Metadata
  notes    String?
  metadata Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  lineItems   InvoiceLineItem[]
  payments    Payment[]
  adjustments InvoiceAdjustment[]

  @@index([tenantId, status])
  @@index([status, dueDate])
  @@index([billingPeriod])
  @@map("invoices")
}

enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
  CANCELLED
  REFUNDED
}

model InvoiceLineItem {
  id        String  @id @default(cuid())
  invoiceId String
  invoice   Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  // Line Item Details
  description String
  quantity    Int
  unitPrice   Decimal @db.Decimal(10, 4)
  totalPrice  Decimal @db.Decimal(10, 2)

  // Classification
  itemType InvoiceItemType

  // Metadata
  metadata Json?

  @@map("invoice_line_items")
}

enum InvoiceItemType {
  SUBSCRIPTION
  USAGE_OVERAGE
  SETUP_FEE
  PROFESSIONAL_SERVICES
  ADJUSTMENT
  CREDIT
}

model InvoiceAdjustment {
  id        String  @id @default(cuid())
  invoiceId String
  invoice   Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  adjustmentType AdjustmentType
  amount         Decimal        @db.Decimal(10, 2)
  reason         String
  description    String?

  createdBy String // User ID who made the adjustment
  createdAt DateTime @default(now())

  @@map("invoice_adjustments")
}

enum AdjustmentType {
  CREDIT
  DEBIT
  DISCOUNT
  TAX_ADJUSTMENT
}

// ============================================================================
// PAYMENT PROCESSING
// ============================================================================

model PaymentMethod {
  id       String @id @default(cuid())
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Payment Method Details
  type             PaymentMethodType
  provider         String // stripe, adyen, etc.
  providerMethodId String // Provider's payment method ID/token

  // Card Details (for display only - actual data stored with provider)
  lastFour    String?
  brand       String? // visa, mastercard, etc.
  expiryMonth Int?
  expiryYear  Int?

  // Bank Account Details (for display only)
  bankName    String?
  accountType String? // checking, savings

  // Settings
  isDefault Boolean @default(false)
  isActive  Boolean @default(true)

  // Verification
  isVerified Boolean   @default(false)
  verifiedAt DateTime?

  // Metadata
  billingAddress Json? // Address information
  metadata       Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  payments Payment[]

  @@index([tenantId, isDefault])
  @@index([tenantId, isActive])
  @@map("payment_methods")
}

enum PaymentMethodType {
  CARD
  BANK_ACCOUNT
  ACH
  WIRE
}

model Payment {
  id              String         @id @default(cuid())
  invoiceId       String
  invoice         Invoice        @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  paymentMethodId String?
  paymentMethod   PaymentMethod? @relation(fields: [paymentMethodId], references: [id])

  // Payment Details
  amount   Decimal       @db.Decimal(10, 2)
  currency String        @default("USD")
  status   PaymentStatus @default(PENDING)

  // Gateway Information
  provider          String // stripe, adyen, etc.
  providerPaymentId String? // Provider's payment/transaction ID
  providerFee       Decimal? @db.Decimal(8, 4)

  // Processing
  processedAt   DateTime?
  settledAt     DateTime?
  failedAt      DateTime?
  failureReason String?
  failureCode   String?

  // Retry Logic
  retryCount  Int       @default(0)
  maxRetries  Int       @default(3)
  nextRetryAt DateTime?

  // Metadata
  metadata Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([status, nextRetryAt])
  @@index([invoiceId])
  @@map("payments")
}

enum PaymentStatus {
  PENDING
  PROCESSING
  SUCCEEDED
  FAILED
  CANCELLED
  REFUNDED
  PARTIALLY_REFUNDED
}

model PaymentRetry {
  id        String @id @default(cuid())
  paymentId String

  attemptNumber Int
  attemptedAt   DateTime
  status        PaymentStatus
  failureReason String?
  failureCode   String?
  metadata      Json?

  @@map("payment_retries")
}

// ============================================================================
// BILLING AUTOMATION & JOBS
// ============================================================================

model BillingJob {
  id      String         @id @default(cuid())
  jobType BillingJobType
  status  JobStatus      @default(PENDING)

  // Job Details
  scheduledFor DateTime
  startedAt    DateTime?
  completedAt  DateTime?
  failedAt     DateTime?

  // Job Parameters
  parameters Json? // Job-specific parameters

  // Results
  successCount Int   @default(0)
  errorCount   Int   @default(0)
  errors       Json? // Error details

  // Retry Logic
  retryCount  Int       @default(0)
  maxRetries  Int       @default(3)
  nextRetryAt DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([jobType, status])
  @@index([scheduledFor])
  @@map("billing_jobs")
}

enum BillingJobType {
  INVOICE_GENERATION
  PAYMENT_PROCESSING
  USAGE_AGGREGATION
  DUNNING_MANAGEMENT
  SUBSCRIPTION_RENEWAL
}

enum JobStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
}

// ============================================================================
// SYSTEM USER MANAGEMENT
// ============================================================================

model SystemUser {
  id           String @id @default(cuid())
  email        String @unique
  name         String
  passwordHash String

  // System Role & Permissions
  role        SystemUserRole
  permissions Json? // Granular permissions

  // Profile
  avatar   String?
  timezone String? @default("UTC")
  locale   String? @default("en")

  // Authentication
  emailVerified   Boolean   @default(false)
  emailVerifiedAt DateTime?
  mfaEnabled      Boolean   @default(false)
  mfaSecret       String?
  lastLoginAt     DateTime?

  // Status
  isActive    Boolean   @default(true)
  suspendedAt DateTime?
  suspendedBy String? // Another system user ID

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  sessions    SystemUserSession[]
  auditLogs   SystemUserAuditLog[]
  backupCodes SystemUserBackupCode[]

  @@map("system_users")
}

enum SystemUserRole {
  SUPER_ADMIN
  PLATFORM_ADMIN
  FINANCE_ADMIN
  SUPPORT_ADMIN
  ANALYTICS_VIEWER
  BILLING_ADMIN
}

model SystemUserSession {
  id     String     @id @default(cuid())
  userId String
  user   SystemUser @relation(fields: [userId], references: [id], onDelete: Cascade)

  sessionToken String  @unique
  refreshToken String? @unique
  device       String?
  ipAddress    String?
  userAgent    String?

  expiresAt    DateTime
  lastActiveAt DateTime @default(now())
  createdAt    DateTime @default(now())

  @@map("system_user_sessions")
}

model SystemUserAuditLog {
  id     String     @id @default(cuid())
  userId String
  user   SystemUser @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Action Details
  action     String // CREATE, UPDATE, DELETE, LOGIN, etc.
  entityType String // Tenant, Subscription, Invoice, etc.
  entityId   String? // ID of the affected entity

  // Context
  ipAddress String?
  userAgent String?

  // Change Details
  oldValues Json? // Previous state
  newValues Json? // New state
  metadata  Json? // Additional context

  timestamp DateTime @default(now())

  @@index([userId, timestamp])
  @@index([entityType, entityId])
  @@map("system_user_audit_logs")
}

model SystemUserBackupCode {
  id     String     @id @default(cuid())
  userId String
  user   SystemUser @relation(fields: [userId], references: [id], onDelete: Cascade)

  hashedCode String
  isUsed     Boolean   @default(false)
  usedAt     DateTime?

  createdAt DateTime @default(now())

  @@index([userId, isUsed])
  @@map("system_user_backup_codes")
}

// ============================================================================
// BILLING ANALYTICS & REPORTING
// ============================================================================

model BillingAnalytics {
  id String @id @default(cuid())

  // Time Period
  period     String // YYYY-MM or YYYY-MM-DD
  periodType String // monthly, daily, weekly

  // Revenue Metrics
  totalRevenue        Decimal @db.Decimal(12, 2)
  subscriptionRevenue Decimal @db.Decimal(12, 2)
  usageRevenue        Decimal @db.Decimal(12, 2)
  setupFeeRevenue     Decimal @db.Decimal(12, 2)

  // Customer Metrics
  totalTenants   Int
  newTenants     Int
  churnedTenants Int

  // Tier Breakdown
  starterTenants      Int
  professionalTenants Int
  enterpriseTenants   Int

  // Payment Metrics
  successfulPayments Int
  failedPayments     Int
  retryPayments      Int

  // Usage Metrics
  totalApplications     Int
  averageUsagePerTenant Decimal @db.Decimal(10, 2)

  // Generated
  calculatedAt DateTime @default(now())

  @@unique([period, periodType])
  @@index([period])
  @@map("billing_analytics")
}

// ============================================================================
// BILLING NOTIFICATIONS & COMMUNICATIONS
// ============================================================================

model BillingNotification {
  id       String  @id @default(cuid())
  tenantId String? // Null for system notifications

  // Notification Details
  type    BillingNotificationType
  title   String
  message String

  // Delivery
  deliveryMethod NotificationDeliveryMethod
  recipientEmail String?
  status         NotificationStatus         @default(PENDING)

  // Timing
  scheduledFor DateTime?
  sentAt       DateTime?

  // Related Entities
  invoiceId      String?
  paymentId      String?
  subscriptionId String?

  // Metadata
  metadata Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([tenantId, type])
  @@index([status, scheduledFor])
  @@map("billing_notifications")
}

enum BillingNotificationType {
  INVOICE_GENERATED
  PAYMENT_SUCCEEDED
  PAYMENT_FAILED
  PAYMENT_RETRY
  SUBSCRIPTION_UPGRADED
  SUBSCRIPTION_DOWNGRADED
  USAGE_LIMIT_WARNING
  ACCOUNT_SUSPENDED
  TRIAL_ENDING
  TRIAL_ENDED
}

enum NotificationDeliveryMethod {
  EMAIL
  IN_APP
  WEBHOOK
  SMS
}
