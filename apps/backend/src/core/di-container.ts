/**
 * Advanced Dependency Injection Container
 * Enterprise-grade service dependency management with sophisticated orchestration capabilities
 * Implements modern IoC patterns with lifecycle management and circular dependency detection
 */

import 'reflect-metadata';
import type { Request, Response, NextFunction } from 'express';

// Dependency injection tokens for service identification
export const DI_TOKENS = {
  // Core Infrastructure
  DATABASE: Symbol('Database'),
  REDIS: Symbol('Redis'),
  LOGGER: Symbol('Logger'),
  CONFIG: Symbol('Config'),
  
  // Domain Services  
  TENANT_SERVICE: Symbol('TenantService'),
  USER_SERVICE: Symbol('UserService'),
  AUTH_SERVICE: Symbol('AuthService'),
  COMMUNICATION_SERVICE: Symbol('CommunicationService'),
  ANALYTICS_SERVICE: Symbol('AnalyticsService'),
  
  // External Services
  EMAIL_SERVICE: Symbol('EmailService'),
  AI_SERVICE: Symbol('AIService'),
  STORAGE_SERVICE: Symbol('StorageService'),
  
  // Repositories
  TENANT_REPOSITORY: Symbol('TenantRepository'),
  USER_REPOSITORY: Symbol('UserRepository'),
  APPLICATION_REPOSITORY: Symbol('ApplicationRepository'),
} as const;

export type DIToken = typeof DI_TOKENS[keyof typeof DI_TOKENS] | string | symbol;

// Service lifecycle management
export enum ServiceLifetime {
  SINGLETON = 'singleton',  // One instance for application lifetime
  SCOPED = 'scoped',       // One instance per request scope
  TRANSIENT = 'transient', // New instance every time
}

// Service registration metadata
export interface ServiceRegistration<T = unknown> {
  token: DIToken;
  factory: (container: ServiceContainer) => T;
  lifetime: ServiceLifetime;
  instance?: T;
  dependencies?: DIToken[];
}

// Dependency injection decorators
export const Injectable = (token?: DIToken): ClassDecorator => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return (target: any) => {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
    const serviceToken = token || target.name;
    Reflect.defineMetadata('di:token', serviceToken, target);
    Reflect.defineMetadata('di:injectable', true, target);
    
    // Auto-detect constructor dependencies
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const paramTypes = Reflect.getMetadata('design:paramtypes', target) || [];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call
    const paramTokens = paramTypes.map((type: any, index: number) => {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      const paramToken = Reflect.getMetadata(`di:param:${index}`, target);
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-member-access
      return paramToken || type.name || `Unknown_${index}`;
    });
    
    Reflect.defineMetadata('di:dependencies', paramTokens, target);
  };
};

export const Inject = (token: DIToken): ParameterDecorator => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return (target: any, _propertyKey: string | symbol | undefined, parameterIndex: number) => {
    Reflect.defineMetadata(`di:param:${parameterIndex}`, token, target);
  };
};

/**
 * Advanced Dependency Injection Container
 * Implements enterprise-grade IoC patterns with sophisticated service orchestration
 */
export class ServiceContainer {
  private services = new Map<DIToken, ServiceRegistration>();
  private scopedInstances = new Map<DIToken, unknown>();
  private resolving = new Set<DIToken>();

  /**
   * Register a service with the container
   */
  public register<T>(
    token: DIToken,
    factory: (container: ServiceContainer) => T,
    lifetime: ServiceLifetime = ServiceLifetime.SINGLETON,
    dependencies: DIToken[] = []
  ): this {
    this.services.set(token, {
      token,
      factory,
      lifetime,
      dependencies,
    });

    return this;
  }

  /**
   * Register a singleton service
   */
  public registerSingleton<T>(
    token: DIToken,
    factory: (container: ServiceContainer) => T,
    dependencies: DIToken[] = []
  ): this {
    return this.register(token, factory, ServiceLifetime.SINGLETON, dependencies);
  }

  /**
   * Register a scoped service (per-request)
   */
  public registerScoped<T>(
    token: DIToken,
    factory: (container: ServiceContainer) => T,
    dependencies: DIToken[] = []
  ): this {
    return this.register(token, factory, ServiceLifetime.SCOPED, dependencies);
  }

  /**
   * Register a transient service (new instance each time)
   */
  public registerTransient<T>(
    token: DIToken,
    factory: (container: ServiceContainer) => T,
    dependencies: DIToken[] = []
  ): this {
    return this.register(token, factory, ServiceLifetime.TRANSIENT, dependencies);
  }

  /**
   * Register a class with automatic dependency injection
   */
  public registerClass<T>(
    ClassConstructor: new (...args: unknown[]) => T,
    token?: DIToken,
    lifetime: ServiceLifetime = ServiceLifetime.SINGLETON
  ): this {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const serviceToken = token || Reflect.getMetadata('di:token', ClassConstructor) || ClassConstructor.name;
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const dependencies = Reflect.getMetadata('di:dependencies', ClassConstructor) || [];

    const factory = (container: ServiceContainer): T => {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
      const resolvedDependencies = dependencies.map((dep: DIToken) => container.resolve(dep));
      return new ClassConstructor(...resolvedDependencies);
    };

    return this.register(serviceToken, factory, lifetime, dependencies);
  }

  /**
   * Resolve a service from the container
   */
  public resolve<T>(token: DIToken): T {
    // Check for circular dependencies
    if (this.resolving.has(token)) {
      throw new Error(
        `Circular dependency detected while resolving ${String(token)}. ` +
        `Resolution chain: ${Array.from(this.resolving).map(String).join(' -> ')} -> ${String(token)}`
      );
    }

    const registration = this.services.get(token) as ServiceRegistration<T>;
    if (!registration) {
      throw new Error(`Service ${String(token)} is not registered in the service container`);
    }

    // Handle different lifetimes
    switch (registration.lifetime) {
      case ServiceLifetime.SINGLETON:
        if (!registration.instance) {
          this.resolving.add(token);
          try {
            registration.instance = registration.factory(this);
          } finally {
            this.resolving.delete(token);
          }
        }
        return registration.instance;

      case ServiceLifetime.SCOPED:
        if (!this.scopedInstances.has(token)) {
          this.resolving.add(token);
          try {
            const instance = registration.factory(this);
            this.scopedInstances.set(token, instance);
          } finally {
            this.resolving.delete(token);
          }
        }
        return this.scopedInstances.get(token) as T;

      case ServiceLifetime.TRANSIENT:
        this.resolving.add(token);
        try {
          return registration.factory(this);
        } finally {
          this.resolving.delete(token);
        }

      default:
        throw new Error(`Unknown service lifetime: ${registration.lifetime}`);
    }
  }

  /**
   * Check if a service is registered
   */
  public isRegistered(token: DIToken): boolean {
    return this.services.has(token);
  }

  /**
   * Get all registered services
   */
  public getRegisteredServices(): DIToken[] {
    return Array.from(this.services.keys());
  }

  /**
   * Clear scoped instances (call at end of request)
   */
  public clearScope(): void {
    this.scopedInstances.clear();
  }

  /**
   * Create a child container with inherited services
   */
  public createChild(): ServiceContainer {
    const child = new ServiceContainer();
    
    // Copy parent registrations
    for (const [token, registration] of this.services) {
      child.services.set(token, { ...registration });
    }
    
    return child;
  }

  /**
   * Dispose all singleton instances (shutdown)
   */
  public async dispose(): Promise<void> {
    const disposalPromises: Promise<void>[] = [];

    for (const registration of this.services.values()) {
      if (registration.instance && 
          typeof registration.instance === 'object' && 
          registration.instance !== null && 
          'dispose' in registration.instance && 
          typeof (registration.instance as { dispose: () => Promise<void> }).dispose === 'function') {
        disposalPromises.push((registration.instance as { dispose: () => Promise<void> }).dispose());
      }
    }

    await Promise.all(disposalPromises);
    this.services.clear();
    this.scopedInstances.clear();
  }

  /**
   * Validate all service dependencies can be resolved
   */
  public validateDependencies(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    for (const [token, registration] of this.services) {
      for (const dependency of registration.dependencies || []) {
        if (!this.services.has(dependency)) {
          errors.push(
            `Service ${String(token)} depends on ${String(dependency)} which is not registered`
          );
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get service dependency graph for debugging
   */
  public getDependencyGraph(): Record<string, string[]> {
    const graph: Record<string, string[]> = {};

    for (const [token, registration] of this.services) {
      graph[String(token)] = (registration.dependencies || []).map(String);
    }

    return graph;
  }
}

/**
 * Global Service Container Instance
 * The central orchestration system for enterprise service management
 */
export const serviceContainer = new ServiceContainer();

/**
 * Express middleware for request-scoped services
 */
export const createScopeMiddleware = () => {
  return (req: Request, res: Response, next: NextFunction): void => {
    // Attach scoped container to request
    (req as Request & { container?: ServiceContainer }).container = serviceContainer.createChild();
    
    // Clear scope after response
    res.on('finish', () => {
      (req as Request & { container?: ServiceContainer }).container?.clearScope();
    });
    
    next();
  };
};

/**
 * Utility to resolve services with proper typing
 */
export const resolve = <T>(token: DIToken): T => {
  return serviceContainer.resolve<T>(token);
};

// Export container for advanced usage
export default serviceContainer;