/**
 * Advanced Service Container Configuration
 * Enterprise dependency orchestration system with sophisticated service lifecycle management
 * Implements modern IoC patterns with comprehensive health monitoring and validation
 */

import { PrismaClient } from '@prisma/client';
import { serviceContainer, DI_TOKENS, ServiceLifetime, ServiceContainer } from './di-container';
import { configService } from '../services/config';
import { logger } from '../utils/logger';

// Import all repositories
import { 
  TenantRepository, 
  UserRepository, 
  ApplicationRepository,
  RepositoryFactory
} from './repository';

// Import domain services
import { TenantDomainService } from '../domains/tenant/TenantDomainService';

// Import application services
import { ErrorHandlingService } from '../services/error-handling';

/**
 * Enterprise Service Registry
 * Orchestrates all dependencies with sophisticated precision and lifecycle management
 */
export class ServiceRegistry {
  private static isInitialized = false;

  /**
   * Initialize the entire dependency injection system
   */
  public static async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.warn('Service Container already initialized');
      return;
    }

    logger.info('🔧 Initializing Enterprise Service Container...');

    try {
      // Phase 1: Register Core Infrastructure
      await this.registerCoreInfrastructure();
      
      // Phase 2: Register Data Access Layer
      this.registerRepositories();
      
      // Phase 3: Register Domain Services
      this.registerDomainServices();
      
      // Phase 4: Register Application Services
      this.registerApplicationServices();
      
      // Phase 5: Register External Services
      this.registerExternalServices();
      
      // Validate all dependencies
      const validation = serviceContainer.validateDependencies();
      if (!validation.isValid) {
        logger.error('Container dependency validation failed:', validation.errors);
        throw new Error(`Dependency validation failed: ${validation.errors.join(', ')}`);
      }

      this.isInitialized = true;
      logger.info('✨ Enterprise Service Container initialized successfully');
      logger.debug('Dependency Graph:', serviceContainer.getDependencyGraph());
      
    } catch (error) {
      logger.error('Failed to initialize Enterprise Service Container:', { error });
      throw error;
    }
  }

  /**
   * Register core infrastructure services
   */
  private static async registerCoreInfrastructure(): Promise<void> {
    logger.debug('Registering core infrastructure...');

    // Configuration Service (Singleton)
    serviceContainer.registerSingleton(
      DI_TOKENS.CONFIG,
      () => configService
    );

    // Logger Service (Singleton)
    serviceContainer.registerSingleton(
      DI_TOKENS.LOGGER,
      () => logger
    );

    // Database Connection (Singleton)
    serviceContainer.registerSingleton(
      DI_TOKENS.DATABASE,
      () => {
        const config = configService.getDatabaseConfig();
        
        const prisma = new PrismaClient({
          log: configService.isDevelopment() 
            ? ['query', 'info', 'warn', 'error']
            : ['warn', 'error'],
          errorFormat: 'colorless',
          datasources: {
            db: {
              url: config.url,
            },
          },
        });

        // Graceful shutdown handlers
        const shutdown = async (): Promise<void> => {
          logger.info('Disconnecting from database...');
          await prisma.$disconnect();
        };

        process.on('SIGINT', shutdown);
        process.on('SIGTERM', shutdown);
        process.on('beforeExit', shutdown);

        logger.info('Database connection established');
        return prisma;
      }
    );

    // Redis Connection (Singleton) - TODO: Implement Redis client
    serviceContainer.registerSingleton(
      DI_TOKENS.REDIS,
      () => {
        // Placeholder for Redis client
        logger.info('Redis connection placeholder registered');
        return null;
      }
    );
  }

  /**
   * Register repository layer (Data Access Layer)
   */
  private static registerRepositories(): void {
    logger.debug('Registering enterprise repositories...');

    // Repository Factory (Singleton)
    serviceContainer.registerClass(RepositoryFactory as new (...args: unknown[]) => RepositoryFactory, 'RepositoryFactory', ServiceLifetime.SINGLETON);

    // Tenant Repository (Singleton)
    serviceContainer.registerClass(
      TenantRepository as new (...args: unknown[]) => TenantRepository, 
      DI_TOKENS.TENANT_REPOSITORY, 
      ServiceLifetime.SINGLETON
    );

    // User Repository (Singleton)
    serviceContainer.registerClass(
      UserRepository as new (...args: unknown[]) => UserRepository, 
      DI_TOKENS.USER_REPOSITORY, 
      ServiceLifetime.SINGLETON
    );

    // Application Repository (Singleton)
    serviceContainer.registerClass(
      ApplicationRepository as new (...args: unknown[]) => ApplicationRepository, 
      DI_TOKENS.APPLICATION_REPOSITORY, 
      ServiceLifetime.SINGLETON
    );

    logger.info('Enterprise repositories registered');
  }

  /**
   * Register domain services (Business Logic)
   */
  private static registerDomainServices(): void {
    logger.debug('Registering domain services...');

    // Tenant Domain Service (Singleton)
    serviceContainer.registerClass(
      TenantDomainService as new (...args: unknown[]) => TenantDomainService, 
      DI_TOKENS.TENANT_SERVICE, 
      ServiceLifetime.SINGLETON
    );

    // TODO: Register other domain services
    // - UserDomainService
    // - ApplicationDomainService
    // - AnalyticsDomainService

    logger.info('Domain services registered');
  }

  /**
   * Register application services (Use Cases)
   */
  private static registerApplicationServices(): void {
    logger.debug('Registering application services...');

    // Error Handling Service (Singleton)
    serviceContainer.registerSingleton(
      'ErrorHandlingService',
      () => ErrorHandlingService
    );

    // TODO: Register application services
    // - TenantApplicationService
    // - UserApplicationService
    // - AuthApplicationService

    logger.info('Application services registered');
  }

  /**
   * Register external services and integrations
   */
  private static registerExternalServices(): void {
    logger.debug('Registering external services...');

    // Email Service (Singleton)
    serviceContainer.registerSingleton(
      DI_TOKENS.EMAIL_SERVICE,
      () => {
        // TODO: Implement email service
        logger.info('Email service placeholder registered');
        return null;
      }
    );

    // AI Service (Singleton)
    serviceContainer.registerSingleton(
      DI_TOKENS.AI_SERVICE,
      () => {
        // TODO: Implement AI service
        logger.info('AI service placeholder registered');
        return null;
      }
    );

    // Storage Service (Singleton)
    serviceContainer.registerSingleton(
      DI_TOKENS.STORAGE_SERVICE,
      () => {
        // TODO: Implement storage service
        logger.info('Storage service placeholder registered');
        return null;
      }
    );

    logger.info('External services registered');
  }

  /**
   * Get a service from the container with type safety
   */
  public static resolve<T>(token: symbol | string): T {
    if (!this.isInitialized) {
      throw new Error('Service Container must be initialized before resolving services');
    }

    return serviceContainer.resolve<T>(token);
  }

  /**
   * Create a request-scoped container for HTTP requests
   */
  public static createRequestScope(): ServiceContainer {
    if (!this.isInitialized) {
      throw new Error('Service Container must be initialized before creating scopes');
    }

    return serviceContainer.createChild();
  }

  /**
   * Get container health status
   */
  public static getHealthStatus(): {
    initialized: boolean;
    servicesRegistered: number;
    dependenciesValid: boolean;
    errors: string[];
  } {
    const validation = serviceContainer.validateDependencies();
    
    return {
      initialized: this.isInitialized,
      servicesRegistered: serviceContainer.getRegisteredServices().length,
      dependenciesValid: validation.isValid,
      errors: validation.errors,
    };
  }

  /**
   * Shutdown the container gracefully
   */
  public static async shutdown(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    logger.info('🔧 Shutting down Service Container...');
    
    try {
      await serviceContainer.dispose();
      this.isInitialized = false;
      logger.info('✨ Service Container shut down successfully');
    } catch (error) {
      logger.error('Error during container shutdown:', { error });
      throw error;
    }
  }

  /**
   * Development helper - list all registered services
   */
  public static listRegisteredServices(): string[] {
    return serviceContainer.getRegisteredServices().map(token => String(token));
  }
}

// Convenience exports
export const resolve = ServiceRegistry.resolve;
export const initializeContainer = ServiceRegistry.initialize;
export const shutdownContainer = ServiceRegistry.shutdown;

// Export for testing
export { serviceContainer };

export default ServiceRegistry;