/**
 * Advanced Repository Pattern Implementation
 * Enterprise data access layer with clean separation between business logic and data persistence
 * Eliminates architectural anti-patterns and provides sophisticated query capabilities
 */

import { 
  PrismaClient, 
  Tenant, 
  User, 
  Application
} from '@prisma/client';

// Use generic types for Prisma input types - these will be refined when Prisma client is properly generated
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type TenantCreateInput = any;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type TenantUpdateInput = any;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type UserCreateInput = any;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type UserUpdateInput = any;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type ApplicationCreateInput = any;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type ApplicationUpdateInput = any;
import { Injectable, Inject, DI_TOKENS } from './di-container';

// Base repository interfaces for all entities
export interface IBaseRepository<T, TCreateInput, TUpdateInput> {
  findById(id: string): Promise<T | null>;
  findMany(options?: QueryOptions): Promise<PaginatedResult<T>>;
  create(data: TCreateInput): Promise<T>;
  update(id: string, data: TUpdateInput): Promise<T>;
  delete(id: string): Promise<boolean>;
  exists(id: string): Promise<boolean>;
}

// Query options for flexible data retrieval
export interface QueryOptions {
  where?: Record<string, unknown>;
  include?: Record<string, unknown>;
  select?: Record<string, unknown>;
  orderBy?: Record<string, 'asc' | 'desc'>;
  skip?: number;
  take?: number;
}

// Paginated result structure
export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Advanced Base Repository
 * Foundation for all data access with enterprise-grade patterns and error handling
 */
export abstract class BaseRepository<T, TCreateInput, TUpdateInput> 
  implements IBaseRepository<T, TCreateInput, TUpdateInput> {
  
  constructor(
    @Inject(DI_TOKENS.DATABASE) protected readonly prisma: PrismaClient,
    protected readonly modelName: string
  ) {}

  protected get model(): {
    findUnique: (args: { where: { id: string } }) => Promise<T | null>;
    findMany: (args?: {
      where?: Record<string, unknown>;
      include?: Record<string, unknown>;
      select?: Record<string, unknown>;
      orderBy?: Record<string, 'asc' | 'desc'>;
      skip?: number;
      take?: number;
    }) => Promise<T[]>;
    count: (args?: { where?: Record<string, unknown> }) => Promise<number>;
    create: (args: { data: TCreateInput }) => Promise<T>;
    update: (args: { where: { id: string }; data: TUpdateInput }) => Promise<T>;
    delete: (args: { where: { id: string } }) => Promise<T>;
  } {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-explicit-any
    return (this.prisma as unknown as Record<string, unknown>)[this.modelName] as any;
  }

  async findById(id: string): Promise<T | null> {
    try {
      return await this.model.findUnique({
        where: { id },
      });
    } catch (error) {
      this.handleError('findById', error, { id });
      throw error;
    }
  }

  async findMany(options: QueryOptions = {}): Promise<PaginatedResult<T>> {
    try {
      const {
        where = {},
        include,
        select,
        orderBy = { createdAt: 'desc' },
        skip = 0,
        take = 10,
      } = options;

      const [data, total] = await Promise.all([
        this.model.findMany({
          where,
          include,
          select,
          orderBy,
          skip,
          take,
        }),
        this.model.count({ where }),
      ]);

      const page = Math.floor(skip / take) + 1;
      const totalPages = Math.ceil(total / take);

      return {
        data,
        total,
        page,
        pageSize: take,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      };
    } catch (error) {
      this.handleError('findMany', error, options);
      throw error;
    }
  }

  async create(data: TCreateInput): Promise<T> {
    try {
      return await this.model.create({
        data,
      });
    } catch (error) {
      this.handleError('create', error, { data });
      throw error;
    }
  }

  async update(id: string, data: TUpdateInput): Promise<T> {
    try {
      return await this.model.update({
        where: { id },
        data,
      });
    } catch (error) {
      this.handleError('update', error, { id, data });
      throw error;
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      await this.model.delete({
        where: { id },
      });
      return true;
    } catch (error) {
      if (error && typeof error === 'object' && 'code' in error && (error as { code: string }).code === 'P2025') {
        // Record not found
        return false;
      }
      this.handleError('delete', error, { id });
      throw error;
    }
  }

  async exists(id: string): Promise<boolean> {
    try {
      const count = await this.model.count({
        where: { id },
      });
      return count > 0;
    } catch (error) {
      this.handleError('exists', error, { id });
      throw error;
    }
  }

  protected handleError(operation: string, error: unknown, context: unknown): void {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(
      `Repository error in ${this.modelName}.${operation}:`,
      errorMessage,
      { context: JSON.stringify(context, null, 2) }
    );
  }
}

// Tenant Repository Interface
export interface ITenantRepository extends IBaseRepository<Tenant, TenantCreateInput, TenantUpdateInput> {
  findByName(name: string): Promise<Tenant | null>;
  findByStatus(status: string): Promise<Tenant[]>;
  updateSubscriptionTier(id: string, tier: string): Promise<Tenant>;
  getAnalytics(id: string, dateRange?: { start: Date; end: Date }): Promise<{
    _count: number;
  }>;
}

// Tenant Repository Implementation
@Injectable(DI_TOKENS.TENANT_REPOSITORY)
export class TenantRepository extends BaseRepository<Tenant, TenantCreateInput, TenantUpdateInput> 
  implements ITenantRepository {
  
  constructor(@Inject(DI_TOKENS.DATABASE) prisma: PrismaClient) {
    super(prisma, 'tenant');
  }

  async findByName(name: string): Promise<Tenant | null> {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
      return await (this.model as any).findFirst({
        where: { name },
      });
    } catch (error) {
      this.handleError('findByName', error, { name });
      throw error;
    }
  }

  async findByStatus(status: string): Promise<Tenant[]> {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
      return await (this.model as any).findMany({
        where: { status },
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      this.handleError('findByStatus', error, { status });
      throw error;
    }
  }

  async updateSubscriptionTier(id: string, tier: string): Promise<Tenant> {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
      return await (this.model as any).update({
        where: { id },
        data: { subscriptionTier: tier, updatedAt: new Date() },
      });
    } catch (error) {
      this.handleError('updateSubscriptionTier', error, { id, tier });
      throw error;
    }
  }

  async getAnalytics(id: string, dateRange?: { start: Date; end: Date }): Promise<{
    _count: number;
  }> {
    try {
      const whereClause: Record<string, unknown> = { tenantId: id };
      
      if (dateRange) {
        whereClause.createdAt = {
          gte: dateRange.start,
          lte: dateRange.end,
        };
      }

      return await this.prisma.application.aggregate({
        where: whereClause,
        _count: true,
      });
    } catch (error) {
      this.handleError('getAnalytics', error, { id, dateRange });
      throw error;
    }
  }
}

// User Repository Interface  
export interface IUserRepository extends IBaseRepository<User, UserCreateInput, UserUpdateInput> {
  findByEmail(email: string): Promise<User | null>;
  findByTenant(tenantId: string): Promise<User[]>;
  updateLastLogin(id: string): Promise<User>;
  deactivateUser(id: string): Promise<User>;
}

// User Repository Implementation
@Injectable(DI_TOKENS.USER_REPOSITORY)
export class UserRepository extends BaseRepository<User, UserCreateInput, UserUpdateInput> 
  implements IUserRepository {
  
  constructor(@Inject(DI_TOKENS.DATABASE) prisma: PrismaClient) {
    super(prisma, 'user');
  }

  async findByEmail(email: string): Promise<User | null> {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
      return await (this.model as any).findUnique({
        where: { email },
        include: { tenant: true },
      });
    } catch (error) {
      this.handleError('findByEmail', error, { email });
      throw error;
    }
  }

  async findByTenant(tenantId: string): Promise<User[]> {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
      return await (this.model as any).findMany({
        where: { tenantId },
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      this.handleError('findByTenant', error, { tenantId });
      throw error;
    }
  }

  async updateLastLogin(id: string): Promise<User> {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
      return await (this.model as any).update({
        where: { id },
        data: { lastLoginAt: new Date() },
      });
    } catch (error) {
      this.handleError('updateLastLogin', error, { id });
      throw error;
    }
  }

  async deactivateUser(id: string): Promise<User> {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
      return await (this.model as any).update({
        where: { id },
        data: { 
          isActive: false, 
          deactivatedAt: new Date(),
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      this.handleError('deactivateUser', error, { id });
      throw error;
    }
  }
}

// Application Repository Interface
export interface IApplicationRepository extends IBaseRepository<Application, ApplicationCreateInput, ApplicationUpdateInput> {
  findByTenant(tenantId: string, options?: QueryOptions): Promise<PaginatedResult<Application>>;
  findByStatus(status: string): Promise<Application[]>;
  updateStatus(id: string, status: string, notes?: string): Promise<Application>;
  getStatsByTenant(tenantId: string, dateRange?: { start: Date; end: Date }): Promise<{
    total: number;
    statusDistribution: Record<string, number>;
  }>;
}

// Application Repository Implementation
@Injectable(DI_TOKENS.APPLICATION_REPOSITORY)
export class ApplicationRepository extends BaseRepository<Application, ApplicationCreateInput, ApplicationUpdateInput> 
  implements IApplicationRepository {
  
  constructor(@Inject(DI_TOKENS.DATABASE) prisma: PrismaClient) {
    super(prisma, 'application');
  }

  async findByTenant(tenantId: string, options: QueryOptions = {}): Promise<PaginatedResult<Application>> {
    const tenantFilter = { ...options, where: { ...options.where, tenantId } };
    return await this.findMany(tenantFilter);
  }

  async findByStatus(status: string): Promise<Application[]> {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
      return await (this.model as any).findMany({
        where: { status },
        include: { tenant: { select: { name: true } } },
        orderBy: { updatedAt: 'desc' },
      });
    } catch (error) {
      this.handleError('findByStatus', error, { status });
      throw error;
    }
  }

  async updateStatus(id: string, status: string, notes?: string): Promise<Application> {
    try {
      const updateData: Record<string, unknown> = { 
        status, 
        updatedAt: new Date(),
      };

      if (notes) {
        updateData.statusNotes = notes;
      }

      // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
      return await (this.model as any).update({
        where: { id },
        data: updateData,
      });
    } catch (error) {
      this.handleError('updateStatus', error, { id, status, notes });
      throw error;
    }
  }

  async getStatsByTenant(tenantId: string, dateRange?: { start: Date; end: Date }): Promise<{
    total: number;
    statusDistribution: Record<string, number>;
  }> {
    try {
      const whereClause: Record<string, unknown> = { tenantId };
      
      if (dateRange) {
        whereClause.createdAt = {
          gte: dateRange.start,
          lte: dateRange.end,
        };
      }

      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      const [total, byStatus] = await Promise.all([
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
        (this.model as any).aggregate({
          where: whereClause,
          _count: true,
        }),
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
        (this.model as any).groupBy({
          by: ['status'],
          where: whereClause,
          _count: true,
        }),
      ]);

      return {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
        total: total._count,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
        statusDistribution: byStatus.reduce((acc: Record<string, number>, item: { status: string; _count: number }) => {
          acc[item.status] = item._count;
          return acc;
        }, {}),
      };
    } catch (error) {
      this.handleError('getStatsByTenant', error, { tenantId, dateRange });
      throw error;
    }
  }
}

/**
 * Repository Factory for dynamic repository creation
 */
@Injectable('RepositoryFactory')
export class RepositoryFactory {
  constructor(@Inject(DI_TOKENS.DATABASE) private readonly prisma: PrismaClient) {}

  createRepository<T, TCreateInput, TUpdateInput>(
    modelName: string
  ): BaseRepository<T, TCreateInput, TUpdateInput> {
    return new (class extends BaseRepository<T, TCreateInput, TUpdateInput> {
      constructor(prisma: PrismaClient) {
        super(prisma, modelName);
      }
    })(this.prisma);
  }
}

// Repositories are already exported with @Injectable decorators above