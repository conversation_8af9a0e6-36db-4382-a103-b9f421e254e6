import { Prisma } from '@prisma/client';
import { prisma } from './client';

// Tenant isolation middleware - automatically filter by tenantId for tenant-scoped models
const tenantScopedModels = [
  'User',
  'Application',
  'Document',
  'Conversation',
  'Task',
  'Message',
  'AuditLog',
  'TenantPlugin',
  'ExternalProviderCall',
  'KycVerification',
  'CreditReport',
  'BankVerification',
  'AnalyticsEvent',
  'NotificationTemplate',
  'TenantMetrics',
  'DataSubjectRequest',
  'RetentionPolicy',
  'ProviderPerformance',
  'Subscription',
  'Invoice',
  'PaymentMethod',
  'UsageEvent',
  'UsageAggregation',
];

// Get current user context (this will be set by auth middleware)
let currentUserContext: {
  userId?: string;
  tenantId?: string;
  role?: string;
  isSystemUser?: boolean;
} | null = null;

export const setUserContext = (context: typeof currentUserContext): void => {
  currentUserContext = context;
};

export const getUserContext = (): typeof currentUserContext =>
  currentUserContext;

export const clearUserContext = (): void => {
  currentUserContext = null;
};

// Tenant isolation middleware
const tenantIsolationMiddleware: Prisma.Middleware = async (
  params,
  next
): Promise<unknown> => {
  const context = getUserContext();

  // Skip tenant isolation for system users or when no context
  if (!context || context.isSystemUser || context.role === 'SUPER_ADMIN') {
    return await next(params);
  }

  // Skip tenant isolation for non-tenant scoped models
  if (!params.model || !tenantScopedModels.includes(params.model)) {
    return await next(params);
  }

  // Skip tenant isolation for SystemUser operations
  if (params.model === 'SystemUser') {
    return await next(params);
  }

  // Add tenant filter for read operations
  if (
    ['findFirst', 'findMany', 'findUnique', 'count', 'aggregate'].includes(
      params.action
    )
  ) {
    if (context.tenantId) {
      const args = (params.args as Record<string, unknown>) || {};
      const where = (args.where as Record<string, unknown>) || {};

      // Don't override if tenantId is already specified
      if (!where.tenantId) {
        where.tenantId = context.tenantId;
      }
      args.where = where;
      params.args = args;
    }
  }

  // Add tenant assignment for create operations
  if (['create'].includes(params.action)) {
    if (context.tenantId) {
      const args = (params.args as Record<string, unknown>) || {};
      const data = args.data as Record<string, unknown>;
      if (data && !data.tenantId) {
        data.tenantId = context.tenantId;
        args.data = data;
        params.args = args;
      }
    }
  }

  // Add tenant filter for update/delete operations
  if (
    ['update', 'updateMany', 'delete', 'deleteMany'].includes(params.action)
  ) {
    if (context.tenantId) {
      const args = (params.args as Record<string, unknown>) || {};
      const where = (args.where as Record<string, unknown>) || {};

      // Don't override if tenantId is already specified
      if (!where.tenantId) {
        where.tenantId = context.tenantId;
      }
      args.where = where;
      params.args = args;
    }
  }

  return await next(params);
};

// Audit logging middleware
const auditLoggingMiddleware: Prisma.Middleware = async (
  params,
  next
): Promise<unknown> => {
  const context = getUserContext();
  const result: unknown = await next(params);

  // Skip audit logging for AuditLog operations to prevent recursion
  if (params.model === 'AuditLog' || params.model === 'SystemUserAuditLog') {
    return result;
  }

  // Log significant operations
  if (
    ['create', 'update', 'delete'].includes(params.action) &&
    context?.userId
  ) {
    try {
      const auditData: {
        action: string;
        entityType: string;
        entityId: string | null;
        userId: string;
        tenantId?: string;
        metadata: {
          args: unknown;
          timestamp: string;
        };
      } = {
        action: params.action.toUpperCase(),
        entityType: params.model || 'Unknown',
        entityId: (result as { id?: string })?.id || null,
        userId: context.userId,
        metadata: {
          args: params.args,
          timestamp: new Date().toISOString(),
        },
      };

      if (context.tenantId) {
        auditData.tenantId = context.tenantId;
      }

      // Use raw Prisma client to avoid middleware recursion
      if (auditData.tenantId) {
        await prisma.auditLog.create({
          data: {
            ...auditData,
            tenantId: auditData.tenantId, // Ensure tenantId is not undefined
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            metadata: JSON.parse(JSON.stringify(auditData.metadata)),
          },
        });
      }
    } catch (error) {
      console.error('Audit logging failed:', error);
      // Don't fail the main operation if audit logging fails
    }
  }

  return result;
};

// Apply middleware to Prisma client
prisma.$use(tenantIsolationMiddleware);
prisma.$use(auditLoggingMiddleware);

export { tenantIsolationMiddleware, auditLoggingMiddleware };
