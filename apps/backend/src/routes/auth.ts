import { Router } from 'express';
import { AuthController } from '../controllers/auth';
import { authenticate } from '../middleware/auth';
import {
  validate,
  ValidationSchemas,
  validateRateLimit,
  sanitizeInput,
} from '../middleware/validation';

const router = Router();

// Apply input sanitization to all routes
router.use(sanitizeInput);

// Rate limiting for auth endpoints (stricter limits)
const authRateLimit = validateRateLimit(5, 15 * 60 * 1000); // 5 requests per 15 minutes
const generalRateLimit = validateRateLimit(100, 15 * 60 * 1000); // 100 requests per 15 minutes

/**
 * POST /api/auth/login
 * Authenticate tenant user
 */
router.post(
  '/login',
  authRateLimit,
  validate(ValidationSchemas.login),
  AuthController.login
);

/**
 * POST /api/auth/system/login
 * Authenticate system user (admin)
 */
router.post(
  '/system/login',
  authRateLimit,
  validate(ValidationSchemas.login),
  AuthController.systemLogin
);

/**
 * POST /api/auth/logout
 * Logout user and invalidate session
 */
router.post('/logout', generalRateLimit, authenticate, AuthController.logout);

/**
 * POST /api/auth/refresh
 * Refresh access token using refresh token
 */
router.post(
  '/refresh',
  authRateLimit,
  validate(ValidationSchemas.refreshToken),
  AuthController.refreshToken
);

/**
 * GET /api/auth/me
 * Get current user profile
 */
router.get('/me', generalRateLimit, authenticate, AuthController.getProfile);

/**
 * POST /api/auth/change-password
 * Change user password
 */
router.post(
  '/change-password',
  authRateLimit,
  authenticate,
  validate(ValidationSchemas.changePassword),
  AuthController.changePassword
);

export { router as authRoutes };
