/**
 * Tenant Management API Routes
 * Provides comprehensive tenant management dashboard endpoints
 */

/* eslint-disable @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access */
// ESLint disabled for express-validator usage - known compatibility issues with TypeScript strict mode

import { Router, Request, Response, NextFunction } from 'express';
// eslint-disable-next-line @typescript-eslint/no-require-imports, no-undef
const { body, param, query, validationResult } = require('express-validator');
import { TenantStatus, SubscriptionTier } from '@prisma/client';
import { tenantManagementService } from '../services/tenant-management';
import { bulkOperationsService, BulkOperationType, BulkOperationRequest } from '../services/bulk-operations';
import { tenantAnalyticsService, AnalyticsFilters } from '../services/tenant-analytics';
import { tenantCommunicationService, TemplateType, TemplateCategory, MessageStatus, TargetCriteria } from '../services/tenant-communication';
// import { auditService } from '../services/audit-enhanced';
import { systemUserAuth } from '../middleware/system-auth';

const router = Router();

// Apply system user authentication to all routes
router.use(systemUserAuth);

/**
 * Validation error handling middleware
 */
const handleValidationErrors = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
    return;
  }
  next();
};

/**
 * Get tenant list with advanced filtering and pagination
 */
router.get('/tenants',
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('search').optional().isLength({ min: 1, max: 100 }).withMessage('Search must be 1-100 characters'),
    query('status').optional().isIn(Object.values(TenantStatus)).withMessage('Invalid status'),
    query('tier').optional().isIn(Object.values(SubscriptionTier)).withMessage('Invalid subscription tier'),
    query('healthScoreMin').optional().isFloat({ min: 0, max: 100 }).withMessage('Health score min must be 0-100'),
    query('healthScoreMax').optional().isFloat({ min: 0, max: 100 }).withMessage('Health score max must be 0-100'),
    query('sortBy').optional().isIn(['name', 'createdAt', 'healthScore', 'revenue', 'lastActivity']).withMessage('Invalid sort field'),
    query('sortOrder').optional().isIn(['asc', 'desc']).withMessage('Sort order must be asc or desc'),
  ],
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const options = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 20,
        filters: {
          search: req.query.search as string,
          status: req.query.status ? [req.query.status as unknown as TenantStatus] : undefined,
          subscriptionTier: req.query.tier ? [req.query.tier as unknown as SubscriptionTier] : undefined,
          healthScoreMin: req.query.healthScoreMin ? parseFloat(req.query.healthScoreMin as string) : undefined,
          healthScoreMax: req.query.healthScoreMax ? parseFloat(req.query.healthScoreMax as string) : undefined
        },
        sortBy: (req.query.sortBy as 'name' | 'createdAt' | 'healthScore' | 'revenue' | 'lastActivity') || 'name',
        sortDirection: (req.query.sortOrder as 'asc' | 'desc') || 'asc'
      };

      const result = await tenantManagementService.getTenantList(options);

      // TODO: Add audit logging when audit service is configured

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * Get specific tenant details
 */
router.get('/tenants/:id',
  [
    param('id').isLength({ min: 5, max: 100 }).matches(/^[a-zA-Z0-9_-]+$/).withMessage('Invalid tenant ID format'),
  ],
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const tenantId = req.params.id;
      if (!tenantId) {
        res.status(400).json({ success: false, error: 'Tenant ID required' });
        return;
      }

      const tenant = await tenantManagementService.getTenantDetails(tenantId);

      // TODO: Add audit logging when audit service is configured

      res.json({
        success: true,
        data: tenant
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * Update tenant status (suspend, activate, etc.)
 */
router.patch('/tenants/:id/status',
  [
    param('id').isLength({ min: 5, max: 100 }).matches(/^[a-zA-Z0-9_-]+$/).withMessage('Invalid tenant ID format'),
    body('status').isIn(Object.values(TenantStatus)).withMessage('Invalid status value'),
    body('reason').optional().isLength({ min: 1, max: 500 }).withMessage('Reason must be 1-500 characters'),
  ],
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const tenantId = req.params.id;
      if (!tenantId) {
        res.status(400).json({ success: false, error: 'Tenant ID required' });
        return;
      }

      const { status, reason } = req.body as { status: string; reason?: string };
      const userId = (req.user as { userId?: string })?.userId || 'system';
      
      const result = await tenantManagementService.updateTenantStatus(
        tenantId,
        status as TenantStatus,
        reason || 'Status update via API',
        userId
      );

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * Update tenant subscription tier
 */
router.patch('/tenants/:id/tier',
  [
    param('id').isLength({ min: 5, max: 100 }).matches(/^[a-zA-Z0-9_-]+$/).withMessage('Invalid tenant ID format'),
    body('tier').isIn(Object.values(SubscriptionTier)).withMessage('Invalid subscription tier'),
    body('reason').optional().isLength({ min: 1, max: 500 }).withMessage('Reason must be 1-500 characters'),
  ],
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const tenantId = req.params.id;
      if (!tenantId) {
        res.status(400).json({ success: false, error: 'Tenant ID required' });
        return;
      }

      const { tier } = req.body as { tier: string };
      const userId = (req.user as { userId?: string })?.userId || 'system';
      
      const result = await tenantManagementService.updateTenantTier(
        tenantId,
        tier as SubscriptionTier,
        userId
      );

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * Get tenant analytics
 */
router.get('/tenants/:id/analytics',
  // TODO: Add validation when express-validator is configured
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const options = {
        includeComparisons: req.query.includeComparisons === 'true',
        includeTrends: req.query.includeTrends === 'true',
        dateRange: req.query.dateRangeStart && req.query.dateRangeEnd ? {
          start: new Date(req.query.dateRangeStart as string),
          end: new Date(req.query.dateRangeEnd as string)
        } : undefined
      };

      const tenantId = req.params.id;
      if (!tenantId) {
        res.status(400).json({ success: false, error: 'Tenant ID required' });
        return;
      }

      const analytics = await tenantAnalyticsService.getTenantAnalytics(tenantId, options);

      res.json({
        success: true,
        data: analytics
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * Get cross-tenant analytics
 */
router.get('/analytics/cross-tenant',
  [
    query('dateRangeStart').notEmpty().isISO8601().withMessage('Start date is required and must be valid ISO date'),
    query('dateRangeEnd').notEmpty().isISO8601().withMessage('End date is required and must be valid ISO date'),
    query('granularity').optional().isIn(['hour', 'day', 'week', 'month']).withMessage('Invalid granularity'),
  ],
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const filters = {
        tenantIds: req.query.tenantIds as string[],
        dateRange: {
          start: new Date(req.query.dateRangeStart as string),
          end: new Date(req.query.dateRangeEnd as string)
        },
        subscriptionTiers: req.query.subscriptionTiers as string[],
        regions: req.query.regions as string[]
      };

      const analytics = await tenantAnalyticsService.getCrossTenantAnalytics(filters);

      res.json({
        success: true,
        data: analytics
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * Generate analytics report
 */
router.post('/analytics/reports',
  [
    body('title').notEmpty().isLength({ min: 1, max: 200 }).withMessage('Title is required and must be 1-200 characters'),
    body('filters').notEmpty().withMessage('Filters are required'),
    body('options.format').optional().isIn(['json', 'csv', 'pdf']).withMessage('Invalid format'),
  ],
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { title, filters, options } = req.body as { title: string; filters: AnalyticsFilters; options: { includeInsights?: boolean; includeRecommendations?: boolean; format?: 'json' | 'csv' | 'pdf' } };
      
      const report = await tenantAnalyticsService.generateAnalyticsReport(title, filters, options);

      // TODO: Add audit logging when audit service is configured

      res.json({
        success: true,
        data: report
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * Export analytics report
 */
router.get('/analytics/reports/:id/export',
  // TODO: Add validation when express-validator is configured
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      // This would normally fetch the report from storage
      // For now, we'll generate a sample report
      const sampleReport = await tenantAnalyticsService.generateAnalyticsReport(
        'Sample Report',
        {
          dateRange: {
            start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            end: new Date()
          }
        }
      );

      const format = req.query.format as 'json' | 'csv' | 'pdf';
      await tenantAnalyticsService.exportReport(sampleReport, format, res);

    } catch (error) {
      next(error);
    }
  }
);

/**
 * Start bulk operation
 */
router.post('/bulk-operations',
  [
    body('operation').isIn(Object.values(BulkOperationType)).withMessage('Invalid operation type'),
    body('tenantIds').isArray({ min: 1 }).withMessage('TenantIds must be a non-empty array'),
    body('tenantIds.*').isLength({ min: 1, max: 100 }).matches(/^[a-zA-Z0-9_-]+$/).withMessage('All tenant IDs must be valid format'),
  ],
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { operation, tenantIds, parameters } = req.body as { operation: string; tenantIds: string[]; parameters?: unknown };
      
      const request: BulkOperationRequest = {
        operation: operation as BulkOperationType,
        tenantIds,
        parameters: parameters as Record<string, unknown> | undefined || undefined,
        metadata: { description: 'Bulk operation', timestamp: Date.now() }
      };
      
      const systemUserId = (req.user as { userId?: string })?.userId || 'system';
      
      const result = await bulkOperationsService.startBulkOperation(
        request,
        systemUserId
      );

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * Get bulk operation status
 */
router.get('/bulk-operations/:id',
  // TODO: Add validation when express-validator is configured
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const operationId = req.params.id;
      if (!operationId) {
        res.status(400).json({ success: false, error: 'Operation ID required' });
        return;
      }

      const progress = await bulkOperationsService.getBulkOperationProgress(operationId);

      res.json({
        success: true,
        data: progress
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * Cancel bulk operation
 */
router.delete('/bulk-operations/:id',
  // TODO: Add validation when express-validator is configured
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const operationId = req.params.id;
      if (!operationId) {
        res.status(400).json({ success: false, error: 'Operation ID required' });
        return;
      }

      const systemUserId = (req.user as { userId?: string })?.userId || 'system';
      
      const result = await bulkOperationsService.cancelBulkOperation(
        operationId,
        systemUserId
      );

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * Get communication templates
 */
router.get('/communication/templates',
  // TODO: Add validation when express-validator is configured
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const filters = {
        type: req.query.type as TemplateType,
        category: req.query.category as TemplateCategory,
        isActive: req.query.isActive ? req.query.isActive === 'true' : undefined
      };

      const templates = await tenantCommunicationService.getTemplates(filters);

      res.json({
        success: true,
        data: templates
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * Send communication to tenant
 */
router.post('/tenants/:id/communicate',
  [
    param('id').isLength({ min: 5, max: 100 }).matches(/^[a-zA-Z0-9_-]+$/).withMessage('Invalid tenant ID format'),
    body('templateId').notEmpty().isLength({ min: 1, max: 100 }).matches(/^[a-zA-Z0-9_-]+$/).withMessage('Template ID is required and must be valid format'),
    body('variables').optional().isObject().withMessage('Variables must be an object'),
  ],
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { templateId, variables } = req.body as { templateId: string; variables: Record<string, unknown> };
      
      const tenantId = req.params.id;
      if (!tenantId) {
        res.status(400).json({ success: false, error: 'Tenant ID required' });
        return;
      }

      const userId = (req.user as { userId?: string })?.userId;
      
      const result = await tenantCommunicationService.sendToTenant(
        tenantId,
        templateId,
        variables || {},
        userId
      );

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * Send bulk communication
 */
router.post('/communication/bulk',
  // TODO: Add validation when express-validator is configured
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { templateId, targetCriteria, variables, jobName } = req.body as { templateId: string; targetCriteria: TargetCriteria; variables: Record<string, unknown>; jobName: string };
      
      const result = await tenantCommunicationService.sendBulkCommunication(
        templateId,
        targetCriteria,
        variables || {},
        (req.user as { userId?: string })?.userId || 'system',
        jobName
      );

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * Get tenant communication history
 */
router.get('/tenants/:id/communication-history',
  // TODO: Add validation when express-validator is configured
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const options = {
        limit: parseInt(req.query.limit as string) || 20,
        offset: parseInt(req.query.offset as string) || 0,
        type: req.query.type as TemplateType,
        status: req.query.status as MessageStatus
      };

      const tenantId = req.params.id;
      if (!tenantId) {
        res.status(400).json({ success: false, error: 'Tenant ID required' });
        return;
      }

      const result = await tenantCommunicationService.getTenantCommunicationHistory(
        tenantId,
        options
      );

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * Get communication analytics
 */
router.get('/communication/analytics',
  // TODO: Add validation when express-validator is configured
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const filters = {
        dateRange: req.query.dateRangeStart && req.query.dateRangeEnd ? {
          start: new Date(req.query.dateRangeStart as string),
          end: new Date(req.query.dateRangeEnd as string)
        } : undefined,
        templateIds: req.query.templateIds as string[],
        tenantIds: req.query.tenantIds as string[]
      };

      const analytics = await tenantCommunicationService.getCommunicationAnalytics(filters);

      res.json({
        success: true,
        data: analytics
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * Error handling middleware
 */
router.use((error: Error, _req: Request, res: Response, _next: NextFunction) => {
  console.error('Tenant Management API Error:', error);

  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred'
  });
});

export default router;