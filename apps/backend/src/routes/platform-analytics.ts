/**
 * Platform Analytics Routes
 * Secure routes for system users to access platform-level revenue and analytics data
 * Implements Story 9.3: Platform Revenue & Analytics Dashboard
 */

import { NextFunction, Request, Response, Router } from 'express';
import { body, query } from 'express-validator';
import { PlatformAnalyticsController } from '../controllers/platform-analytics';
import { authenticate, authorize } from '../middleware/auth';
import {
    sanitizeInput,
    validate,
    validateRateLimit,
} from '../middleware/validation';

const router = Router();

// Apply input sanitization to all routes
router.use(sanitizeInput);

// Rate limiting for analytics endpoints (more generous for data-heavy operations)
const analyticsRateLimit = validateRateLimit(100, 15 * 60 * 1000); // 100 requests per 15 minutes
const exportRateLimit = validateRateLimit(10, 60 * 60 * 1000); // 10 exports per hour

// Middleware to ensure system user authentication
const requireSystemUser = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const authReq = req as any;
  if (!authReq.user || !authReq.user.isSystemUser) {
    res.status(403).json({
      success: false,
      error: 'System user access required',
    });
    return;
  }
  next();
};

/**
 * GET /api/platform/analytics/dashboard
 * Get dashboard summary with key metrics and alerts
 */
router.get(
  '/dashboard',
  analyticsRateLimit,
  authenticate,
  requireSystemUser,
  authorize(['SUPER_ADMIN', 'PLATFORM_ADMIN', 'FINANCE_ADMIN', 'ANALYTICS_VIEWER']),
  PlatformAnalyticsController.getDashboardSummary
);

/**
 * GET /api/platform/analytics/revenue
 * Get comprehensive revenue analytics (MRR, ARR, growth rates, etc.)
 */
router.get(
  '/revenue',
  analyticsRateLimit,
  authenticate,
  requireSystemUser,
  authorize(['SUPER_ADMIN', 'PLATFORM_ADMIN', 'FINANCE_ADMIN']),
  [
    query('startDate')
      .optional()
      .isISO8601()
      .withMessage('Start date must be in ISO 8601 format (YYYY-MM-DD)'),
    query('endDate')
      .optional()
      .isISO8601()
      .withMessage('End date must be in ISO 8601 format (YYYY-MM-DD)'),
  ],
  validate,
  PlatformAnalyticsController.getRevenueAnalytics
);

/**
 * GET /api/platform/analytics/metrics
 * Get platform-wide metrics (tenant counts, revenue totals, etc.)
 */
router.get(
  '/metrics',
  analyticsRateLimit,
  authenticate,
  requireSystemUser,
  authorize(['SUPER_ADMIN', 'PLATFORM_ADMIN', 'FINANCE_ADMIN', 'ANALYTICS_VIEWER']),
  [
    query('startDate')
      .optional()
      .isISO8601()
      .withMessage('Start date must be in ISO 8601 format (YYYY-MM-DD)'),
    query('endDate')
      .optional()
      .isISO8601()
      .withMessage('End date must be in ISO 8601 format (YYYY-MM-DD)'),
  ],
  validate,
  PlatformAnalyticsController.getPlatformMetrics
);

/**
 * GET /api/platform/analytics/churn-prediction
 * Get churn risk predictions for tenants
 */
router.get(
  '/churn-prediction',
  analyticsRateLimit,
  authenticate,
  requireSystemUser,
  authorize(['SUPER_ADMIN', 'PLATFORM_ADMIN', 'ANALYTICS_VIEWER']),
  [
    query('tenantIds')
      .optional()
      .custom((value) => {
        if (typeof value === 'string') {
          return true; // Single tenant ID
        }
        if (Array.isArray(value)) {
          return value.every(id => typeof id === 'string' && id.length > 0);
        }
        return false;
      })
      .withMessage('Tenant IDs must be valid strings'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Limit must be between 1 and 1000'),
  ],
  validate,
  PlatformAnalyticsController.getChurnPrediction
);

/**
 * GET /api/platform/analytics/segmentation
 * Get tenant segmentation analysis
 */
router.get(
  '/segmentation',
  analyticsRateLimit,
  authenticate,
  requireSystemUser,
  authorize(['SUPER_ADMIN', 'PLATFORM_ADMIN', 'ANALYTICS_VIEWER']),
  PlatformAnalyticsController.getTenantSegmentation
);

/**
 * POST /api/platform/analytics/executive-report
 * Generate comprehensive executive report
 */
router.post(
  '/executive-report',
  analyticsRateLimit,
  authenticate,
  requireSystemUser,
  authorize(['SUPER_ADMIN', 'PLATFORM_ADMIN']),
  [
    body('startDate')
      .notEmpty()
      .isISO8601()
      .withMessage('Start date is required and must be in ISO 8601 format'),
    body('endDate')
      .notEmpty()
      .isISO8601()
      .withMessage('End date is required and must be in ISO 8601 format'),
    body('includeForecasts')
      .optional()
      .isBoolean()
      .withMessage('Include forecasts must be a boolean'),
    body('includeSegmentation')
      .optional()
      .isBoolean()
      .withMessage('Include segmentation must be a boolean'),
    body('includeRecommendations')
      .optional()
      .isBoolean()
      .withMessage('Include recommendations must be a boolean'),
  ],
  validate,
  PlatformAnalyticsController.generateExecutiveReport
);

/**
 * GET /api/platform/analytics/export
 * Export analytics data in various formats (JSON, CSV, PDF)
 */
router.get(
  '/export',
  exportRateLimit,
  authenticate,
  requireSystemUser,
  authorize(['SUPER_ADMIN', 'PLATFORM_ADMIN', 'FINANCE_ADMIN']),
  [
    query('type')
      .optional()
      .isIn(['revenue', 'metrics', 'segmentation', 'churn'])
      .withMessage('Type must be one of: revenue, metrics, segmentation, churn'),
    query('format')
      .optional()
      .isIn(['json', 'csv', 'pdf'])
      .withMessage('Format must be one of: json, csv, pdf'),
    query('startDate')
      .optional()
      .isISO8601()
      .withMessage('Start date must be in ISO 8601 format'),
    query('endDate')
      .optional()
      .isISO8601()
      .withMessage('End date must be in ISO 8601 format'),
    query('filename')
      .optional()
      .isLength({ min: 1, max: 100 })
      .matches(/^[a-zA-Z0-9_-]+$/)
      .withMessage('Filename must be 1-100 characters and contain only letters, numbers, hyphens, and underscores'),
  ],
  validate,
  PlatformAnalyticsController.exportAnalyticsData
);

/**
 * Error handling middleware for analytics routes
 */
router.use((error: any, req: Request, res: Response, _next: NextFunction) => {
  console.error('Platform Analytics Route Error:', error);

  // Handle specific error types
  if (error.name === 'ValidationError') {
    res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: error.message,
    });
    return;
  }

  if (error.name === 'UnauthorizedError') {
    res.status(401).json({
      success: false,
      error: 'Authentication required',
    });
    return;
  }

  if (error.name === 'ForbiddenError') {
    res.status(403).json({
      success: false,
      error: 'Insufficient permissions',
    });
    return;
  }

  // Database errors
  if (error.code === 'P2002') {
    res.status(409).json({
      success: false,
      error: 'Data conflict',
    });
    return;
  }

  if (error.code === 'P2025') {
    res.status(404).json({
      success: false,
      error: 'Resource not found',
    });
    return;
  }

  // Rate limiting errors
  if (error.status === 429) {
    res.status(429).json({
      success: false,
      error: 'Rate limit exceeded',
      message: 'Too many requests. Please try again later.',
    });
    return;
  }

  // Generic server error
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'An unexpected error occurred',
  });
});

export { router as platformAnalyticsRoutes };
