import { Router } from 'express';
import { UsersController } from '../controllers/users';
import {
  authenticate,
  authorize,
  requireTenantAccess,
} from '../middleware/auth';
import {
  validate,
  ValidationSchemas,
  validateRateLimit,
  sanitizeInput,
} from '../middleware/validation';

const router = Router();

// Apply input sanitization to all routes
router.use(sanitizeInput);

// Rate limiting for user endpoints
const userRateLimit = validateRateLimit(50, 15 * 60 * 1000); // 50 requests per 15 minutes
const registrationRateLimit = validateRateLimit(3, 60 * 60 * 1000); // 3 registrations per hour

/**
 * POST /api/users/register
 * Register new tenant user
 */
router.post(
  '/register',
  registrationRateLimit,
  validate(ValidationSchemas.userRegistration),
  UsersController.register
);

/**
 * GET /api/users/profile
 * Get user profile with detailed information
 */
router.get(
  '/profile',
  userRateLimit,
  authenticate,
  requireTenantAccess,
  UsersController.getProfile
);

/**
 * PUT /api/users/profile
 * Update user profile
 */
router.put(
  '/profile',
  userRateLimit,
  authenticate,
  requireTenantAccess,
  validate(ValidationSchemas.updateProfile),
  UsersController.updateProfile
);

/**
 * GET /api/users
 * List users in tenant (admin only)
 */
router.get(
  '/',
  userRateLimit,
  authenticate,
  authorize(['ADMIN', 'OWNER']),
  requireTenantAccess,
  validate(ValidationSchemas.pagination),
  UsersController.listUsers
);

/**
 * PUT /api/users/:userId/role
 * Update user role (admin only)
 */
router.put(
  '/:userId/role',
  userRateLimit,
  authenticate,
  authorize(['ADMIN', 'OWNER']),
  requireTenantAccess,
  validate(ValidationSchemas.updateUserRole),
  UsersController.updateUserRole
);

export { router as usersRoutes };
