import { Router, Request, Response, NextFunction } from 'express';
import {
  SystemUsersController,
  SystemAuthRequest,
} from '../controllers/system-users';
import { authenticate, authorize } from '../middleware/auth';
import {
  validate,
  ValidationSchemas,
  validateRateLimit,
  sanitizeInput,
} from '../middleware/validation';

const router = Router();

// Apply input sanitization to all routes
router.use(sanitizeInput);

// Rate limiting for system user endpoints (stricter limits)
const systemUserRateLimit = validateRateLimit(20, 15 * 60 * 1000); // 20 requests per 15 minutes

// Middleware to ensure system user authentication
const requireSystemUser = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const authReq = req as SystemAuthRequest;
  if (!authReq.user || !authReq.user.isSystemUser) {
    res.status(403).json({
      success: false,
      error: 'System user access required',
    });
    return;
  }
  next();
};

/**
 * POST /api/system/users
 * Create new system user (super admin only)
 */
router.post(
  '/',
  systemUserRateLimit,
  authenticate,
  requireSystemUser,
  authorize(['SUPER_ADMIN']),
  validate(ValidationSchemas.createSystemUser),
  SystemUsersController.createSystemUser
);

/**
 * GET /api/system/users
 * List system users (admin only)
 */
router.get(
  '/',
  systemUserRateLimit,
  authenticate,
  requireSystemUser,
  authorize(['SUPER_ADMIN', 'PLATFORM_ADMIN']),
  validate(ValidationSchemas.pagination),
  SystemUsersController.listSystemUsers
);

/**
 * GET /api/system/users/:userId
 * Get system user details
 */
router.get(
  '/:userId',
  systemUserRateLimit,
  authenticate,
  requireSystemUser,
  authorize(['SUPER_ADMIN', 'PLATFORM_ADMIN']),
  validate(ValidationSchemas.userIdParam),
  SystemUsersController.getSystemUser
);

/**
 * PUT /api/system/users/:userId
 * Update system user (super admin only)
 */
router.put(
  '/:userId',
  systemUserRateLimit,
  authenticate,
  requireSystemUser,
  authorize(['SUPER_ADMIN']),
  validate(ValidationSchemas.updateSystemUser),
  SystemUsersController.updateSystemUser
);

/**
 * DELETE /api/system/users/:userId
 * Delete system user (super admin only)
 */
router.delete(
  '/:userId',
  systemUserRateLimit,
  authenticate,
  requireSystemUser,
  authorize(['SUPER_ADMIN']),
  validate(ValidationSchemas.userIdParam),
  SystemUsersController.deleteSystemUser
);

/**
 * GET /api/system/audit-logs
 * Get system audit logs (admin only)
 */
router.get(
  '/audit-logs',
  systemUserRateLimit,
  authenticate,
  requireSystemUser,
  authorize(['SUPER_ADMIN', 'PLATFORM_ADMIN']),
  SystemUsersController.getAuditLogs
);

export { router as systemUsersRoutes };
