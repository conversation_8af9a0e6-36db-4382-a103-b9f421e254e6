import { Router } from 'express';
import { authRoutes } from './auth';
import { usersRoutes } from './users';
import { systemUsersRoutes } from './system-users';
import tenantManagementRoutes from './tenant-management';
// import { billingRoutes } from '../billing';
import { ApiResponse } from '@underwriting/shared';

const router = Router();

/**
 * API v1 routes
 */

// Authentication routes
router.use('/auth', authRoutes);

// User management routes
router.use('/users', usersRoutes);

// System user management routes
router.use('/system/users', systemUsersRoutes);

// Platform management routes (cross-tenant management)
router.use('/platform/tenants', tenantManagementRoutes);

// Billing and subscription management routes - temporarily disabled
// router.use('/subscriptions', billingRoutes.subscriptions);
// router.use('/billing', billingRoutes.payments);
// router.use('/billing/invoices', billingRoutes.invoices);

// API documentation endpoint
router.get('/', (_req, res) => {
  const response: ApiResponse = {
    success: true,
    data: {
      message: 'AI Underwriting Platform API v1',
      version: '1.0.0',
      endpoints: {
        authentication: {
          'POST /api/auth/login': 'Authenticate tenant user',
          'POST /api/auth/system/login': 'Authenticate system user',
          'POST /api/auth/logout': 'Logout user',
          'POST /api/auth/refresh': 'Refresh access token',
          'GET /api/auth/me': 'Get current user profile',
          'POST /api/auth/change-password': 'Change user password',
        },
        users: {
          'POST /api/users/register': 'Register new tenant user',
          'GET /api/users/profile': 'Get detailed user profile',
          'PUT /api/users/profile': 'Update user profile',
          'GET /api/users': 'List users in tenant (admin only)',
          'PUT /api/users/:userId/role': 'Update user role (admin only)',
        },
        systemUsers: {
          'POST /api/system/users': 'Create system user (super admin only)',
          'GET /api/system/users': 'List system users (admin only)',
          'GET /api/system/users/:userId': 'Get system user details',
          'PUT /api/system/users/:userId':
            'Update system user (super admin only)',
          'DELETE /api/system/users/:userId':
            'Delete system user (super admin only)',
          'GET /api/system/audit-logs': 'Get system audit logs (admin only)',
        },
        platformTenants: {
          'GET /api/platform/tenants/search': 'Search and filter tenants',
          'GET /api/platform/tenants/:tenantId': 'Get tenant details',
          'POST /api/platform/tenants/:tenantId/action':
            'Perform tenant action',
          'POST /api/platform/tenants/bulk-operation':
            'Execute bulk operations',
          'GET /api/platform/tenants/bulk-operation/:operationId':
            'Get bulk operation status',
          'GET /api/platform/tenants/:tenantId/health':
            'Get tenant health metrics',
          'GET /api/platform/analytics': 'Get cross-tenant analytics',
          'POST /api/platform/tenants/export': 'Export tenant data',
          'GET /api/platform/tenants/:tenantId/activity': 'Get tenant activity',
        },
        subscriptions: {
          'POST /api/subscriptions': 'Create new subscription',
          'GET /api/subscriptions/config/tiers':
            'Get subscription tiers and pricing',
          'GET /api/subscriptions/:id': 'Get subscription details',
          'GET /api/subscriptions/tenant/:tenantId': 'Get tenant subscription',
          'PUT /api/subscriptions/:id/upgrade':
            'Upgrade/downgrade subscription',
          'GET /api/subscriptions/:id/usage-quota': 'Get usage quota',
          'POST /api/subscriptions/track-usage': 'Track usage events',
          'GET /api/subscriptions/:id/metrics': 'Get subscription metrics',
          'GET /api/subscriptions/:id/validate': 'Validate subscription',
        },
        payments: {
          'POST /api/billing/payment-methods': 'Create payment method',
          'GET /api/billing/payment-methods': 'List payment methods',
          'DELETE /api/billing/payment-methods/:id': 'Delete payment method',
          'POST /api/billing/payments': 'Process payment',
          'POST /api/billing/payments/:id/retry': 'Retry failed payment',
          'GET /api/billing/payments': 'List payments',
          'POST /api/billing/webhooks/:provider': 'Handle payment webhooks',
          'GET /api/billing/health': 'Get payment gateway health',
          'POST /api/billing/reconcile': 'Reconcile payments (admin only)',
        },
        invoices: {
          'POST /api/billing/invoices/generate':
            'Generate invoice for subscription',
          'GET /api/billing/invoices': 'List invoices with pagination',
          'GET /api/billing/invoices/metrics':
            'Get invoice metrics and analytics',
          'GET /api/billing/invoices/:id': 'Get invoice details',
          'PUT /api/billing/invoices/:id/status': 'Update invoice status',
          'POST /api/billing/invoices/:id/adjustments':
            'Add adjustment to invoice',
          'POST /api/billing/invoices/:id/pdf': 'Generate PDF for invoice',
          'GET /api/billing/invoices/:id/download': 'Download invoice PDF',
          'POST /api/billing/invoices/:id/deliver': 'Deliver invoice via email',
          'POST /api/billing/invoices/process-monthly':
            'Process monthly invoicing (admin only)',
        },
      },
      rateLimit: {
        auth: '5 requests per 15 minutes',
        registration: '3 requests per hour',
        general: '50-100 requests per 15 minutes',
        system: '20 requests per 15 minutes',
      },
      security: {
        authentication: 'JWT Bearer tokens',
        authorization: 'Role-based access control',
        inputValidation: 'Comprehensive validation and sanitization',
        auditLogging: 'All operations logged',
        rateLimiting: 'Per-endpoint rate limits',
      },
    },
  };
  res.status(200).json(response);
});

// Health check for API routes
router.get('/health', (_req, res) => {
  const response: ApiResponse = {
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      api: 'v1',
    },
  };
  res.status(200).json(response);
});

export { router as apiRoutes };
