/**
 * Tenant Domain Service
 * Pure business logic separated from infrastructure concerns
 * Implements Domain-Driven Design principles with enterprise architectural patterns
 */

import { Injectable, Inject } from '../../core/di-container';
import { ITenantRepository, IUserRepository } from '../../core/repository';
import { DI_TOKENS } from '../../core/di-container';
import { ErrorHandlingService } from '../../services/error-handling';
// Prisma types will be imported when client is properly generated

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type TenantCreateInput = any;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type TenantUpdateInput = any;

// Domain Value Objects
export interface TenantBusinessRules {
  maxUsers: number;
  maxApplications: number;
  allowedFeatures: string[];
  subscriptionLimits: SubscriptionLimits;
}

export interface SubscriptionLimits {
  maxStorageGB: number;
  maxAPICallsPerMonth: number;
  supportLevel: 'basic' | 'premium' | 'enterprise';
  customBranding: boolean;
}

// Domain Events
export interface TenantDomainEvent {
  type: 'TenantCreated' | 'TenantSuspended' | 'TenantReactivated' | 'SubscriptionChanged';
  tenantId: string;
  timestamp: Date;
  data: Record<string, unknown>;
}

// Tenant Aggregate Root
export interface TenantAggregate {
  id: string;
  name: string;
  status: 'ACTIVE' | 'SUSPENDED' | 'INACTIVE';
  subscriptionTier: 'STARTER' | 'PROFESSIONAL' | 'ENTERPRISE';
  businessRules: TenantBusinessRules;
  createdAt: Date;
  updatedAt: Date;
  
  // Domain methods
  canCreateUser(): boolean;
  canSubmitApplication(): boolean;
  canAccessFeature(feature: string): boolean;
  suspend(reason: string): TenantDomainEvent;
  reactivate(): TenantDomainEvent;
  upgradeSubscription(newTier: string): TenantDomainEvent;
}

/**
 * Tenant Domain Service
 * Contains core business logic for tenant management
 * Separated from infrastructure and application concerns
 */
@Injectable(DI_TOKENS.TENANT_SERVICE)
export class TenantDomainService {
  constructor(
    @Inject(DI_TOKENS.TENANT_REPOSITORY) private readonly tenantRepo: ITenantRepository,
    @Inject(DI_TOKENS.USER_REPOSITORY) private readonly userRepo: IUserRepository
  ) {}

  /**
   * Create new tenant with business rules validation
   */
  async createTenant(request: {
    name: string;
    ownerEmail: string;
    subscriptionTier: string;
    initialFeatures?: string[];
  }): Promise<TenantAggregate> {
    // Business rule: Validate tenant name uniqueness
    const existingTenant = await this.tenantRepo.findByName(request.name);
    if (existingTenant) {
      throw ErrorHandlingService.createConflictError(
        `Tenant with name "${request.name}" already exists`,
        'Tenant name must be unique'
      );
    }

    // Business rule: Validate subscription tier
    const validTiers = ['STARTER', 'PROFESSIONAL', 'ENTERPRISE'];
    if (!validTiers.includes(request.subscriptionTier)) {
      throw ErrorHandlingService.createValidationError(
        `Invalid subscription tier: ${request.subscriptionTier}`,
        'Please select a valid subscription plan'
      );
    }

    // Apply business rules based on subscription
    const businessRules = this.getBusinessRulesForTier(request.subscriptionTier);
    
    // Create tenant aggregate
    const tenantData = {
      name: request.name,
      status: 'ACTIVE',
      subscriptionTier: request.subscriptionTier,
      businessRules,
      ownerEmail: request.ownerEmail,
      features: request.initialFeatures || businessRules.allowedFeatures,
    };

    const tenant = await this.tenantRepo.create(tenantData as unknown as TenantCreateInput);
    
    // Emit domain event
    await this.emitDomainEvent({
      type: 'TenantCreated',
      tenantId: tenant.id,
      timestamp: new Date(),
      data: { tenant, ownerEmail: request.ownerEmail },
    });

    return this.mapToAggregate(tenant);
  }

  /**
   * Suspend tenant with business validation
   */
  async suspendTenant(tenantId: string, reason: string, requesterId: string): Promise<void> {
    const tenant = await this.tenantRepo.findById(tenantId);
    if (!tenant) {
      throw ErrorHandlingService.createNotFoundError(
        `Tenant ${tenantId}`,
        'Tenant not found'
      );
    }

    // Business rule: Cannot suspend already suspended tenant
    if (tenant.status === 'SUSPENDED') {
      throw ErrorHandlingService.createConflictError(
        'Tenant is already suspended',
        'Tenant is already in suspended state'
      );
    }

    // Business rule: Validate suspension reason
    if (!reason || reason.trim().length < 10) {
      throw ErrorHandlingService.createValidationError(
        'Suspension reason must be at least 10 characters',
        'Please provide a detailed suspension reason'
      );
    }

    // Update tenant status
    await this.tenantRepo.update(tenantId, {
      status: 'SUSPENDED',
      suspensionReason: reason,
      suspendedBy: requesterId,
      suspendedAt: new Date(),
    } as unknown as TenantUpdateInput);

    // Deactivate all tenant users
    const users = await this.userRepo.findByTenant(tenantId);
    await Promise.all(
      users.map(user => this.userRepo.deactivateUser(user.id))
    );

    // Emit domain event
    await this.emitDomainEvent({
      type: 'TenantSuspended',
      tenantId,
      timestamp: new Date(),
      data: { reason, requesterId },
    });
  }

  /**
   * Reactivate suspended tenant
   */
  async reactivateTenant(tenantId: string, requesterId: string): Promise<void> {
    const tenant = await this.tenantRepo.findById(tenantId);
    if (!tenant) {
      throw ErrorHandlingService.createNotFoundError(
        `Tenant ${tenantId}`,
        'Tenant not found'
      );
    }

    // Business rule: Can only reactivate suspended tenants
    if (tenant.status !== 'SUSPENDED') {
      throw ErrorHandlingService.createConflictError(
        'Only suspended tenants can be reactivated',
        'Tenant is not in suspended state'
      );
    }

    // Update tenant status
    await this.tenantRepo.update(tenantId, {
      status: 'ACTIVE',
      suspensionReason: null,
      suspendedBy: null,
      suspendedAt: null,
      reactivatedBy: requesterId,
      reactivatedAt: new Date(),
    } as unknown as TenantUpdateInput);

    // Emit domain event
    await this.emitDomainEvent({
      type: 'TenantReactivated',
      tenantId,
      timestamp: new Date(),
      data: { requesterId },
    });
  }

  /**
   * Upgrade tenant subscription with validation
   */
  async upgradeSubscription(
    tenantId: string,
    newTier: string,
    requesterId: string
  ): Promise<void> {
    const tenant = await this.tenantRepo.findById(tenantId);
    if (!tenant) {
      throw ErrorHandlingService.createNotFoundError(
        `Tenant ${tenantId}`,
        'Tenant not found'
      );
    }

    // Business rule: Validate upgrade path
    const upgradeMatrix = {
      STARTER: ['PROFESSIONAL', 'ENTERPRISE'],
      PROFESSIONAL: ['ENTERPRISE'],
      ENTERPRISE: [], // Cannot upgrade from enterprise
    };

    const allowedUpgrades = upgradeMatrix[tenant.subscriptionTier as keyof typeof upgradeMatrix] || [];
    if (!allowedUpgrades.includes(newTier as never)) {
      throw ErrorHandlingService.createValidationError(
        `Cannot upgrade from ${tenant.subscriptionTier} to ${newTier}`,
        'Invalid subscription upgrade path'
      );
    }

    // Apply new business rules
    const newBusinessRules = this.getBusinessRulesForTier(newTier);
    
    await this.tenantRepo.updateSubscriptionTier(tenantId, newTier);
    await this.tenantRepo.update(tenantId, {
      businessRules: newBusinessRules,
      subscriptionUpgradedAt: new Date(),
      subscriptionUpgradedBy: requesterId,
    } as unknown as TenantUpdateInput);

    // Emit domain event
    await this.emitDomainEvent({
      type: 'SubscriptionChanged',
      tenantId,
      timestamp: new Date(),
      data: { oldTier: tenant.subscriptionTier, newTier, requesterId },
    });
  }

  /**
   * Validate if tenant can perform action
   */
  async validateTenantAction(tenantId: string, action: string): Promise<boolean> {
    const tenant = await this.tenantRepo.findById(tenantId);
    if (!tenant) {
      return false;
    }

    // Business rule: Suspended tenants cannot perform actions
    if (tenant.status === 'SUSPENDED') {
      return false;
    }

    const aggregate = this.mapToAggregate(tenant);
    
    switch (action) {
      case 'CREATE_USER':
        return aggregate.canCreateUser();
      case 'SUBMIT_APPLICATION':
        return aggregate.canSubmitApplication();
      default:
        return aggregate.canAccessFeature(action);
    }
  }

  /**
   * Get business rules for subscription tier
   */
  private getBusinessRulesForTier(tier: string): TenantBusinessRules {
    const businessRules = {
      STARTER: {
        maxUsers: 5,
        maxApplications: 100,
        allowedFeatures: ['basic_underwriting', 'email_notifications'],
        subscriptionLimits: {
          maxStorageGB: 1,
          maxAPICallsPerMonth: 1000,
          supportLevel: 'basic' as const,
          customBranding: false,
        },
      },
      PROFESSIONAL: {
        maxUsers: 25,
        maxApplications: 1000,
        allowedFeatures: ['basic_underwriting', 'advanced_analytics', 'email_notifications', 'api_access'],
        subscriptionLimits: {
          maxStorageGB: 10,
          maxAPICallsPerMonth: 10000,
          supportLevel: 'premium' as const,
          customBranding: true,
        },
      },
      ENTERPRISE: {
        maxUsers: 999999, // Unlimited
        maxApplications: 999999, // Unlimited
        allowedFeatures: ['*'], // All features
        subscriptionLimits: {
          maxStorageGB: 999999, // Unlimited
          maxAPICallsPerMonth: 999999, // Unlimited
          supportLevel: 'enterprise' as const,
          customBranding: true,
        },
      },
    };

    return businessRules[tier as keyof typeof businessRules] || businessRules.STARTER;
  }

  /**
   * Map database entity to domain aggregate
   */
  private mapToAggregate(tenant: {
    id: string;
    name: string;
    status: string;
    subscriptionTier: string;
    businessRules?: TenantBusinessRules;
    createdAt: Date;
    updatedAt: Date;
  }): TenantAggregate {
    return {
      id: tenant.id,
      name: tenant.name,
      status: tenant.status as 'ACTIVE' | 'SUSPENDED' | 'INACTIVE',
      subscriptionTier: tenant.subscriptionTier as 'STARTER' | 'PROFESSIONAL' | 'ENTERPRISE',
      businessRules: tenant.businessRules || this.getBusinessRulesForTier(tenant.subscriptionTier),
      createdAt: tenant.createdAt,
      updatedAt: tenant.updatedAt,

      canCreateUser: function(): boolean {
        return this.status === 'ACTIVE' && this.businessRules.maxUsers > 0;
      },

      canSubmitApplication: function(): boolean {
        return this.status === 'ACTIVE' && this.businessRules.maxApplications > 0;
      },

      canAccessFeature: function(feature: string): boolean {
        return this.status === 'ACTIVE' && 
               (this.businessRules.allowedFeatures.includes('*') || 
                this.businessRules.allowedFeatures.includes(feature));
      },

      suspend: function(reason: string): TenantDomainEvent {
        return {
          type: 'TenantSuspended',
          tenantId: this.id,
          timestamp: new Date(),
          data: { reason },
        };
      },

      reactivate: function(): TenantDomainEvent {
        return {
          type: 'TenantReactivated',
          tenantId: this.id,
          timestamp: new Date(),
          data: {},
        };
      },

      upgradeSubscription: function(newTier: string): TenantDomainEvent {
        return {
          type: 'SubscriptionChanged',
          tenantId: this.id,
          timestamp: new Date(),
          data: { oldTier: this.subscriptionTier, newTier },
        };
      },
    };
  }

  /**
   * Emit domain events (placeholder for event system)
   */
  private async emitDomainEvent(event: TenantDomainEvent): Promise<void> {
    // TODO: Integrate with event bus/messaging system
    console.log(`Domain Event: ${event.type}`, {
      tenantId: event.tenantId,
      timestamp: event.timestamp,
      data: event.data,
    });
  }
}