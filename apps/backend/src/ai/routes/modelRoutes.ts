/**
 * AI Model Configuration Routes
 *
 * Express routes for AI model management API endpoints.
 */

import { Router, Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import { ModelConfigController } from '../controllers/ModelConfigController';
import { AiModelManager } from '../services/AiModelManager';

/**
 * TypeScript interfaces for proper type safety
 */

/**
 * Create model configuration routes
 */
export function createModelRoutes(
  prisma: PrismaClient,
  modelManager: AiModelManager
): Router {
  const router = Router();
  const controller = new ModelConfigController(prisma, modelManager);

  // Model Configuration CRUD
  router.get('/configurations', controller.getModelConfigurations);
  router.get('/configurations/:id', controller.getModelConfiguration);
  router.post('/configurations', controller.createModelConfiguration);
  router.put('/configurations/:id', controller.updateModelConfiguration);
  router.delete('/configurations/:id', controller.deleteModelConfiguration);

  // Model Selection
  router.post('/select', controller.selectModel);

  // Performance and Metrics
  router.get('/metrics', controller.getModelMetrics);
  router.get('/usage-statistics', controller.getUsageStatistics);

  // Fallback Rules
  router.get('/fallback-rules', controller.getFallbackRules);
  router.post('/fallback-rules', controller.createFallbackRule);

  // Audit and Monitoring
  router.get('/audit-logs', controller.getAuditLogs);

  return router;
}

/**
 * Authentication and authorization middleware placeholder
 * In a real implementation, this would verify JWT tokens and check permissions
 */
export const authMiddleware = (
  req: Request, 
  _res: Response, 
  next: NextFunction
): void => {
  // Placeholder - extract user from JWT token
  req.user = {
    userId: 'user_123',
    email: '<EMAIL>',
    tenantId: 'tenant_456',
    role: 'admin',
    sessionId: 'session_123',
  };
  next();
};

/**
 * Admin-only middleware
 */
export const adminMiddleware = (
  req: Request, 
  res: Response, 
  next: NextFunction
): void => {
  if (req.user?.role !== 'admin') {
    res.status(403).json({
      success: false,
      error: 'Admin access required',
    });
    return;
  }
  next();
};