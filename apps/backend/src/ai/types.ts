/**
 * AI Service Types and Interfaces
 *
 * Defines the types and interfaces for AI model integration,
 * supporting multiple providers and models through the plugin architecture.
 */

// import { PluginConfig, PluginResult } from '../plugins/types';

// Temporary stubs
interface PluginConfig {
  provider: string;
  modelId: string;
}

interface PluginResult<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * AI Model Configuration
 */
export interface AiModelConfig extends PluginConfig {
  // Model Identity
  provider: string; // openrouter, ollama, azure, aws, etc.
  modelId: string; // Provider-specific model identifier
  version?: string;

  // Model Parameters
  temperature?: number; // 0.0 - 2.0, creativity/randomness
  maxTokens?: number; // Maximum tokens in response
  topP?: number; // 0.0 - 1.0, nucleus sampling
  topK?: number; // Top-k sampling
  frequencyPenalty?: number; // -2.0 - 2.0, reduce repetition
  presencePenalty?: number; // -2.0 - 2.0, encourage new topics

  // Custom parameters for specific providers
  customParameters?: Record<string, unknown>;

  // System behavior
  systemPrompt?: string;
  stopSequences?: string[];

  // Cost and usage limits
  costPerInputToken?: number;
  costPerOutputToken?: number;
  dailyCostLimit?: number;
  monthlyTokenLimit?: number;
}

/**
 * AI Request and Response Types
 */
export interface AiMessage {
  role: 'system' | 'user' | 'assistant' | 'function';
  content: string;
  name?: string; // For function calls
  functionCall?: {
    name: string;
    arguments: string;
  };
}

export interface AiRequest {
  messages: AiMessage[];
  model?: string; // Override default model
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  topK?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stopSequences?: string[];
  functions?: AiFunction[];
  functionCall?: 'auto' | 'none' | { name: string };
  modelCriteria?: ModelSelectionCriteria; // For model selection

  // Context and metadata
  context?: {
    tenantId?: string;
    userId?: string;
    applicationId?: string;
    conversationId?: string;
    scenario?: AiScenario;
    requestId?: string;
    timestamp?: Date;
    environment?: string;
  };
}

export interface AiResponse {
  id: string;
  model: string;
  choices: AiChoice[];
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  cost?: {
    inputCost: number;
    outputCost: number;
    totalCost: number;
  };
  metadata?: {
    provider: string;
    responseTime: number;
    timestamp: Date;
  };
}

export interface AiChoice {
  index: number;
  message: AiMessage;
  finishReason: 'stop' | 'length' | 'function_call' | 'content_filter' | 'null';
}

export interface AiFunction {
  name: string;
  description: string;
  parameters: Record<string, unknown>; // JSON Schema
}

/**
 * AI Scenarios for different use cases
 */
export enum AiScenario {
  UNDERWRITING_ANALYSIS = 'underwriting_analysis',
  RISK_ASSESSMENT = 'risk_assessment',
  DOCUMENT_PROCESSING = 'document_processing',
  CONVERSATION_CHAT = 'conversation_chat',
  DATA_EXTRACTION = 'data_extraction',
  DECISION_EXPLANATION = 'decision_explanation',
  COMPLIANCE_CHECK = 'compliance_check',
  FRAUD_DETECTION = 'fraud_detection',
  CUSTOM = 'custom',
}

/**
 * Model Selection Criteria
 */
export interface ModelSelectionCriteria {
  scenario: AiScenario;
  tenantId?: string;
  maxCost?: number;
  maxLatency?: number;
  requiredCapabilities?: string[];
  excludeModels?: string[];
  preferredProviders?: string[];
}

/**
 * Model Selection Result
 */
export interface ModelSelectionResult {
  modelConfig: AiModelConfig;
  reason: string;
  alternatives: AiModelConfig[];
  estimatedCost: number;
  estimatedLatency: number;
}

/**
 * AI Service Plugin Interface
 */
export interface IAiServicePlugin {
  /**
   * Send a request to the AI model
   */
  chat(request: AiRequest): Promise<PluginResult<AiResponse>>;

  /**
   * Get available models from this provider
   */
  getAvailableModels(): Promise<PluginResult<string[]>>;

  /**
   * Validate model configuration
   */
  validateModelConfig(config: AiModelConfig): Promise<PluginResult<boolean>>;

  /**
   * Get model pricing information
   */
  getModelPricing(modelId: string): Promise<
    PluginResult<{
      inputTokenPrice: number;
      outputTokenPrice: number;
    }>
  >;
}

/**
 * Provider-specific configurations
 */
export interface OpenRouterConfig extends AiModelConfig {
  provider: 'openrouter';
  apiKey: string;
  baseUrl?: string;
  httpReferer?: string;
  xTitle?: string;
}

export interface OllamaConfig extends AiModelConfig {
  provider: 'ollama';
  baseUrl?: string;
  keepAlive?: string;
}

export interface AzureOpenAIConfig extends AiModelConfig {
  provider: 'azure';
  apiKey: string;
  endpoint: string;
  apiVersion?: string;
  deploymentName: string;
}

/**
 * Error types for AI services
 */
export enum AiErrorCode {
  MODEL_NOT_FOUND = 'MODEL_NOT_FOUND',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  INVALID_REQUEST = 'INVALID_REQUEST',
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
  MODEL_OVERLOADED = 'MODEL_OVERLOADED',
  CONTENT_FILTERED = 'CONTENT_FILTERED',
  CONTEXT_LENGTH_EXCEEDED = 'CONTEXT_LENGTH_EXCEEDED',
  FUNCTION_CALL_FAILED = 'FUNCTION_CALL_FAILED',
}

export interface AiError extends Error {
  code: AiErrorCode;
  provider?: string;
  model?: string;
  retryable: boolean;
  details?: Record<string, unknown>;
}

/**
 * AI Service Events for monitoring and analytics
 */
export interface AiServiceEvent {
  type: 'request' | 'response' | 'error' | 'fallback' | 'cost_alert';
  provider: string;
  model: string;
  tenantId?: string;
  userId?: string;
  scenario?: AiScenario;

  // Performance data
  responseTime?: number;
  tokenUsage?: {
    input: number;
    output: number;
    total: number;
  };
  cost?: number;

  // Error data
  error?: {
    code: AiErrorCode;
    message: string;
    retryable: boolean;
  };

  timestamp: Date;
  metadata?: Record<string, unknown>;
}

/**
 * Fallback strategy configuration
 */
export interface FallbackStrategy {
  enabled: boolean;
  maxAttempts: number;
  fallbackModels: string[];
  conditions: {
    onTimeout: boolean;
    onError: boolean;
    onCostLimit: boolean;
    onQualityThreshold: boolean;
  };
  backoffStrategy: 'linear' | 'exponential' | 'custom';
  baseDelayMs: number;
}
