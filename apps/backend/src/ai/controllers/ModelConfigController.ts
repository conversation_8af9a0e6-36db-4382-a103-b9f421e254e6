/**
 * Model Configuration Controller
 *
 * REST API endpoints for managing AI model configurations, including
 * CRUD operations, model selection, and performance monitoring.
 */

import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import <PERSON><PERSON> from 'joi';
import { AiModelManager } from '../services/AiModelManager';
import { AiModelConfig, AiScenario, ModelSelectionCriteria } from '../types';

/**
 * TypeScript interfaces for proper type safety
 */

interface AuditRecord {
  [key: string]: unknown;
}

interface ModelConfigUpdateData {
  name?: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  topK?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  timeoutMs?: number;
  retryAttempts?: number;
  retryDelayMs?: number;
  costPerInputToken?: string;
  costPerOutputToken?: string;
  dailyCostLimit?: string;
  monthlyTokenLimit?: number;
  usageScenarios?: string[];
  priority?: number;
  isActive?: boolean;
  isDefault?: boolean;
  customParameters?: unknown;
  systemPrompt?: string;
  stopSequences?: string[];
  description?: string;
  tags?: string[];
}

/**
 * Helper to convert data to Prisma JSON compatible format
 * Uses proper Prisma InputJsonValue type
 */
function toPrismaJson(data: unknown): object | string | number | boolean | undefined {
  if (data === null || data === undefined) {
    return undefined;
  }
  
  try {
    // Ensure JSON serializable - filter out null values
    const jsonString = JSON.stringify(data);
    const serialized: unknown = JSON.parse(jsonString);
    
    // Return undefined if the result is null to avoid Prisma type issues
    if (serialized === null || serialized === undefined) {
      return undefined;
    }
    
    // Type guard to ensure we return the correct types
    if (typeof serialized === 'string' || 
        typeof serialized === 'number' || 
        typeof serialized === 'boolean' ||
        (typeof serialized === 'object' && serialized !== null)) {
      return serialized;
    }
    
    return undefined;
  } catch (error) {
    console.warn('Failed to serialize data for Prisma JSON:', error);
    return undefined;
  }
}

interface ValidatedModelConfigData {
  name?: string;
  provider: string;
  modelId: string;
  version?: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  topK?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  timeoutMs?: number;
  retryAttempts?: number;
  retryDelayMs?: number;
  costPerInputToken?: number;
  costPerOutputToken?: number;
  dailyCostLimit?: number;
  monthlyTokenLimit?: number;
  usageScenarios?: string[];
  priority?: number;
  isActive?: boolean;
  isDefault?: boolean;
  customParameters?: Record<string, unknown>;
  systemPrompt?: string;
  stopSequences?: string[];
  description?: string;
  tags?: string[];
}

interface ValidatedModelSelectionData {
  scenario: string;
  tenantId?: string;
  maxCost?: number;
  maxLatency?: number;
  requiredCapabilities?: string[];
  excludeModels?: string[];
  preferredProviders?: string[];
}

interface ValidatedFallbackRuleData {
  sourceModelId: string;
  fallbackModelId: string;
  scenario: string;
  priority?: number;
  maxAttempts?: number;
  conditions?: Record<string, unknown>;
}

interface ModelConfigurationWhereInput {
  provider?: string;
  isActive?: boolean;
  usageScenarios?: { has: string };
}

interface UsageStatisticsWhereInput {
  modelConfig?: {
    provider?: string;
    modelId?: string;
  };
  tenantId?: string;
  periodStart?: {
    gte?: Date;
    lte?: Date;
  };
  granularity?: string;
}

interface FallbackRuleWhereInput {
  isActive: boolean;
  sourceModelId?: string;
  scenario?: string;
}

interface AuditLogWhereInput {
  modelConfigId?: string;
  action?: string;
  userId?: string;
}


/**
 * Validation schemas
 */
const modelConfigSchema = Joi.object({
  name: Joi.string().min(1).max(100).optional(),
  provider: Joi.string()
    .valid('openrouter', 'ollama', 'azure', 'aws')
    .required(),
  modelId: Joi.string().min(1).max(200).required(),
  version: Joi.string().max(50).optional(),

  // Model parameters
  temperature: Joi.number().min(0).max(2).optional(),
  maxTokens: Joi.number().min(1).max(128000).optional(),
  topP: Joi.number().min(0).max(1).optional(),
  topK: Joi.number().min(1).max(1000).optional(),
  frequencyPenalty: Joi.number().min(-2).max(2).optional(),
  presencePenalty: Joi.number().min(-2).max(2).optional(),

  // System behavior
  timeoutMs: Joi.number().min(1000).max(300000).optional(),
  retryAttempts: Joi.number().min(0).max(10).optional(),
  retryDelayMs: Joi.number().min(100).max(60000).optional(),

  // Cost and usage
  costPerInputToken: Joi.number().min(0).optional(),
  costPerOutputToken: Joi.number().min(0).optional(),
  dailyCostLimit: Joi.number().min(0).optional(),
  monthlyTokenLimit: Joi.number().min(0).optional(),

  // Configuration
  usageScenarios: Joi.array()
    .items(Joi.string().valid(...Object.values(AiScenario)))
    .optional(),
  priority: Joi.number().min(0).max(100).optional(),
  isActive: Joi.boolean().optional(),
  isDefault: Joi.boolean().optional(),

  // Advanced
  customParameters: Joi.object().optional(),
  systemPrompt: Joi.string().max(5000).optional(),
  stopSequences: Joi.array().items(Joi.string().max(100)).optional(),
  description: Joi.string().max(500).optional(),
  tags: Joi.array().items(Joi.string().max(50)).optional(),
});

const modelSelectionSchema = Joi.object({
  scenario: Joi.string()
    .valid(...Object.values(AiScenario))
    .required(),
  tenantId: Joi.string().optional(),
  maxCost: Joi.number().min(0).optional(),
  maxLatency: Joi.number().min(0).optional(),
  requiredCapabilities: Joi.array().items(Joi.string()).optional(),
  excludeModels: Joi.array().items(Joi.string()).optional(),
  preferredProviders: Joi.array().items(Joi.string()).optional(),
});

const fallbackRuleSchema = Joi.object({
  sourceModelId: Joi.string().required(),
  fallbackModelId: Joi.string().required(),
  scenario: Joi.string()
    .valid('timeout', 'error', 'cost_limit', 'quality')
    .required(),
  priority: Joi.number().min(0).max(100).optional(),
  maxAttempts: Joi.number().min(1).max(10).optional(),
  conditions: Joi.object().optional(),
});

/**
 * Model Configuration Controller
 */
export class ModelConfigController {
  private prisma: PrismaClient;
  private modelManager: AiModelManager;

  constructor(prisma: PrismaClient, modelManager: AiModelManager) {
    this.prisma = prisma;
    this.modelManager = modelManager;
  }

  /**
   * Get all model configurations
   */
  public getModelConfigurations = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      const { provider, isActive, scenario, page = 1, limit = 20 } = req.query;

      const where: ModelConfigurationWhereInput = {};
      if (provider) where.provider = provider as string;
      if (isActive !== undefined) where.isActive = isActive === 'true';
      if (scenario) where.usageScenarios = { has: scenario as string };

      const skip = (Number(page) - 1) * Number(limit);

      const [configurations, total] = await Promise.all([
        this.prisma.aiModelConfiguration.findMany({
          where,
          skip,
          take: Number(limit),
          include: {
            tenantConfigs: true,
            usageStats: {
              take: 1,
              orderBy: { createdAt: 'desc' },
            },
            auditLogs: {
              take: 5,
              orderBy: { createdAt: 'desc' },
            },
          },
          orderBy: [{ priority: 'desc' }, { createdAt: 'desc' }],
        }),
        this.prisma.aiModelConfiguration.count({ where }),
      ]);

      res.json({
        success: true,
        data: {
          configurations,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total,
            pages: Math.ceil(total / Number(limit)),
          },
        },
      });
    } catch (error) {
      console.error('Failed to get model configurations:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve model configurations',
      });
    }
  };

  /**
   * Get a specific model configuration
   */
  public getModelConfiguration = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(400).json({
          success: false,
          error: 'Configuration ID is required',
        });
        return;
      }

      const configuration = await this.prisma.aiModelConfiguration.findUnique({
        where: { id },
        include: {
          tenantConfigs: true,
          usageStats: {
            orderBy: { createdAt: 'desc' },
            take: 10,
          },
          auditLogs: {
            orderBy: { createdAt: 'desc' },
            take: 20,
          },
          fallbackTargets: {
            include: { fallbackModel: true },
          },
          fallbackSources: {
            include: { sourceModel: true },
          },
        },
      });

      if (!configuration) {
        res.status(404).json({
          success: false,
          error: 'Model configuration not found',
        });
        return;
      }

      // Get performance metrics
      const metrics = this.modelManager.getModelMetrics(
        configuration.provider,
        configuration.modelId
      );

      res.json({
        success: true,
        data: {
          ...configuration,
          performanceMetrics: metrics[0] || null,
        },
      });
    } catch (error) {
      console.error('Failed to get model configuration:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve model configuration',
      });
    }
  };

  /**
   * Create a new model configuration
   */
  public createModelConfiguration = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      const validation = modelConfigSchema.validate(req.body);
      const error = validation.error;
      const value = validation.value as ValidatedModelConfigData;
      if (error) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: error.details,
        });
        return;
      }

      const tenantId = req.user?.tenantId;
      const validatedData = value as ValidatedModelConfigData;

      // Create AI model config object
      const aiConfig: AiModelConfig = {
        provider: validatedData.provider,
        modelId: validatedData.modelId,
        ...(validatedData.version && { version: validatedData.version }),
        ...(validatedData.temperature !== undefined && { temperature: validatedData.temperature }),
        ...(validatedData.maxTokens !== undefined && { maxTokens: validatedData.maxTokens }),
        ...(validatedData.topP !== undefined && { topP: validatedData.topP }),
        ...(validatedData.topK !== undefined && { topK: validatedData.topK }),
        ...(validatedData.frequencyPenalty !== undefined && { frequencyPenalty: validatedData.frequencyPenalty }),
        ...(validatedData.presencePenalty !== undefined && { presencePenalty: validatedData.presencePenalty }),
        ...(validatedData.costPerInputToken !== undefined && { costPerInputToken: validatedData.costPerInputToken }),
        ...(validatedData.costPerOutputToken !== undefined && { costPerOutputToken: validatedData.costPerOutputToken }),
        ...(validatedData.dailyCostLimit !== undefined && { dailyCostLimit: validatedData.dailyCostLimit }),
        monthlyTokenLimit: validatedData.monthlyTokenLimit,
        customParameters: validatedData.customParameters,
        systemPrompt: validatedData.systemPrompt,
        stopSequences: validatedData.stopSequences,
      };

      // Add configuration using model manager
      const configId = await this.modelManager.addModelConfiguration(
        aiConfig,
        tenantId
      );

      // Retrieve the created configuration
      const configuration = await this.prisma.aiModelConfiguration.findUnique({
        where: { id: configId },
        include: {
          tenantConfigs: true,
        },
      });

      res.status(201).json({
        success: true,
        data: configuration,
      });
    } catch (error) {
      console.error('Failed to create model configuration:', error);
      res.status(500).json({
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to create model configuration',
      });
    }
  };

  /**
   * Update a model configuration
   */
  public updateModelConfiguration = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      const { id } = req.params;
      
      if (!id) {
        res.status(400).json({
          success: false,
          error: 'Configuration ID is required',
        });
        return;
      }
      
      const validation = modelConfigSchema.validate(req.body);
      const error = validation.error;
      const value = validation.value as ValidatedModelConfigData;
      const userId = req.user?.userId || 'system';
      const validatedData = value;

      if (error) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: error.details,
        });
        return;
      }

      // Get existing configuration for audit
      const existing = await this.prisma.aiModelConfiguration.findUnique({
        where: { id },
      });

      if (!existing) {
        res.status(404).json({
          success: false,
          error: 'Model configuration not found',
        });
        return;
      }

      // Update configuration
      const updateData: ModelConfigUpdateData = {};
      if (validatedData.name !== undefined) updateData.name = validatedData.name;
      if (validatedData.temperature !== undefined) updateData.temperature = validatedData.temperature;
      if (validatedData.maxTokens !== undefined) updateData.maxTokens = validatedData.maxTokens;
      if (validatedData.topP !== undefined) updateData.topP = validatedData.topP;
      if (validatedData.topK !== undefined) updateData.topK = validatedData.topK;
      if (validatedData.frequencyPenalty !== undefined) updateData.frequencyPenalty = validatedData.frequencyPenalty;
      if (validatedData.presencePenalty !== undefined) updateData.presencePenalty = validatedData.presencePenalty;
      if (validatedData.timeoutMs !== undefined) updateData.timeoutMs = validatedData.timeoutMs;
      if (validatedData.retryAttempts !== undefined) updateData.retryAttempts = validatedData.retryAttempts;
      if (validatedData.retryDelayMs !== undefined) updateData.retryDelayMs = validatedData.retryDelayMs;
      
      // Add remaining fields to updateData
      if (validatedData.costPerInputToken !== undefined) updateData.costPerInputToken = validatedData.costPerInputToken.toString();
      if (validatedData.costPerOutputToken !== undefined) updateData.costPerOutputToken = validatedData.costPerOutputToken.toString();
      if (validatedData.dailyCostLimit !== undefined) updateData.dailyCostLimit = validatedData.dailyCostLimit.toString();
      if (validatedData.monthlyTokenLimit !== undefined) updateData.monthlyTokenLimit = validatedData.monthlyTokenLimit;
      if (validatedData.usageScenarios !== undefined) updateData.usageScenarios = validatedData.usageScenarios;
      if (validatedData.priority !== undefined) updateData.priority = validatedData.priority;
      if (validatedData.isActive !== undefined) updateData.isActive = validatedData.isActive;
      if (validatedData.isDefault !== undefined) updateData.isDefault = validatedData.isDefault;
      if (validatedData.customParameters !== undefined) updateData.customParameters = toPrismaJson(validatedData.customParameters);
      if (validatedData.systemPrompt !== undefined) updateData.systemPrompt = validatedData.systemPrompt;
      if (validatedData.stopSequences !== undefined) updateData.stopSequences = validatedData.stopSequences;
      if (validatedData.description !== undefined) updateData.description = validatedData.description;
      
      if (validatedData.tags !== undefined) updateData.tags = validatedData.tags;
      
      const updated = await this.prisma.aiModelConfiguration.update({
        where: { id },
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-explicit-any
        data: updateData as any, // Prisma input types - will be properly typed when client is generated
        include: {
          tenantConfigs: true,
        },
      });

      // Log the change
      await this.prisma.modelConfigurationAudit.create({
        data: {
          modelConfigId: id,
          action: 'UPDATE',
          oldValues: toPrismaJson(existing),
          newValues: toPrismaJson(validatedData),
          changes: this.getChangedFields(existing, validatedData as unknown as AuditRecord),
          userId,
          source: 'api',
        },
      });

      res.json({
        success: true,
        data: updated,
      });
    } catch (error) {
      console.error('Failed to update model configuration:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update model configuration',
      });
    }
  };

  /**
   * Delete a model configuration
   */
  public deleteModelConfiguration = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      const { id } = req.params;
      
      if (!id) {
        res.status(400).json({
          success: false,
          error: 'Configuration ID is required',
        });
        return;
      }
      
      const userId = req.user?.userId || 'system';
      const existing = await this.prisma.aiModelConfiguration.findUnique({
        where: { id },
      });

      if (!existing) {
        res.status(404).json({
          success: false,
          error: 'Model configuration not found',
        });
        return;
      }

      // Soft delete - mark as inactive
      await this.prisma.aiModelConfiguration.update({
        where: { id },
        data: { isActive: false },
      });

      // Log the deletion
      await this.prisma.modelConfigurationAudit.create({
        data: {
          modelConfigId: id,
          action: 'DELETE',
          oldValues: toPrismaJson(existing),
          newValues: toPrismaJson({}),
          changes: ['isActive'],
          userId,
          source: 'api',
        },
      });

      res.json({
        success: true,
        message: 'Model configuration deleted successfully',
      });
    } catch (error) {
      console.error('Failed to delete model configuration:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete model configuration',
      });
    }
  };

  /**
   * Select best model for given criteria
   */
  public selectModel = async (
    req: Request, 
    res: Response
  ): Promise<void> => {
    try {
      const validation = modelSelectionSchema.validate(req.body);
      const error = validation.error;
      const value = validation.value as Record<string, unknown>;
      if (error) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: error.details,
        });
        return;
      }

      const validatedValue = value.value as ValidatedModelSelectionData;
      const criteria: ModelSelectionCriteria = {
        scenario: validatedValue.scenario as AiScenario,
        tenantId: validatedValue.tenantId || req.user?.tenantId,
        maxCost: validatedValue.maxCost,
        maxLatency: validatedValue.maxLatency,
        requiredCapabilities: validatedValue.requiredCapabilities,
        excludeModels: validatedValue.excludeModels,
        preferredProviders: validatedValue.preferredProviders,
      };

      const selection = await this.modelManager.selectModel(criteria);

      res.json({
        success: true,
        data: selection,
      });
    } catch (error) {
      console.error('Model selection failed:', error);
      res.status(500).json({
        success: false,
        error:
          error instanceof Error ? error.message : 'Model selection failed',
      });
    }
  };

  /**
   * Get model performance metrics
   */
  public getModelMetrics = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      const { provider, modelId } = req.query;

      const metrics = this.modelManager.getModelMetrics(
        provider as string,
        modelId as string
      );

      res.json({
        success: true,
        data: metrics,
      });
    } catch (error) {
      console.error('Failed to get model metrics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve model metrics',
      });
    }
  };

  /**
   * Create fallback rule
   */
  public createFallbackRule = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      const validation = fallbackRuleSchema.validate(req.body);
      const error = validation.error;
      const value = validation.value as Record<string, unknown>;
      if (error) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: error.details,
        });
        return;
      }

      const validatedValue = value.value as ValidatedFallbackRuleData;
      const rule = await this.prisma.modelFallbackRule.create({
        data: {
          sourceModelId: validatedValue.sourceModelId,
          fallbackModelId: validatedValue.fallbackModelId,
          scenario: validatedValue.scenario,
          priority: validatedValue.priority || 0,
          maxAttempts: validatedValue.maxAttempts || 3,
          conditions: toPrismaJson(validatedValue.conditions || {}),
          isActive: true,
        },
        include: {
          sourceModel: true,
          fallbackModel: true,
        },
      });

      res.status(201).json({
        success: true,
        data: rule,
      });
    } catch (error) {
      console.error('Failed to create fallback rule:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create fallback rule',
      });
    }
  };

  /**
   * Get fallback rules
   */
  public getFallbackRules = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      const { sourceModelId, scenario } = req.query;

      const where: FallbackRuleWhereInput = { isActive: true };
      if (sourceModelId) where.sourceModelId = sourceModelId as string;
      if (scenario) where.scenario = scenario as string;

      const rules = await this.prisma.modelFallbackRule.findMany({
        where,
        include: {
          sourceModel: true,
          fallbackModel: true,
        },
        orderBy: [{ scenario: 'asc' }, { priority: 'asc' }],
      });

      res.json({
        success: true,
        data: rules,
      });
    } catch (error) {
      console.error('Failed to get fallback rules:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve fallback rules',
      });
    }
  };

  /**
   * Get model usage statistics
   */
  public getUsageStatistics = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      const {
        provider,
        modelId,
        tenantId,
        startDate,
        endDate,
        granularity = 'daily',
      } = req.query;

      const where: UsageStatisticsWhereInput = {};
      if (provider || modelId) {
        where.modelConfig = {};
        if (provider) where.modelConfig.provider = provider as string;
        if (modelId) where.modelConfig.modelId = modelId as string;
      }
      if (tenantId) where.tenantId = tenantId as string;
      if (startDate || endDate) {
        where.periodStart = {};
        if (startDate) where.periodStart.gte = new Date(startDate as string);
        if (endDate) where.periodStart.lte = new Date(endDate as string);
      }
      if (granularity) where.granularity = granularity as string;

      const stats = await this.prisma.modelUsageStats.findMany({
        where,
        include: {
          modelConfig: true,
        },
        orderBy: {
          periodStart: 'desc',
        },
        take: 100,
      });

      res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      console.error('Failed to get usage statistics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve usage statistics',
      });
    }
  };

  /**
   * Get audit logs for model configurations
   */
  public getAuditLogs = async (req: Request, res: Response): Promise<void> => {
    try {
      const { modelConfigId, action, userId, page = 1, limit = 50 } = req.query;

      const where: AuditLogWhereInput = {};
      if (modelConfigId) where.modelConfigId = modelConfigId as string;
      if (action) where.action = action as string;
      if (userId) where.userId = userId as string;

      const skip = (Number(page) - 1) * Number(limit);

      const results = await Promise.all([
        this.prisma.modelConfigurationAudit.findMany({
          where,
          skip,
          take: Number(limit),
          include: {
            modelConfig: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
        }),
        this.prisma.modelConfigurationAudit.count({ where }),
      ]);
      const logs = results[0];
      const total = results[1];

      res.json({
        success: true,
        data: {
          logs,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total,
            pages: Math.ceil(total / Number(limit)),
          },
        },
      });
    } catch (error) {
      console.error('Failed to get audit logs:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve audit logs',
      });
    }
  };

  /**
   * Get list of changed fields between old and new values
   */
  private getChangedFields(
    oldValues: AuditRecord, 
    newValues: AuditRecord
  ): string[] {
    const changes: string[] = [];
    const allKeys = new Set([
      ...Object.keys(oldValues || {}),
      ...Object.keys(newValues || {}),
    ]);

    for (const key of allKeys) {
      if (oldValues?.[key] !== newValues?.[key]) {
        changes.push(key);
      }
    }

    return changes;
  }
}

// Express Request type extension defined in src/types/express.d.ts