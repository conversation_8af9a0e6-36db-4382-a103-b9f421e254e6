// Temporary stubs to fix compilation

import { AiRequest, AiModelConfig, AiResponse } from '../types';

/**
 * TypeScript interfaces for proper type safety
 */
interface PluginInitializationConfig {
  config: {
    provider: string;
    modelId: string;
    apiKey?: string;
  };
  environment: string;
}

/**
 * Generic plugin result interface with proper typing
 */
export interface PluginResult<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * OpenRouter Plugin stub implementation
 */
export class OpenRouterPlugin {
  // Empty stub methods to satisfy interface
  async initialize(_config: PluginInitializationConfig): Promise<void> {
    // Stub implementation
  }

  async chat(_request: AiRequest): Promise<PluginResult<AiResponse>> {
    return { success: false, error: 'Not implemented' };
  }

  async getAvailableModels(): Promise<PluginResult<string[]>> {
    return { success: true, data: [] };
  }

  async validateModelConfig(_config: AiModelConfig): Promise<PluginResult<boolean>> {
    return { success: true, data: true };
  }

  async getModelPricing(
    _modelId: string
  ): Promise<PluginResult<{ inputTokenPrice: number; outputTokenPrice: number }>> {
    return { success: true, data: { inputTokenPrice: 0, outputTokenPrice: 0 } };
  }
}