/**
 * AI Model Manager
 *
 * Manages AI model configurations, selection, and fallback strategies.
 * Provides a unified interface for AI model operations across the platform.
 */

import { PrismaClient } from '@prisma/client';
import {
  AiModelConfig,
  AiRequest,
  AiResponse,
  AiScenario,
  ModelSelectionCriteria,
  ModelSelectionResult,
  FallbackStrategy,
  IAiServicePlugin,
} from '../types';
import { OpenRouterPlugin } from './temp-stubs';

/**
 * TypeScript interfaces for proper type safety
 */
interface ExtendedAiModelConfig extends AiModelConfig {
  enabled?: boolean;
  timeoutMs?: number;
  retryAttempts?: number;
  retryDelayMs?: number;
  scenario?: AiScenario;
}


interface ScoredModel {
  config: AiModelConfig;
  score: number;
  estimatedCost: number;
  estimatedLatency: number;
}

interface ErrorWithMessage {
  message?: string;
}

/**
 * AI Model Manager Configuration
 */
export interface AiModelManagerConfig {
  defaultModel?: string;
  fallbackEnabled: boolean;
  maxFallbackAttempts: number;
  costThreshold?: number;
  latencyThreshold?: number;
  enableMetrics: boolean;
  enableAuditLog: boolean;
}

/**
 * Model Performance Metrics
 */
export interface ModelPerformanceMetrics {
  modelId: string;
  provider: string;
  avgResponseTime: number;
  successRate: number;
  avgCostPerRequest: number;
  totalRequests: number;
  lastUpdated: Date;
}

/**
 * AI Model Manager Service
 */

interface AuditRecord {
  [key: string]: unknown;
}

/**
 * Helper to convert data to Prisma JSON compatible format
 */
function toPrismaJson(data: unknown): object | string | number | boolean | undefined {
  if (data === null || data === undefined) {
    return undefined;
  }
  
  try {
    // Ensure JSON serializable - filter out null values
    const jsonString = JSON.stringify(data);
    const serialized: unknown = JSON.parse(jsonString);
    
    // Return undefined if the result is null to avoid Prisma type issues
    if (serialized === null || serialized === undefined) {
      return undefined;
    }
    
    // Type guard to ensure we return the correct types
    if (typeof serialized === 'string' || 
        typeof serialized === 'number' || 
        typeof serialized === 'boolean' ||
        (typeof serialized === 'object' && serialized !== null)) {
      return serialized;
    }
    
    return undefined;
  } catch (error) {
    console.warn('Failed to serialize data for Prisma JSON:', error);
    return undefined;
  }
}
export class AiModelManager {
  private prisma: PrismaClient;
  private plugins: Map<string, IAiServicePlugin> = new Map();
  private modelConfigs: Map<string, AiModelConfig> = new Map();
  private performanceMetrics: Map<string, ModelPerformanceMetrics> = new Map();
  private config: AiModelManagerConfig;

  constructor(
    prisma: PrismaClient,
    config: AiModelManagerConfig = {
      fallbackEnabled: true,
      maxFallbackAttempts: 3,
      enableMetrics: true,
      enableAuditLog: true,
    }
  ) {
    this.prisma = prisma;
    this.config = config;
  }

  /**
   * Initialize the AI Model Manager
   */
  public async initialize(): Promise<void> {
    try {
      // Load model configurations from database
      await this.loadModelConfigurations();

      // Load fallback strategies
      await this.loadFallbackStrategies();

      // Initialize performance metrics
      await this.loadPerformanceMetrics();

      // Register default plugins
      await this.registerDefaultPlugins();

      console.log('AI Model Manager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize AI Model Manager:', error);
      throw error;
    }
  }

  /**
   * Register an AI service plugin
   */
  public registerPlugin(provider: string, plugin: IAiServicePlugin): void {
    this.plugins.set(provider, plugin);
    console.log(`Registered AI plugin for provider: ${provider}`);
  }

  /**
   * Add or update a model configuration
   */
  public async addModelConfiguration(
    config: AiModelConfig,
    tenantId?: string
  ): Promise<string> {
    try {
      // Validate configuration
      const plugin = this.plugins.get(config.provider);
      if (plugin) {
        const validation = await plugin.validateModelConfig(config);
        if (!validation.success) {
          throw new Error(
            `Model configuration validation failed: ${validation.error || 'unknown error'}`
          );
        }
      }

      const extendedConfig = config as ExtendedAiModelConfig;

      // Save to database
      const modelConfig = await this.prisma.aiModelConfiguration.create({
        data: {
          name: `${config.provider}-${config.modelId}`,
          provider: config.provider,
          modelId: config.modelId,
          version: config.version || '1.0',
          temperature: config.temperature,
          maxTokens: config.maxTokens,
          topP: config.topP,
          topK: config.topK,
          frequencyPenalty: config.frequencyPenalty,
          presencePenalty: config.presencePenalty,
          timeoutMs: extendedConfig.timeoutMs || 30000,
          retryAttempts: extendedConfig.retryAttempts || 3,
          retryDelayMs: extendedConfig.retryDelayMs || 1000,
          costPerInputToken: config.costPerInputToken
            ? config.costPerInputToken.toString()
            : '0',
          costPerOutputToken: config.costPerOutputToken
            ? config.costPerOutputToken.toString()
            : '0',
          dailyCostLimit: config.dailyCostLimit
            ? config.dailyCostLimit.toString()
            : undefined,
          monthlyTokenLimit: config.monthlyTokenLimit,
          usageScenarios: ['CUSTOM'],
          priority: 0,
          isActive: extendedConfig.enabled ?? true,
          customParameters: toPrismaJson(config.customParameters || {}),
          systemPrompt: config.systemPrompt,
          stopSequences: config.stopSequences || [],
          description: `AI model configuration for ${config.provider}/${config.modelId}`,
          tags: [config.provider, 'ai', 'llm'],
        },
      });

      // Create tenant-specific configuration if provided
      if (tenantId) {
        await this.prisma.tenantModelConfiguration.create({
          data: {
            tenantId,
            modelConfigId: modelConfig.id,
            isEnabled: true,
            customConfig: toPrismaJson(config.customParameters || {}),
          },
        });
      }

      // Update local cache
      this.modelConfigs.set(modelConfig.id, config);

      // Log configuration change
      if (this.config.enableAuditLog) {
        await this.logConfigurationChange(
          'CREATE',
          modelConfig.id,
          null,
          config as unknown as AuditRecord
        );
      }

      return modelConfig.id;
    } catch (error) {
      console.error('Failed to add model configuration:', error);
      throw error;
    }
  }

  /**
   * Select the best model for a given scenario
   */
  public async selectModel(
    criteria: ModelSelectionCriteria
  ): Promise<ModelSelectionResult> {
    try {
      const availableModels = await this.getAvailableModels(criteria);

      if (availableModels.length === 0) {
        throw new Error(
          `No models available for scenario: ${criteria.scenario}`
        );
      }

      // Score models based on criteria
      const scoredModels = await Promise.all(
        availableModels.map(async (config) => {
          const score = await this.scoreModel(config, criteria);
          const metrics = this.performanceMetrics.get(
            `${config.provider}:${config.modelId}`
          );

          return {
            config,
            score,
            estimatedCost: this.estimateCost(config, criteria),
            estimatedLatency: metrics?.avgResponseTime || 1000,
          };
        })
      );

      // Sort by score (higher is better)
      scoredModels.sort((a, b) => b.score - a.score);

      const selectedModel = scoredModels[0];
      if (!selectedModel) {
        throw new Error('No suitable model found for selection criteria');
      }
      
      const alternatives = scoredModels.slice(1, 4).map((m) => m.config);

      return {
        modelConfig: selectedModel.config,
        reason: this.generateSelectionReason(selectedModel, criteria),
        alternatives,
        estimatedCost: selectedModel.estimatedCost,
        estimatedLatency: selectedModel.estimatedLatency,
      };
    } catch (error) {
      console.error('Model selection failed:', error);
      throw error;
    }
  }

  /**
   * Execute AI request with automatic fallback
   */
  public async executeRequest(
    request: AiRequest,
    criteria?: ModelSelectionCriteria
  ): Promise<AiResponse> {
    const selectionCriteria: ModelSelectionCriteria = criteria || {
      scenario: request.context?.scenario || AiScenario.CUSTOM,
      tenantId: request.context?.tenantId,
    };

    let lastError: Error | null = null;
    let attemptCount = 0;
    const maxAttempts = this.config.maxFallbackAttempts;

    while (attemptCount < maxAttempts) {
      try {
        attemptCount++;

        // Select model for this attempt
        const selection = await this.selectModel({
          ...selectionCriteria,
          excludeModels:
            attemptCount > 1 ? this.getFailedModels(lastError) : undefined,
        });

        // Get plugin for the selected model
        const plugin = this.plugins.get(selection.modelConfig.provider);
        if (!plugin) {
          throw new Error(
            `No plugin available for provider: ${selection.modelConfig.provider}`
          );
        }

        // Execute request
        const startTime = Date.now();
        const result = await plugin.chat({
          ...request,
          model: selection.modelConfig.modelId,
          temperature: request.temperature ?? selection.modelConfig.temperature,
          maxTokens: request.maxTokens ?? selection.modelConfig.maxTokens,
        });

        if (result.success && result.data) {
          const responseTime = Date.now() - startTime;

          // Update performance metrics
          await this.updatePerformanceMetrics(
            selection.modelConfig,
            responseTime,
            true,
            result.data.cost?.totalCost || 0
          );

          // Log successful usage
          await this.logModelUsage(request, result.data, selection.modelConfig);

          return result.data;
        } else {
          throw new Error(result.error || 'AI request failed');
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');

        console.warn(`AI request attempt ${attemptCount} failed:`, {
          error: lastError.message,
          attemptCount,
          maxAttempts,
        });

        // Check if we should attempt fallback
        if (attemptCount >= maxAttempts || !this.isRetryableError(lastError)) {
          break;
        }

        // Wait before retry with exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attemptCount - 1), 10000);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    throw lastError || new Error('All AI request attempts failed');
  }

  /**
   * Get model performance metrics
   */
  public getModelMetrics(
    provider?: string,
    modelId?: string
  ): ModelPerformanceMetrics[] {
    const allMetrics = Array.from(this.performanceMetrics.values());

    if (provider && modelId) {
      const key = `${provider}:${modelId}`;
      const metrics = this.performanceMetrics.get(key);
      return metrics ? [metrics] : [];
    }

    if (provider) {
      return allMetrics.filter((m) => m.provider === provider);
    }

    return allMetrics;
  }

  /**
   * Load model configurations from database
   */
  private async loadModelConfigurations(): Promise<void> {
    const configs = await this.prisma.aiModelConfiguration.findMany({
      where: { isActive: true },
      include: {
        tenantConfigs: true,
        fallbackTargets: true,
      },
    });

    for (const config of configs) {
      const aiConfig: AiModelConfig = {
        provider: config.provider,
        modelId: config.modelId,
        version: config.version,
        temperature: config.temperature || undefined,
        maxTokens: config.maxTokens || undefined,
        topP: config.topP || undefined,
        topK: config.topK || undefined,
        frequencyPenalty: config.frequencyPenalty || undefined,
        presencePenalty: config.presencePenalty || undefined,
        costPerInputToken: parseFloat(config.costPerInputToken.toString()),
        costPerOutputToken: parseFloat(config.costPerOutputToken.toString()),
        dailyCostLimit: config.dailyCostLimit
          ? parseFloat(config.dailyCostLimit.toString())
          : undefined,
        monthlyTokenLimit: config.monthlyTokenLimit || undefined,
        customParameters:
          (config.customParameters as Record<string, unknown>) || {},
        systemPrompt: config.systemPrompt || undefined,
        stopSequences: config.stopSequences,
      };

      this.modelConfigs.set(config.id, aiConfig);
    }

    console.log(`Loaded ${configs.length} model configurations`);
  }

  /**
   * Load fallback strategies
   */
  private async loadFallbackStrategies(): Promise<void> {
    const fallbackRules = await this.prisma.modelFallbackRule.findMany({
      where: { isActive: true },
      include: {
        sourceModel: true,
        fallbackModel: true,
      },
    });

    // Group by source model
    const strategiesByModel = new Map<string, FallbackStrategy>();

    for (const rule of fallbackRules) {
      const sourceModelKey = `${rule.sourceModel.provider}:${rule.sourceModel.modelId}`;

      if (!strategiesByModel.has(sourceModelKey)) {
        strategiesByModel.set(sourceModelKey, {
          enabled: true,
          maxAttempts: rule.maxAttempts,
          fallbackModels: [],
          conditions: {
            onTimeout: rule.scenario === 'timeout',
            onError: rule.scenario === 'error',
            onCostLimit: rule.scenario === 'cost_limit',
            onQualityThreshold: rule.scenario === 'quality',
          },
          backoffStrategy: 'exponential',
          baseDelayMs: 1000,
        });
      }

      const strategy = strategiesByModel.get(sourceModelKey)!;
      strategy.fallbackModels.push(
        `${rule.fallbackModel.provider}:${rule.fallbackModel.modelId}`
      );
    }

    // TODO: Store fallback strategies when needed
    console.log(`Loaded ${strategiesByModel.size} fallback strategies`);
  }

  /**
   * Load performance metrics
   */
  private async loadPerformanceMetrics(): Promise<void> {
    const metrics = await this.prisma.modelUsageStats.findMany({
      where: {
        periodStart: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
        },
      },
      include: {
        modelConfig: true,
      },
    });

    for (const metric of metrics) {
      const key = `${metric.modelConfig.provider}:${metric.modelConfig.modelId}`;

      const performanceMetric: ModelPerformanceMetrics = {
        modelId: metric.modelConfig.modelId,
        provider: metric.modelConfig.provider,
        avgResponseTime: metric.avgResponseTime,
        successRate:
          metric.totalRequests > 0
            ? (metric.successfulRequests / metric.totalRequests) * 100
            : 0,
        avgCostPerRequest:
          parseFloat(metric.totalCost.toString()) /
          Math.max(metric.totalRequests, 1),
        totalRequests: metric.totalRequests,
        lastUpdated: metric.createdAt,
      };

      this.performanceMetrics.set(key, performanceMetric);
    }

    console.log(`Loaded performance metrics for ${metrics.length} models`);
  }

  /**
   * Register default AI service plugins
   */
  private async registerDefaultPlugins(): Promise<void> {
    // Register OpenRouter plugin if API key is available
    if (process.env.OPENROUTER_API_KEY) {
      const openRouterPlugin = new OpenRouterPlugin();
      await openRouterPlugin.initialize({
        config: {
          provider: 'openrouter',
          modelId: 'openai/gpt-4',
          apiKey: process.env.OPENROUTER_API_KEY,
        },
        environment: 'production',
      });

      this.registerPlugin('openrouter', openRouterPlugin);
    }

    // Add more plugins here as needed (Ollama, Azure, etc.)
  }

  /**
   * Get available models for given criteria
   */
  private async getAvailableModels(
    criteria: ModelSelectionCriteria
  ): Promise<AiModelConfig[]> {
    const configs = Array.from(this.modelConfigs.values());

    return configs.filter((config) => {
      const extendedConfig = config as ExtendedAiModelConfig;

      // Filter by scenario
      if (
        criteria.scenario &&
        extendedConfig.scenario &&
        extendedConfig.scenario !== criteria.scenario
      ) {
        return false;
      }

      // Filter by excluded models
      if (
        criteria.excludeModels?.includes(`${config.provider}:${config.modelId}`)
      ) {
        return false;
      }

      // Filter by preferred providers
      if (
        criteria.preferredProviders &&
        !criteria.preferredProviders.includes(config.provider)
      ) {
        return false;
      }

      return extendedConfig.enabled ?? true;
    });
  }

  /**
   * Score a model based on selection criteria
   */
  private async scoreModel(
    config: AiModelConfig,
    criteria: ModelSelectionCriteria
  ): Promise<number> {
    let score = 0;

    // Base score for enabled models
    score += 50;

    // Performance scoring
    const metrics = this.performanceMetrics.get(
      `${config.provider}:${config.modelId}`
    );
    if (metrics) {
      score += metrics.successRate * 0.3; // Up to 30 points for success rate
      score += Math.max(0, (5000 - metrics.avgResponseTime) / 100); // Up to 50 points for speed
    }

    // Cost efficiency scoring
    if (criteria.maxCost && config.costPerInputToken) {
      const estimatedCost = this.estimateCost(config, criteria);
      if (estimatedCost <= criteria.maxCost) {
        score += 20;
      } else {
        score -= (estimatedCost - criteria.maxCost) * 10;
      }
    }

    // Latency scoring
    if (criteria.maxLatency && metrics) {
      if (metrics.avgResponseTime <= criteria.maxLatency) {
        score += 20;
      } else {
        score -= (metrics.avgResponseTime - criteria.maxLatency) / 100;
      }
    }

    return Math.max(0, score);
  }

  /**
   * Estimate cost for a request
   */
  private estimateCost(
    config: AiModelConfig,
    _criteria: ModelSelectionCriteria
  ): number {
    // Simple estimation - would be more sophisticated in real implementation
    const estimatedInputTokens = 1000; // Default estimate
    const estimatedOutputTokens = 500;

    const inputCost = (config.costPerInputToken || 0) * estimatedInputTokens;
    const outputCost = (config.costPerOutputToken || 0) * estimatedOutputTokens;

    return inputCost + outputCost;
  }

  /**
   * Generate human-readable selection reason
   */
  private generateSelectionReason(
    selection: ScoredModel,
    criteria: ModelSelectionCriteria
  ): string {
    const reasons = [];

    if (criteria.scenario) {
      reasons.push(`optimized for ${criteria.scenario}`);
    }

    const metrics = this.performanceMetrics.get(
      `${selection.config.provider}:${selection.config.modelId}`
    );
    if (metrics) {
      if (metrics.successRate > 95) {
        reasons.push('high reliability');
      }
      if (metrics.avgResponseTime < 2000) {
        reasons.push('fast response');
      }
    }

    if (selection.estimatedCost < 0.01) {
      reasons.push('cost-effective');
    }

    return reasons.length > 0
      ? `Selected for: ${reasons.join(', ')}`
      : 'Best available option';
  }

  /**
   * Get models that have failed in previous attempts
   */
  private getFailedModels(_error: Error | null): string[] {
    // Logic to extract failed models from error context
    // This would be more sophisticated in real implementation
    return [];
  }

  /**
   * Check if an error is retryable
   */
  private isRetryableError(error: Error): boolean {
    const retryablePatterns = [
      'timeout',
      'network',
      'connection',
      'rate limit',
      'server error',
      'service unavailable',
    ];

    const errorWithMessage = error as ErrorWithMessage;
    const errorMessage = (
      typeof error === 'string'
        ? error
        : errorWithMessage?.message || 'unknown error'
    ).toLowerCase();
    return retryablePatterns.some((pattern) => errorMessage.includes(pattern));
  }

  /**
   * Update performance metrics for a model
   */
  private async updatePerformanceMetrics(
    config: AiModelConfig,
    responseTime: number,
    success: boolean,
    cost: number
  ): Promise<void> {
    const key = `${config.provider}:${config.modelId}`;
    const existing = this.performanceMetrics.get(key);

    if (existing) {
      // Update existing metrics
      existing.totalRequests++;
      existing.avgResponseTime =
        (existing.avgResponseTime * (existing.totalRequests - 1) +
          responseTime) /
        existing.totalRequests;
      existing.successRate = success
        ? (((existing.successRate * (existing.totalRequests - 1)) / 100 + 1) /
            existing.totalRequests) *
          100
        : ((existing.successRate * (existing.totalRequests - 1)) /
            100 /
            existing.totalRequests) *
          100;
      existing.avgCostPerRequest =
        (existing.avgCostPerRequest * (existing.totalRequests - 1) + cost) /
        existing.totalRequests;
      existing.lastUpdated = new Date();
    } else {
      // Create new metrics
      this.performanceMetrics.set(key, {
        modelId: config.modelId,
        provider: config.provider,
        avgResponseTime: responseTime,
        successRate: success ? 100 : 0,
        avgCostPerRequest: cost,
        totalRequests: 1,
        lastUpdated: new Date(),
      });
    }
  }

  /**
   * Log model usage for analytics
   */
  private async logModelUsage(
    request: AiRequest,
    response: AiResponse,
    config: AiModelConfig
  ): Promise<void> {
    try {
      await this.prisma.aiModelUsage.create({
        data: {
          tenantId: request.context?.tenantId || 'unknown',
          provider: config.provider,
          model: config.modelId,
          endpoint: 'chat/completions',
          inputTokens: response.usage.promptTokens,
          outputTokens: response.usage.completionTokens,
          totalTokens: response.usage.totalTokens,
          cost: response.cost?.totalCost?.toString() || '0',
          conversationId: request.context?.conversationId,
          applicationId: request.context?.applicationId,
          userId: request.context?.userId,
          responseTime: response.metadata?.responseTime || 0,
          successful: true,
        },
      });
    } catch (error) {
      console.error('Failed to log model usage:', error);
    }
  }

  /**
   * Log configuration changes for audit
   */
  private async logConfigurationChange(
    action: string,
    modelConfigId: string,
    oldValues: AuditRecord | null,
    newValues: AuditRecord
  ): Promise<void> {
    try {
      await this.prisma.modelConfigurationAudit.create({
        data: {
          modelConfigId,
          action,
          oldValues: toPrismaJson(oldValues || {}),
          newValues: toPrismaJson(newValues || {}),
          changes: this.getChangedFields(oldValues, newValues),
          source: 'api',
        },
      });
    } catch (error) {
      console.error('Failed to log configuration change:', error);
    }
  }

  /**
   * Get list of changed fields between old and new values
   */
  private getChangedFields(
    oldValues: AuditRecord | null, 
    newValues: AuditRecord
  ): string[] {
    if (!oldValues) return Object.keys(newValues || {});

    const changes: string[] = [];
    const allKeys = new Set([
      ...Object.keys(oldValues || {}),
      ...Object.keys(newValues || {}),
    ]);

    for (const key of allKeys) {
      if (oldValues?.[key] !== newValues?.[key]) {
        changes.push(key);
      }
    }

    return changes;
  }
}