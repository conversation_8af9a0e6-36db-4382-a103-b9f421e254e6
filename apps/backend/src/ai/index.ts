/**
 * AI Service Module - Main Entry Point
 *
 * This module provides the main entry point for AI services,
 * including model management, request routing, and monitoring.
 */

// Core types and interfaces
export * from './types';

// Services
export { AiModelManager } from './services/AiModelManager';

// Plugins - temporarily disabled
// export {
//   OpenRouterPlugin,
//   OpenRouterPluginFactory,
//   createOpenRouterPlugin,
// } from './plugins/OpenRouterPlugin';

// Controllers and routes
export { ModelConfigController } from './controllers/ModelConfigController';
export {
  createModelRoutes,
  authMiddleware,
  adminMiddleware,
} from './routes/modelRoutes';

// Main AI service class
import { PrismaClient } from '@prisma/client';
import { AiModelManager, ModelPerformanceMetrics } from './services/AiModelManager';
import {
  AiRequest,
  AiResponse,
  AiScenario,
  ModelSelectionCriteria,
  ModelSelectionResult,
} from './types';

/**
 * Main AI Service Class
 *
 * Provides a high-level interface for AI operations across the platform.
 */
export class AiService {
  private modelManager: AiModelManager;

  constructor(prisma: PrismaClient) {
    this.modelManager = new AiModelManager(prisma);
  }

  /**
   * Initialize the AI service
   */
  public async initialize(): Promise<void> {
    await this.modelManager.initialize();
    console.log('AI Service initialized successfully');
  }

  /**
   * Execute an AI request with automatic model selection and fallback
   */
  public async executeRequest(request: AiRequest): Promise<AiResponse> {
    return this.modelManager.executeRequest(request);
  }

  /**
   * Select the best model for a given scenario
   */
  public async selectModel(criteria: ModelSelectionCriteria): Promise<ModelSelectionResult> {
    return this.modelManager.selectModel(criteria);
  }

  /**
   * Get model performance metrics
   */
  public getModelMetrics(provider?: string, modelId?: string): ModelPerformanceMetrics[] {
    return this.modelManager.getModelMetrics(provider, modelId);
  }

  /**
   * Get the model manager instance
   */
  public getModelManager(): AiModelManager {
    return this.modelManager;
  }

  /**
   * Convenience method for chat completion
   */
  public async chat(
    messages: Array<{ role: string; content: string }>,
    options?: {
      scenario?: AiScenario;
      tenantId?: string;
      userId?: string;
      model?: string;
      temperature?: number;
      maxTokens?: number;
    }
  ): Promise<AiResponse> {
    const request: AiRequest = {
      messages: messages.map((msg) => ({
        role: msg.role as 'system' | 'user' | 'assistant',
        content: msg.content,
      })),
      model: options?.model,
      temperature: options?.temperature,
      maxTokens: options?.maxTokens,
      context: {
        scenario: options?.scenario || AiScenario.CONVERSATION_CHAT,
        tenantId: options?.tenantId,
        userId: options?.userId,
        timestamp: new Date(),
      },
    };

    return this.executeRequest(request);
  }

  /**
   * Convenience method for underwriting analysis
   */
  public async analyzeUnderwriting(
    applicationData: Record<string, unknown>,
    context: {
      tenantId: string;
      userId: string;
      applicationId: string;
    }
  ): Promise<AiResponse> {
    const prompt = this.buildUnderwritingPrompt(applicationData);

    const request: AiRequest = {
      messages: [
        {
          role: 'system',
          content:
            'You are an expert underwriter analyzing loan applications. Provide a detailed risk assessment with clear reasoning.',
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
      context: {
        scenario: AiScenario.UNDERWRITING_ANALYSIS,
        tenantId: context.tenantId,
        userId: context.userId,
        applicationId: context.applicationId,
        timestamp: new Date(),
      },
    };

    return this.executeRequest(request);
  }

  /**
   * Convenience method for document processing
   */
  public async processDocument(
    documentText: string,
    documentType: string,
    context: {
      tenantId: string;
      userId: string;
      applicationId?: string;
    }
  ): Promise<AiResponse> {
    const prompt = this.buildDocumentProcessingPrompt(
      documentText,
      documentType
    );

    const request: AiRequest = {
      messages: [
        {
          role: 'system',
          content:
            'You are a document processing AI. Extract key information from documents accurately and structure the output as JSON.',
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
      context: {
        scenario: AiScenario.DOCUMENT_PROCESSING,
        tenantId: context.tenantId,
        userId: context.userId,
        applicationId: context.applicationId,
        timestamp: new Date(),
      },
    };

    return this.executeRequest(request);
  }

  /**
   * Build prompt for underwriting analysis
   */
  private buildUnderwritingPrompt(
    applicationData: Record<string, unknown>
  ): string {
    return `
Please analyze the following loan application data and provide a comprehensive risk assessment:

Application Data:
${JSON.stringify(applicationData, null, 2)}

Please provide your analysis in the following format:
1. Risk Level (Low/Medium/High)
2. Key Risk Factors
3. Positive Factors
4. Recommendation (Approve/Deny/Request More Information)
5. Reasoning

Focus on:
- Credit history and score
- Income stability and debt-to-income ratio
- Employment history
- Loan purpose and amount
- Collateral (if applicable)
- Any red flags or compliance concerns
`;
  }

  /**
   * Build prompt for document processing
   */
  private buildDocumentProcessingPrompt(
    documentText: string,
    documentType: string
  ): string {
    return `
Please extract key information from this ${documentType} document:

Document Content:
${documentText}

Extract the following information as JSON:
- Document type and subtype
- Key dates (issue date, expiration date, etc.)
- Personal information (names, addresses, IDs)
- Financial information (amounts, account numbers, etc.)
- Verification status and any anomalies
- Confidence score for each extracted field

Return the result as a structured JSON object with clear field names and values.
`;
  }
}

/**
 * Default AI service instance (singleton)
 */
let defaultAiService: AiService | null = null;

/**
 * Get or create the default AI service instance
 */
export function getAiService(prisma?: PrismaClient): AiService {
  if (!defaultAiService) {
    if (!prisma) {
      throw new Error('Prisma client required to initialize AI service');
    }
    defaultAiService = new AiService(prisma);
  }
  return defaultAiService;
}

/**
 * Initialize the default AI service
 */
export async function initializeAiService(
  prisma: PrismaClient
): Promise<AiService> {
  const service = getAiService(prisma);
  await service.initialize();
  return service;
}