/**
 * Platform Analytics Performance Tests
 * Ensures Story 9.3 meets performance requirements (<5 seconds dashboard loading)
 */

import request from 'supertest';
import { app } from '../../index';
import { prisma } from '../../prisma/client';
import { JWTService } from '../../utils/jwt';
import { SubscriptionStatus, BillingCycle, SystemUserRole } from '@prisma/client';

describe('Platform Analytics Performance Tests', () => {
  let systemUserToken: string;
  const PERFORMANCE_THRESHOLD = 5000; // 5 seconds in milliseconds

  beforeAll(async () => {
    systemUserToken = JWTService.generateSystemToken({
      id: 'perf-test-user',
      email: '<EMAIL>',
      role: SystemUserRole.SUPER_ADMIN,
      isSystemUser: true,
    });

    // Create large dataset for performance testing
    await createLargeDataset();
  });

  afterAll(async () => {
    // Clean up test data
    await prisma.subscription.deleteMany();
    await prisma.tenant.deleteMany();
    await prisma.user.deleteMany();
    await prisma.$disconnect();
  });

  describe('Dashboard Performance', () => {
    it('should load dashboard summary within 5 seconds', async () => {
      const startTime = Date.now();

      const response = await request(app)
        .get('/api/platform/analytics/dashboard')
        .set('Authorization', `Bearer ${systemUserToken}`)
        .expect(200);

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      expect(response.body.success).toBe(true);
      expect(responseTime).toBeLessThan(PERFORMANCE_THRESHOLD);

      console.log(`Dashboard loaded in ${responseTime}ms`);
    });

    it('should load revenue analytics within 5 seconds', async () => {
      const startTime = Date.now();

      const response = await request(app)
        .get('/api/platform/analytics/revenue')
        .set('Authorization', `Bearer ${systemUserToken}`)
        .expect(200);

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      expect(response.body.success).toBe(true);
      expect(responseTime).toBeLessThan(PERFORMANCE_THRESHOLD);

      console.log(`Revenue analytics loaded in ${responseTime}ms`);
    });

    it('should load platform metrics within 3 seconds', async () => {
      const startTime = Date.now();

      const response = await request(app)
        .get('/api/platform/analytics/metrics')
        .set('Authorization', `Bearer ${systemUserToken}`)
        .expect(200);

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      expect(response.body.success).toBe(true);
      expect(responseTime).toBeLessThan(3000); // Metrics should be faster

      console.log(`Platform metrics loaded in ${responseTime}ms`);
    });

    it('should load churn predictions within 5 seconds', async () => {
      const startTime = Date.now();

      const response = await request(app)
        .get('/api/platform/analytics/churn-prediction')
        .query({ limit: '100' })
        .set('Authorization', `Bearer ${systemUserToken}`)
        .expect(200);

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      expect(response.body.success).toBe(true);
      expect(responseTime).toBeLessThan(PERFORMANCE_THRESHOLD);

      console.log(`Churn predictions loaded in ${responseTime}ms`);
    });

    it('should load tenant segmentation within 3 seconds', async () => {
      const startTime = Date.now();

      const response = await request(app)
        .get('/api/platform/analytics/segmentation')
        .set('Authorization', `Bearer ${systemUserToken}`)
        .expect(200);

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      expect(response.body.success).toBe(true);
      expect(responseTime).toBeLessThan(3000);

      console.log(`Tenant segmentation loaded in ${responseTime}ms`);
    });
  });

  describe('Executive Report Performance', () => {
    it('should generate executive report within 10 seconds', async () => {
      const startTime = Date.now();

      const response = await request(app)
        .post('/api/platform/analytics/executive-report')
        .set('Authorization', `Bearer ${systemUserToken}`)
        .send({
          startDate: '2024-01-01',
          endDate: '2024-12-31',
          includeForecasts: true,
          includeSegmentation: true,
          includeRecommendations: true,
        })
        .expect(200);

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      expect(response.body.success).toBe(true);
      expect(responseTime).toBeLessThan(10000); // Executive reports can take longer

      console.log(`Executive report generated in ${responseTime}ms`);
    });
  });

  describe('Export Performance', () => {
    it('should export JSON data within 2 seconds', async () => {
      const startTime = Date.now();

      const response = await request(app)
        .get('/api/platform/analytics/export')
        .query({
          type: 'metrics',
          format: 'json',
        })
        .set('Authorization', `Bearer ${systemUserToken}`)
        .expect(200);

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      expect(response.headers['content-type']).toContain('application/json');
      expect(responseTime).toBeLessThan(2000);

      console.log(`JSON export completed in ${responseTime}ms`);
    });

    it('should export CSV data within 3 seconds', async () => {
      const startTime = Date.now();

      const response = await request(app)
        .get('/api/platform/analytics/export')
        .query({
          type: 'segmentation',
          format: 'csv',
        })
        .set('Authorization', `Bearer ${systemUserToken}`)
        .expect(200);

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      expect(response.headers['content-type']).toContain('text/csv');
      expect(responseTime).toBeLessThan(3000);

      console.log(`CSV export completed in ${responseTime}ms`);
    });
  });

  describe('Concurrent Load Performance', () => {
    it('should handle multiple concurrent dashboard requests', async () => {
      const concurrentRequests = 5;
      const promises = [];

      for (let i = 0; i < concurrentRequests; i++) {
        const promise = request(app)
          .get('/api/platform/analytics/dashboard')
          .set('Authorization', `Bearer ${systemUserToken}`)
          .expect(200);
        promises.push(promise);
      }

      const startTime = Date.now();
      const responses = await Promise.all(promises);
      const endTime = Date.now();
      const totalTime = endTime - startTime;

      // All requests should complete within reasonable time
      expect(totalTime).toBeLessThan(PERFORMANCE_THRESHOLD * 2);

      // All responses should be successful
      responses.forEach(response => {
        expect(response.body.success).toBe(true);
      });

      console.log(`${concurrentRequests} concurrent requests completed in ${totalTime}ms`);
    });
  });

  // Helper function to create large dataset for performance testing
  async function createLargeDataset() {
    console.log('Creating large dataset for performance testing...');

    const tenantPromises = [];
    const subscriptionPromises = [];
    const userPromises = [];

    // Create 100 tenants
    for (let i = 1; i <= 100; i++) {
      const tier = i <= 60 ? 'STARTER' : i <= 85 ? 'PROFESSIONAL' : 'ENTERPRISE';
      
      tenantPromises.push(
        prisma.tenant.create({
          data: {
            id: `perf-tenant-${i}`,
            name: `Performance Test Tenant ${i}`,
            subscriptionTier: tier,
            createdAt: new Date(2024, 0, Math.floor(Math.random() * 365)),
          },
        })
      );
    }

    await Promise.all(tenantPromises);

    // Create subscriptions for each tenant
    for (let i = 1; i <= 100; i++) {
      const tier = i <= 60 ? 'STARTER' : i <= 85 ? 'PROFESSIONAL' : 'ENTERPRISE';
      const monthlyFee = tier === 'STARTER' ? 99 : tier === 'PROFESSIONAL' ? 299 : 999;
      const status = Math.random() > 0.1 ? SubscriptionStatus.ACTIVE : SubscriptionStatus.CANCELLED;

      subscriptionPromises.push(
        prisma.subscription.create({
          data: {
            id: `perf-sub-${i}`,
            tenantId: `perf-tenant-${i}`,
            monthlyFee,
            billingCycle: Math.random() > 0.8 ? BillingCycle.YEARLY : BillingCycle.MONTHLY,
            status,
            startDate: new Date(2024, 0, Math.floor(Math.random() * 365)),
            endDate: status === SubscriptionStatus.CANCELLED ? 
              new Date(2024, 6, Math.floor(Math.random() * 180)) : null,
          },
        })
      );
    }

    await Promise.all(subscriptionPromises);

    // Create 2-5 users per tenant
    for (let i = 1; i <= 100; i++) {
      const userCount = Math.floor(Math.random() * 4) + 2; // 2-5 users
      
      for (let j = 1; j <= userCount; j++) {
        userPromises.push(
          prisma.user.create({
            data: {
              id: `perf-user-${i}-${j}`,
              email: `user${j}@perftenant${i}.com`,
              name: `Performance User ${i}-${j}`,
              tenantId: `perf-tenant-${i}`,
              lastLoginAt: new Date(Date.now() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1000),
            },
          })
        );
      }
    }

    await Promise.all(userPromises);

    console.log('Large dataset created successfully');
  }
});
