/**
 * Platform Analytics Integration Tests
 * Tests for Story 9.3: Platform Revenue & Analytics Dashboard API endpoints
 */

import request from 'supertest';
import { app } from '../../index';
import { prisma } from '../../prisma/client';
import { JWTService } from '../../utils/jwt';
import { SubscriptionStatus, BillingCycle, SystemUserRole } from '@prisma/client';

describe('Platform Analytics API Integration Tests', () => {
  let systemUserToken: string;
  let financeAdminToken: string;
  let analyticsViewerToken: string;
  let regularUserToken: string;

  beforeAll(async () => {
    // Create test system users and generate tokens
    systemUserToken = JWTService.generateSystemToken({
      id: 'system-user-1',
      email: '<EMAIL>',
      role: SystemUserRole.SUPER_ADMIN,
      isSystemUser: true,
    });

    financeAdminToken = JWTService.generateSystemToken({
      id: 'finance-user-1',
      email: '<EMAIL>',
      role: SystemUserRole.FINANCE_ADMIN,
      isSystemUser: true,
    });

    analyticsViewerToken = JWTService.generateSystemToken({
      id: 'analytics-user-1',
      email: '<EMAIL>',
      role: SystemUserRole.ANALYTICS_VIEWER,
      isSystemUser: true,
    });

    // Regular tenant user (should not have access)
    regularUserToken = JWTService.generateToken({
      userId: 'regular-user-1',
      email: '<EMAIL>',
      tenantId: 'tenant-1',
      role: 'USER',
    });
  });

  beforeEach(async () => {
    // Clean up test data
    await prisma.subscription.deleteMany();
    await prisma.tenant.deleteMany();
    await prisma.systemUser.deleteMany();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  describe('GET /api/platform/analytics/dashboard', () => {
    it('should return dashboard summary for system admin', async () => {
      // Create test data
      await createTestData();

      const response = await request(app)
        .get('/api/platform/analytics/dashboard')
        .set('Authorization', `Bearer ${systemUserToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('overview');
      expect(response.body.data).toHaveProperty('changes');
      expect(response.body.data).toHaveProperty('alerts');
      expect(response.body.data).toHaveProperty('topSegments');
      expect(response.body.data).toHaveProperty('recentTrends');

      expect(response.body.data.overview).toHaveProperty('mrr');
      expect(response.body.data.overview).toHaveProperty('arr');
      expect(response.body.data.overview).toHaveProperty('totalTenants');
      expect(response.body.data.overview).toHaveProperty('activeTenants');
    });

    it('should allow analytics viewer access', async () => {
      await request(app)
        .get('/api/platform/analytics/dashboard')
        .set('Authorization', `Bearer ${analyticsViewerToken}`)
        .expect(200);
    });

    it('should deny access to regular users', async () => {
      const response = await request(app)
        .get('/api/platform/analytics/dashboard')
        .set('Authorization', `Bearer ${regularUserToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('System user access required');
    });

    it('should deny access without authentication', async () => {
      await request(app)
        .get('/api/platform/analytics/dashboard')
        .expect(401);
    });
  });

  describe('GET /api/platform/analytics/revenue', () => {
    it('should return revenue analytics for finance admin', async () => {
      await createTestData();

      const response = await request(app)
        .get('/api/platform/analytics/revenue')
        .set('Authorization', `Bearer ${financeAdminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('mrr');
      expect(response.body.data).toHaveProperty('arr');
      expect(response.body.data).toHaveProperty('growthRate');
      expect(response.body.data).toHaveProperty('churnRate');
      expect(response.body.data).toHaveProperty('ltv');
      expect(response.body.data).toHaveProperty('cac');
      expect(response.body.data).toHaveProperty('cohortData');
      expect(response.body.data).toHaveProperty('forecasts');
    });

    it('should accept date range parameters', async () => {
      await createTestData();

      const response = await request(app)
        .get('/api/platform/analytics/revenue')
        .query({
          startDate: '2024-01-01',
          endDate: '2024-01-31',
        })
        .set('Authorization', `Bearer ${systemUserToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should validate date format', async () => {
      const response = await request(app)
        .get('/api/platform/analytics/revenue')
        .query({
          startDate: 'invalid-date',
          endDate: '2024-01-31',
        })
        .set('Authorization', `Bearer ${systemUserToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid date format');
    });

    it('should deny access to analytics viewer', async () => {
      const response = await request(app)
        .get('/api/platform/analytics/revenue')
        .set('Authorization', `Bearer ${analyticsViewerToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Platform admin access required');
    });
  });

  describe('GET /api/platform/analytics/churn-prediction', () => {
    it('should return churn predictions', async () => {
      await createTestData();

      const response = await request(app)
        .get('/api/platform/analytics/churn-prediction')
        .set('Authorization', `Bearer ${systemUserToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('predictions');
      expect(response.body.data).toHaveProperty('totalCount');
      expect(response.body.data).toHaveProperty('highRiskCount');
      expect(response.body.data).toHaveProperty('mediumRiskCount');
      expect(response.body.data).toHaveProperty('lowRiskCount');

      expect(Array.isArray(response.body.data.predictions)).toBe(true);
    });

    it('should accept limit parameter', async () => {
      await createTestData();

      const response = await request(app)
        .get('/api/platform/analytics/churn-prediction')
        .query({ limit: '5' })
        .set('Authorization', `Bearer ${systemUserToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.predictions.length).toBeLessThanOrEqual(5);
    });

    it('should validate limit parameter', async () => {
      const response = await request(app)
        .get('/api/platform/analytics/churn-prediction')
        .query({ limit: '2000' })
        .set('Authorization', `Bearer ${systemUserToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/platform/analytics/segmentation', () => {
    it('should return tenant segmentation', async () => {
      await createTestData();

      const response = await request(app)
        .get('/api/platform/analytics/segmentation')
        .set('Authorization', `Bearer ${analyticsViewerToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('segments');
      expect(response.body.data).toHaveProperty('totalSegments');
      expect(response.body.data).toHaveProperty('summary');

      expect(Array.isArray(response.body.data.segments)).toBe(true);
    });
  });

  describe('POST /api/platform/analytics/executive-report', () => {
    it('should generate executive report for platform admin', async () => {
      await createTestData();

      const response = await request(app)
        .post('/api/platform/analytics/executive-report')
        .set('Authorization', `Bearer ${systemUserToken}`)
        .send({
          startDate: '2024-01-01',
          endDate: '2024-01-31',
          includeForecasts: true,
          includeSegmentation: true,
          includeRecommendations: true,
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data).toHaveProperty('title');
      expect(response.body.data).toHaveProperty('generatedAt');
      expect(response.body.data).toHaveProperty('period');
      expect(response.body.data).toHaveProperty('summary');
      expect(response.body.data).toHaveProperty('metrics');
      expect(response.body.data).toHaveProperty('segments');
      expect(response.body.data).toHaveProperty('forecasts');
      expect(response.body.data).toHaveProperty('recommendations');
    });

    it('should require start and end dates', async () => {
      const response = await request(app)
        .post('/api/platform/analytics/executive-report')
        .set('Authorization', `Bearer ${systemUserToken}`)
        .send({
          includeForecasts: true,
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Start date and end date are required');
    });

    it('should deny access to finance admin', async () => {
      const response = await request(app)
        .post('/api/platform/analytics/executive-report')
        .set('Authorization', `Bearer ${financeAdminToken}`)
        .send({
          startDate: '2024-01-01',
          endDate: '2024-01-31',
        })
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Platform admin access required');
    });
  });

  describe('GET /api/platform/analytics/export', () => {
    it('should export revenue data as JSON', async () => {
      await createTestData();

      const response = await request(app)
        .get('/api/platform/analytics/export')
        .query({
          type: 'revenue',
          format: 'json',
        })
        .set('Authorization', `Bearer ${financeAdminToken}`)
        .expect(200);

      expect(response.headers['content-type']).toContain('application/json');
      expect(response.headers['content-disposition']).toContain('attachment');
    });

    it('should export metrics data as CSV', async () => {
      await createTestData();

      const response = await request(app)
        .get('/api/platform/analytics/export')
        .query({
          type: 'metrics',
          format: 'csv',
        })
        .set('Authorization', `Bearer ${systemUserToken}`)
        .expect(200);

      expect(response.headers['content-type']).toContain('text/csv');
      expect(response.headers['content-disposition']).toContain('attachment');
    });

    it('should validate export format', async () => {
      const response = await request(app)
        .get('/api/platform/analytics/export')
        .query({
          type: 'revenue',
          format: 'xml',
        })
        .set('Authorization', `Bearer ${systemUserToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should validate export type', async () => {
      const response = await request(app)
        .get('/api/platform/analytics/export')
        .query({
          type: 'invalid',
          format: 'json',
        })
        .set('Authorization', `Bearer ${systemUserToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  // Helper function to create test data
  async function createTestData() {
    // Create test tenants
    const tenant1 = await prisma.tenant.create({
      data: {
        id: 'tenant-1',
        name: 'Test Tenant 1',
        subscriptionTier: 'STARTER',
        createdAt: new Date('2024-01-01'),
      },
    });

    const tenant2 = await prisma.tenant.create({
      data: {
        id: 'tenant-2',
        name: 'Test Tenant 2',
        subscriptionTier: 'PROFESSIONAL',
        createdAt: new Date('2024-01-15'),
      },
    });

    // Create test subscriptions
    await prisma.subscription.create({
      data: {
        id: 'sub-1',
        tenantId: tenant1.id,
        monthlyFee: 99,
        billingCycle: BillingCycle.MONTHLY,
        status: SubscriptionStatus.ACTIVE,
        startDate: new Date('2024-01-01'),
      },
    });

    await prisma.subscription.create({
      data: {
        id: 'sub-2',
        tenantId: tenant2.id,
        monthlyFee: 299,
        billingCycle: BillingCycle.MONTHLY,
        status: SubscriptionStatus.ACTIVE,
        startDate: new Date('2024-01-15'),
      },
    });

    // Create test users
    await prisma.user.create({
      data: {
        id: 'user-1',
        email: '<EMAIL>',
        name: 'User 1',
        tenantId: tenant1.id,
        lastLoginAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
      },
    });

    await prisma.user.create({
      data: {
        id: 'user-2',
        email: '<EMAIL>',
        name: 'User 2',
        tenantId: tenant2.id,
        lastLoginAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      },
    });
  }
});
