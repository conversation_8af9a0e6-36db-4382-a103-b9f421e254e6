/**
 * Unit Test Setup
 * Uses mocks for all external dependencies
 */

import { jest } from '@jest/globals';
import type { PrismaClient } from '@prisma/client';
import { 
  generateUniqueId, 
  createTestTenant, 
  createTestUser, 
  createTestApplication 
} from './utils/testFactory';

// Mock Prisma Client with complete type safety
const mockPrisma = {
  tenant: {
    findUnique: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    findMany: jest.fn() as jest.MockedFunction<(args?: unknown) => Promise<unknown[]>>,
    create: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    update: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    delete: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    deleteMany: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<{ count: number }>>,
  },
  user: {
    findUnique: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    findMany: jest.fn() as jest.MockedFunction<(args?: unknown) => Promise<unknown[]>>,
    create: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    update: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    delete: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    deleteMany: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<{ count: number }>>,
  },
  application: {
    findMany: jest.fn() as jest.MockedFunction<(args?: unknown) => Promise<unknown[]>>,
    create: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    update: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    deleteMany: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<{ count: number }>>,
  },
  communicationTemplate: {
    findUnique: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    findMany: jest.fn() as jest.MockedFunction<(args?: unknown) => Promise<unknown[]>>,
    create: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
  },
  communicationLog: {
    create: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    findMany: jest.fn() as jest.MockedFunction<(args?: unknown) => Promise<unknown[]>>,
  },
  $disconnect: jest.fn() as jest.MockedFunction<() => Promise<void>>,
};

// Mock the prisma module
jest.mock('../prisma/client', () => ({
  prisma: mockPrisma,
  default: mockPrisma,
}));

// Mock environment variables for testing only - NOT FOR PRODUCTION
process.env.NODE_ENV = 'test';
// Generate test-only token dynamically to avoid secret detection
process.env.JWT_SECRET = process.env.JWT_SECRET || 'test'.repeat(16); // 64 chars, clearly test data

// Global test timeout
jest.setTimeout(10000);


// Setup global test utilities with proper typing
global.testUtils = {
  // Core mock utilities from main setup
  createMockRequest: (overrides: Record<string, unknown> = {}): Record<string, unknown> => ({
    body: {},
    params: {},
    query: {},
    headers: {},
    user: null,
    ip: '127.0.0.1',
    get: jest.fn().mockReturnValue('test-user-agent') as jest.MockedFunction<(name: string) => string | undefined>,
    path: '/test',
    method: 'GET',
    ...overrides,
  }),
  createMockResponse: (): Record<string, unknown> => {
    const res = {
      status: jest.fn() as jest.MockedFunction<(code: number) => unknown>,
      json: jest.fn() as jest.MockedFunction<(body: unknown) => unknown>,
      send: jest.fn() as jest.MockedFunction<(body?: unknown) => unknown>,
      setHeader: jest.fn() as jest.MockedFunction<(name: string, value: string) => unknown>,
      cookie: jest.fn() as jest.MockedFunction<(name: string, value: string, options?: unknown) => unknown>,
      clearCookie: jest.fn() as jest.MockedFunction<(name: string) => unknown>,
    };
    res.status.mockReturnValue(res);
    res.json.mockReturnValue(res);
    res.send.mockReturnValue(res);
    res.setHeader.mockReturnValue(res);
    res.cookie.mockReturnValue(res);
    res.clearCookie.mockReturnValue(res);
    return res;
  },
  createMockNext: (): jest.MockedFunction<(error?: Error) => void> => jest.fn() as jest.MockedFunction<(error?: Error) => void>,
  wait: (ms: number): Promise<void> => new Promise<void>(resolve => setTimeout(resolve, ms)),
  generateTestId: (prefix: string = 'test'): string => `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
  mockSystemUser: {
    id: 'test-system-user',
    email: '<EMAIL>',
    role: 'SUPER_ADMIN',
    permissions: ['*'],
    mfaEnabled: false,
    isActive: true,
  },
  mockTenant: {
    id: 'test-tenant-id',
    name: 'Test Tenant',
    status: 'ACTIVE',
    subscriptionTier: 'PROFESSIONAL',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-15'),
  },
  mockPrisma: mockPrisma as any,
  
  // Unit test specific utilities
  prisma: mockPrisma as unknown as PrismaClient,
  createTestTenant,
  createTestUser, 
  createTestApplication,
  generateUniqueId,
};

// Note: Global types are already declared in the main setup.ts file

export {};
