/**
 * Type-Safe Test Data Factory
 * Provides consistent, typed test data generation with proper defaults
 */

import { randomBytes } from 'crypto';

/**
 * Generate a unique ID for test isolation
 */
export const generateUniqueId = (): string => {
  return `test-${Date.now()}-${randomBytes(4).toString('hex')}`;
};

/**
 * Create test tenant data with proper typing and defaults
 */
export const createTestTenant = (overrides: Partial<{
  name: string;
  slug: string;
  status: string;
  subscriptionTier: string;
}> = {}) => {
  const id = generateUniqueId();
  const timestamp = new Date();
  
  return {
    id,
    name: overrides.name || `Test Tenant ${id}`,
    slug: overrides.slug || `test-slug-${id.toLowerCase()}`,
    domain: null,
    status: overrides.status || 'ACTIVE',
    subscriptionTier: overrides.subscriptionTier || 'PROFESSIONAL',
    branding: {},
    customDomain: null,
    settings: {},
    createdAt: timestamp,
    updatedAt: timestamp,
  };
};

/**
 * Create test user data with proper typing and defaults
 */
export const createTestUser = (tenantId: string, overrides: Partial<{
  email: string;
  role: string;
  name: string;
}> = {}) => {
  const id = generateUniqueId();
  const timestamp = new Date();
  
  return {
    id,
    tenantId,
    email: overrides.email || `test-${id}@example.com`,
    role: overrides.role || 'USER',
    name: overrides.name || `Test User ${id}`,
    emailVerified: false,
    emailVerifiedAt: null,
    passwordHash: null,
    lastLoginAt: null,
    createdAt: timestamp,
    updatedAt: timestamp,
    permissions: {},
    preferences: {},
    metadata: {},
  };
};

/**
 * Create test application data with proper typing and defaults  
 */
export const createTestApplication = (tenantId: string, overrides: Partial<{
  businessName: string;
  businessType: string;
  industry: string;
  requestedAmount: number;
  status: string;
}> = {}) => {
  const id = generateUniqueId();
  const timestamp = new Date();
  
  return {
    id,
    tenantId,
    applicationNumber: `APP-${id}`,
    businessName: overrides.businessName || `Test Business ${id}`,
    businessType: overrides.businessType || 'LLC',
    industry: overrides.industry || 'TECHNOLOGY',
    businessEmail: `business-${id}@example.com`,
    businessAddress: {
      street: '123 Test Street',
      city: 'Test City',
      state: 'CA',
      zip: '90210',
      country: 'USA'
    },
    requestedAmount: overrides.requestedAmount || 50000,
    status: overrides.status || 'IN_REVIEW',
    createdAt: timestamp,
    updatedAt: timestamp,
    completedAt: null,
    metadata: {},
    riskLevel: null,
  };
};

/**
 * Create test communication template
 */
export const createTestCommunicationTemplate = (overrides: Partial<{
  name: string;
  type: string;
  subject: string;
  category: string;
}> = {}) => {
  const id = generateUniqueId();
  const timestamp = new Date();
  
  return {
    id,
    name: overrides.name || `Test Template ${id}`,
    type: overrides.type || 'EMAIL',
    subject: overrides.subject || 'Test Subject',
    bodyText: 'Test body text',
    bodyHtml: '<p>Test body HTML</p>',
    variables: [],
    category: overrides.category || 'SYSTEM',
    isActive: true,
    createdBy: 'test-user',
    createdAt: timestamp,
    updatedAt: timestamp,
    usage: {
      totalSent: 0,
      lastUsed: undefined,
      successRate: 100,
      averageOpenRate: 85.5,
      averageClickRate: 12.3
    }
  };
};

/**
 * Create test subscription data
 */
export const createTestSubscription = (tenantId: string, overrides: Partial<{
  tier: string;
  status: string;
  monthlyFee: number;
}> = {}) => {
  const id = generateUniqueId();
  const timestamp = new Date();
  const nextMonth = new Date(timestamp);
  nextMonth.setMonth(nextMonth.getMonth() + 1);
  
  return {
    id,
    tenantId,
    tier: overrides.tier || 'PROFESSIONAL',
    status: overrides.status || 'ACTIVE',
    billingCycle: 'MONTHLY',
    monthlyFee: overrides.monthlyFee || 299.99,
    includedUsage: 1000,
    overageRate: 0.10,
    currentPeriodStart: timestamp,
    currentPeriodEnd: nextMonth,
    nextBillingDate: nextMonth,
    createdAt: timestamp,
    updatedAt: timestamp,
  };
};