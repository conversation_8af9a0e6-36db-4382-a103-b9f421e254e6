/**
 * Jest Test Setup
 * Global test setup for all test files
 */

import { jest } from '@jest/globals';

// Mock Prisma Client with complete type safety
const mockPrisma = {
  tenant: {
    findUnique: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    findMany: jest.fn() as jest.MockedFunction<(args?: unknown) => Promise<unknown[]>>,
    create: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    update: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    delete: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    deleteMany: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<{ count: number }>>,
  },
  user: {
    findUnique: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    findMany: jest.fn() as jest.MockedFunction<(args?: unknown) => Promise<unknown[]>>,
    create: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    update: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    delete: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    deleteMany: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<{ count: number }>>,
  },
  application: {
    findUnique: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    findMany: jest.fn() as jest.MockedFunction<(args?: unknown) => Promise<unknown[]>>,
    create: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    update: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    delete: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    deleteMany: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<{ count: number }>>,
  },
  communicationTemplate: {
    findUnique: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    findMany: jest.fn() as jest.MockedFunction<(args?: unknown) => Promise<unknown[]>>,
    create: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    update: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    delete: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    deleteMany: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<{ count: number }>>,
  },
  communicationLog: {
    findUnique: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    findMany: jest.fn() as jest.MockedFunction<(args?: unknown) => Promise<unknown[]>>,
    create: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    update: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    delete: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    deleteMany: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<{ count: number }>>,
  },
  subscription: {
    findUnique: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    findMany: jest.fn() as jest.MockedFunction<(args?: unknown) => Promise<unknown[]>>,
    create: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    update: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    delete: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    deleteMany: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<{ count: number }>>,
  },
  bulkCommunicationJob: {
    findUnique: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    findMany: jest.fn() as jest.MockedFunction<(args?: unknown) => Promise<unknown[]>>,
    create: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    update: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    delete: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<unknown>>,
    deleteMany: jest.fn() as jest.MockedFunction<(args: unknown) => Promise<{ count: number }>>,
  },
  $disconnect: jest.fn() as jest.MockedFunction<() => Promise<void>>,
};

// Mock the prisma module
jest.mock('../prisma/client', () => ({
  prisma: mockPrisma,
  default: mockPrisma,
}));

// Mock environment variables for testing only - NOT FOR PRODUCTION
process.env.NODE_ENV = 'test';
// Generate test-only tokens dynamically to avoid secret detection
process.env.JWT_SECRET = process.env.JWT_SECRET || 'test'.repeat(16); // 64 chars, clearly test data
process.env.SYSTEM_JWT_SECRET = process.env.SYSTEM_JWT_SECRET || 'test-system'.repeat(6); // 66 chars
process.env.SESSION_SECRET = process.env.SESSION_SECRET || 'test-session'.repeat(5); // 60 chars
process.env.SMTP_HOST = 'localhost';
process.env.SMTP_PORT = '587';
process.env.SMTP_USER = 'test';
process.env.SMTP_PASS = 'test';
process.env.SMTP_FROM = '<EMAIL>';
process.env.DATABASE_URL = 'postgresql://postgres:password@localhost:5433/underwriting_test';
process.env.TEST_DATABASE_URL = 'postgresql://postgres:password@localhost:5433/underwriting_test';
process.env.REDIS_URL = 'redis://localhost:6380';
process.env.OLLAMA_URL = 'http://localhost:11434';

// Global test timeout
jest.setTimeout(30000);

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error;
const originalConsoleLog = console.log;
const originalConsoleWarn = console.warn;

beforeEach((): void => {
  // Reset console mocks before each test
  console.error = jest.fn() as jest.MockedFunction<typeof console.error>;
  console.log = jest.fn() as jest.MockedFunction<typeof console.log>;
  console.warn = jest.fn() as jest.MockedFunction<typeof console.warn>;
});

afterEach((): void => {
  // Clear all mocks after each test
  jest.clearAllMocks();
  
  // Restore console methods if needed for debugging
  if (process.env.JEST_VERBOSE === 'true') {
    console.error = originalConsoleError;
    console.log = originalConsoleLog;
    console.warn = originalConsoleWarn;
  }
});

// Global test utilities
global.testUtils = {
  // Create mock request object
  createMockRequest: (overrides: Record<string, unknown> = {}): Record<string, unknown> => ({
    body: {},
    params: {},
    query: {},
    headers: {},
    user: null,
    ip: '127.0.0.1',
    get: jest.fn().mockReturnValue('test-user-agent') as jest.MockedFunction<(name: string) => string | undefined>,
    path: '/test',
    method: 'GET',
    ...overrides,
  }),

  // Create mock response object
  createMockResponse: (): Record<string, unknown> => {
    const res = {
      status: jest.fn() as jest.MockedFunction<(code: number) => unknown>,
      json: jest.fn() as jest.MockedFunction<(body: unknown) => unknown>,
      send: jest.fn() as jest.MockedFunction<(body?: unknown) => unknown>,
      setHeader: jest.fn() as jest.MockedFunction<(name: string, value: string) => unknown>,
      cookie: jest.fn() as jest.MockedFunction<(name: string, value: string, options?: unknown) => unknown>,
      clearCookie: jest.fn() as jest.MockedFunction<(name: string) => unknown>,
    };
    
    // Setup chainable methods
    res.status.mockReturnValue(res);
    res.json.mockReturnValue(res);
    res.send.mockReturnValue(res);
    res.setHeader.mockReturnValue(res);
    res.cookie.mockReturnValue(res);
    res.clearCookie.mockReturnValue(res);
    
    return res;
  },

  // Create mock next function
  createMockNext: (): jest.MockedFunction<(error?: Error) => void> => jest.fn() as jest.MockedFunction<(error?: Error) => void>,

  // Wait for async operations
  wait: (ms: number): Promise<void> => new Promise<void>(resolve => setTimeout(resolve, ms)),

  // Generate test IDs
  generateTestId: (prefix: string = 'test'): string => 
    `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,

  // Generate unique ID
  generateUniqueId: (): string => 
    `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,

  // Mock system user for auth tests
  mockSystemUser: {
    id: 'test-system-user',
    email: '<EMAIL>',
    role: 'SUPER_ADMIN',
    permissions: ['*'],
    mfaEnabled: false,
    isActive: true,
  },

  // Mock tenant for tests
  mockTenant: {
    id: 'test-tenant-id',
    name: 'Test Tenant',
    status: 'ACTIVE',
    subscriptionTier: 'PROFESSIONAL',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-15'),
  },

  // Test data factories
  createTestTenant: (overrides: Partial<{
    name: string;
    slug: string;
    status: string;
    subscriptionTier: string;
  }> = {}) => {
    const id = `tenant-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const name = overrides.name || 'Test Tenant';
    const slug = overrides.slug || name.toLowerCase().replace(/\s+/g, '-');
    
    return {
      id,
      name,
      slug,
      status: overrides.status || 'ACTIVE',
      subscriptionTier: overrides.subscriptionTier || 'PROFESSIONAL',
      createdAt: new Date(),
      updatedAt: new Date(),
      domain: null,
      branding: {},
      customDomain: null,
      settings: {},
      healthScore: 85,
      monthlyUsage: {
        aiTokensUsed: 1000,
        aiTokensQuota: 10000,
        applicationsProcessed: 50,
        activeUsers: 25,
        apiCalls: 500,
        storageUsed: 100
      },
      lastActivity: new Date(),
      supportTickets: 2,
      revenue: {
        current: 299.99,
        previous: 199.99,
        growth: 50.0,
        forecasted: 399.99
      },
      userCount: 25,
      applicationCount: 50,
    };
  },

  createTestUser: (tenantId: string, overrides: Partial<{
    email: string;
    role: string;
    name: string;
  }> = {}) => {
    const id = `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      id,
      tenantId,
      email: overrides.email || '<EMAIL>',
      role: overrides.role || 'USER',
      name: overrides.name || 'Test User',
      emailVerified: true,
      emailVerifiedAt: new Date(),
      passwordHash: 'hashed-password',
      lastLoginAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
      permissions: {},
      preferences: {},
      metadata: {},
    };
  },

  createTestApplication: (tenantId: string, overrides: Partial<{
    businessName: string;
    businessType: string;
    industry: string;
    requestedAmount: number;
    status: string;
  }> = {}) => {
    const id = `app-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      id,
      tenantId,
      applicationNumber: `APP-${Date.now()}`,
      businessName: overrides.businessName || 'Test Business',
      businessType: overrides.businessType || 'RETAIL',
      industry: overrides.industry || 'RETAIL',
      businessEmail: '<EMAIL>',
      businessAddress: {},
      requestedAmount: overrides.requestedAmount || 100000,
      status: overrides.status || 'PENDING',
      createdAt: new Date(),
      updatedAt: new Date(),
      completedAt: null,
      metadata: {},
      riskLevel: null,
    };
  },

  // Mock Prisma client for tests
  mockPrisma,
};


export {};