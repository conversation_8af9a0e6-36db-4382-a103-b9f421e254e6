/**
 * Integration Test Setup
 * Uses real test database for end-to-end testing
 */

import { PrismaClient } from '@prisma/client';
import { jest } from '@jest/globals';

// Real Prisma client for integration tests
const testPrisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.TEST_DATABASE_URL || 'postgresql://postgres:password@localhost:5433/underwriting_test',
    },
  },
});

// Test environment variables
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 'postgresql://postgres:password@localhost:5433/underwriting_test';

// Global test timeout for integration tests
jest.setTimeout(30000);

// Clean up test data before each test
beforeEach(async (): Promise<void> => {
  // Clean test data (order matters due to foreign keys)
  try {
    await testPrisma.application.deleteMany({
      where: { tenantId: { contains: 'test' } }
    });
    
    await testPrisma.user.deleteMany({
      where: { email: { contains: 'test' } }
    });
    
    await testPrisma.tenant.deleteMany({
      where: { name: { contains: 'Test' } }
    });
  } catch {
    // Ignore cleanup errors in tests
  }
});

// Disconnect after all tests
afterAll(async (): Promise<void> => {
  await testPrisma.$disconnect();
});

// Test utility functions
const generateUniqueId = (): string => `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

const createTestTenant = (overrides: Partial<{
  name: string;
  slug: string;
  status: string;
  subscriptionTier: string;
}> = {}) => ({
  id: generateUniqueId(),
  name: overrides.name || `Test Tenant ${Date.now()}`,
  slug: overrides.slug || `test-slug-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`,
  status: overrides.status || 'ACTIVE',
  subscriptionTier: overrides.subscriptionTier || 'PROFESSIONAL',
  createdAt: new Date(),
  updatedAt: new Date(),
  domain: null,
  branding: {},
  customDomain: null,
  settings: {},
});

const createTestUser = (tenantId: string, overrides: Partial<{
  email: string;
  role: string;
  name: string;
}> = {}) => ({
  id: generateUniqueId(),
  tenantId,
  email: overrides.email || `test-user-${Date.now()}@example.com`,
  role: overrides.role || 'USER',
  name: overrides.name || `Test User ${Date.now()}`,
  emailVerified: true,
  emailVerifiedAt: new Date(),
  passwordHash: 'hashed-password',
  lastLoginAt: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
  permissions: {},
  preferences: {},
  metadata: {},
});

// Extend existing global testUtils for integration tests
if (global.testUtils) {
  // Add integration-specific utilities to existing testUtils
  global.testUtils.prisma = testPrisma;
  global.testUtils.generateUniqueId = generateUniqueId;
  global.testUtils.createTestTenant = createTestTenant;
  global.testUtils.createTestUser = createTestUser;
} else {
  // Fallback if global.testUtils doesn't exist yet
  global.testUtils = {
    prisma: testPrisma,
    generateUniqueId,
    createTestTenant,
    createTestUser,
    createMockRequest: (): Record<string, unknown> => ({}),
    createMockResponse: (): Record<string, unknown> => ({}),
    createMockNext: (): jest.MockedFunction<(error?: Error) => void> => jest.fn() as jest.MockedFunction<(error?: Error) => void>,
    wait: (): Promise<void> => Promise.resolve(),
    generateTestId: (): string => '',
    mockSystemUser: {},
    mockTenant: {},
    mockPrisma: testPrisma as any,
  };
}

// Note: Global types are already declared in the main setup.ts file
// Integration tests extend the existing testUtils interface with:
// - prisma: PrismaClient (real database connection)
// - generateUniqueId: () => string  
// - createTestTenant: (overrides?) => Record<string, unknown>
// - createTestUser: (tenantId, overrides?) => Record<string, unknown>

export {};
