/**
 * Pipeline Service Tests
 *
 * Comprehensive tests for the decision pipeline framework,
 * including pipeline creation, execution, error handling, and monitoring.
 */

import { PipelineService } from '../../pipeline/services/PipelineService';
import {
  StageType,
  ExecutionTrigger,
  PipelineStatus,
  StageStatus,
  ErrorHandlingStrategy,
  PipelineConfig,
  PipelineExecutionResult,
} from '../../pipeline/types';
import { AiResponse } from '../../ai/types';

/**
 * Mock Prisma client with proper typing
 */
interface MockPrismaClient {
  decisionPipeline: {
    create: jest.MockedFunction<(args: { data: Record<string, unknown> }) => Promise<{ id: string; name: string }>>;
    findUnique: jest.MockedFunction<(args: { where: { id: string }; include?: Record<string, unknown> }) => Promise<Record<string, unknown> | null>>;
    findMany: jest.MockedFunction<(args: { where?: Record<string, unknown>; take?: number; skip?: number }) => Promise<Record<string, unknown>[]>>;
    update: jest.MockedFunction<(args: { where: { id: string }; data: Record<string, unknown> }) => Promise<Record<string, unknown>>>;
    count: jest.MockedFunction<(args: { where?: Record<string, unknown> }) => Promise<number>>;
  };
  pipelineExecution: {
    create: jest.MockedFunction<(args: { data: Record<string, unknown> }) => Promise<{ id: string }>>;
    findUnique: jest.MockedFunction<(args: { where: { id: string }; include?: Record<string, unknown> }) => Promise<Record<string, unknown> | null>>;
    findMany: jest.MockedFunction<(args: { where?: Record<string, unknown>; take?: number; skip?: number }) => Promise<Record<string, unknown>[]>>;
    update: jest.MockedFunction<(args: { where: { id: string }; data: Record<string, unknown> }) => Promise<Record<string, unknown>>>;
    count: jest.MockedFunction<(args: { where?: Record<string, unknown> }) => Promise<number>>;
  };
  pipelineStageExecution: {
    create: jest.MockedFunction<(args: { data: Record<string, unknown> }) => Promise<{ id: string }>>;
    updateMany: jest.MockedFunction<(args: { where?: Record<string, unknown>; data: Record<string, unknown> }) => Promise<{ count: number }>>;
    findMany: jest.MockedFunction<(args: { where?: Record<string, unknown>; take?: number; skip?: number }) => Promise<Record<string, unknown>[]>>;
  };
  pipelineMetrics: {
    findMany: jest.MockedFunction<(args: { where?: Record<string, unknown>; take?: number; skip?: number }) => Promise<Record<string, unknown>[]>>;
    create: jest.MockedFunction<(args: { data: Record<string, unknown> }) => Promise<{ id: string }>>;
  };
  application: {
    findUnique: jest.MockedFunction<(args: { where: { id: string }; include?: Record<string, unknown> }) => Promise<Record<string, unknown> | null>>;
  };
}

/**
 * Mock AI Model Manager with proper typing
 */
interface MockAiModelManager {
  executeRequest: jest.MockedFunction<(request: Record<string, unknown>) => Promise<AiResponse>>;
  selectModel: jest.MockedFunction<(criteria: Record<string, unknown>) => Promise<Record<string, unknown>>>;
  getModelMetrics: jest.MockedFunction<(provider?: string, modelId?: string) => Record<string, unknown>[]>;
  initialize: jest.MockedFunction<() => Promise<void>>;
}

// Create mock Prisma client
const mockPrisma: MockPrismaClient = {
  decisionPipeline: {
    create: jest.fn(),
    findUnique: jest.fn(),
    findMany: jest.fn(),
    update: jest.fn(),
    count: jest.fn(),
  },
  pipelineExecution: {
    create: jest.fn(),
    findUnique: jest.fn(),
    findMany: jest.fn(),
    update: jest.fn(),
    count: jest.fn(),
  },
  pipelineStageExecution: {
    create: jest.fn(),
    updateMany: jest.fn(),
    findMany: jest.fn(),
  },
  pipelineMetrics: {
    findMany: jest.fn(),
    create: jest.fn(),
  },
  application: {
    findUnique: jest.fn(),
  },
};

// Create mock AI Model Manager
const mockAiModelManager: MockAiModelManager = {
  executeRequest: jest.fn(),
  selectModel: jest.fn(),
  getModelMetrics: jest.fn(),
  initialize: jest.fn(),
};

describe('PipelineService', () => {
  let pipelineService: PipelineService;

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup mock application data
    mockPrisma.application.findUnique.mockResolvedValue({
      id: 'app-123',
      tenantId: 'tenant-456',
      businessName: 'Test Business',
      businessEmail: '<EMAIL>',
      requestedAmount: 100000,
      monthlyRevenue: 50000,
      businessType: 'RETAIL',
      applicationData: [
        {
          fieldName: 'businessName',
          value: 'Test Business',
        },
        {
          fieldName: 'businessEmail',
          value: '<EMAIL>',
        },
      ],
    });

    // Setup mock pipeline data
    mockPrisma.decisionPipeline.findUnique.mockResolvedValue({
      id: 'pipeline-123',
      name: 'Test Pipeline',
      description: 'Test pipeline description',
      version: '1.0',
      stages: [
        {
          name: 'input_validation',
          type: StageType.INPUT_VALIDATION,
          dependencies: [],
          config: {
            requiredFields: ['businessName', 'businessEmail'],
            validationRules: [],
            sanitization: true,
          },
          enabled: true,
          timeout: 30000,
        },
        {
          name: 'ai_analysis',
          type: StageType.AI_ANALYSIS,
          dependencies: ['input_validation'],
          config: {
            scenario: 'underwriting_analysis',
            prompts: [
              {
                name: 'system_prompt',
                template: 'You are an expert underwriter.',
                variables: [],
                required: true,
              },
            ],
            outputFormat: 'structured',
            modelSelection: {
              scenario: 'underwriting_analysis',
              maxCost: 0.1,
              maxLatency: 10000,
            },
          },
          enabled: true,
          timeout: 60000,
        },
      ],
      config: {
        timeout: 300000,
        maxRetries: 3,
        parallelExecution: false,
        failFast: true,
        errorHandling: ErrorHandlingStrategy.FAIL_FAST,
        monitoring: {
          enabled: true,
          stageMetrics: true,
        },
      },
      applicableTypes: ['loan'],
      isActive: true,
      isDefault: false,
      tags: ['test'],
      tenantId: 'tenant-456',
    });

    // Setup mock AI response
    mockAiModelManager.executeRequest.mockResolvedValue({
      id: 'ai-response-123',
      model: 'openai/gpt-4',
      choices: [
        {
          index: 0,
          message: {
            role: 'assistant',
            content:
              'Risk Level: Medium\nScore: 75\nRecommendation: Approve\nReasoning: Good business metrics',
          },
          finishReason: 'stop',
        },
      ],
      usage: {
        promptTokens: 100,
        completionTokens: 50,
        totalTokens: 150,
      },
      cost: {
        inputCost: 0.003,
        outputCost: 0.006,
        totalCost: 0.009,
      },
      metadata: {
        provider: 'openrouter',
        responseTime: 1500,
        timestamp: new Date(),
      },
    });

    // Type assertion needed to pass mock to PipelineService constructor
    pipelineService = new PipelineService(
      mockPrisma as never,
      mockAiModelManager as never
    );
  });

  describe('Pipeline Creation', () => {
    it('should create a new pipeline successfully', async () => {
      mockPrisma.decisionPipeline.create.mockResolvedValue({
        id: 'new-pipeline-123',
        name: 'New Test Pipeline',
      });

      const pipelineConfig: PipelineConfig = {
        name: 'New Test Pipeline',
        description: 'A new test pipeline',
        version: '1.0',
        stages: [
          {
            name: 'input_validation',
            type: StageType.INPUT_VALIDATION,
            dependencies: [],
            config: {
              requiredFields: ['businessName'],
              validationRules: [],
              sanitization: true,
            },
            enabled: true,
          },
        ],
        config: {
          timeout: 300000,
          maxRetries: 3,
          parallelExecution: false,
          failFast: true,
          errorHandling: ErrorHandlingStrategy.FAIL_FAST,
          monitoring: {
            enabled: true,
            stageMetrics: true,
            performanceTracking: true,
            costTracking: true,
            errorTracking: true,
          },
        },
        applicableTypes: ['loan'],
        isActive: true,
        isDefault: false,
        tags: ['test'],
      };

      const pipelineId = await pipelineService.createPipeline(
        'tenant-456',
        pipelineConfig
      );

      expect(pipelineId).toBe('new-pipeline-123');
      expect(mockPrisma.decisionPipeline.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            tenantId: 'tenant-456',
            name: 'New Test Pipeline',
            version: '1.0',
            isActive: true,
          }) as Record<string, unknown>,
        }) as { data: Record<string, unknown> }
      );
    });

    it('should validate pipeline configuration before creation', async () => {
      const invalidConfig: PipelineConfig = {
        name: '', // Invalid - empty name
        version: '1.0',
        stages: [], // Invalid - no stages
        config: {
          timeout: 300000,
          maxRetries: 3,
          parallelExecution: false,
          failFast: true,
          errorHandling: ErrorHandlingStrategy.FAIL_FAST,
          monitoring: {
            enabled: true,
            stageMetrics: true,
            performanceTracking: true,
            costTracking: true,
            errorTracking: true,
          },
        },
        applicableTypes: [],
        isActive: true,
        isDefault: false,
        tags: [],
      };

      await expect(
        pipelineService.createPipeline('tenant-456', invalidConfig)
      ).rejects.toThrow();
    });

    it('should detect circular dependencies in stages', async () => {
      const configWithCircularDeps: PipelineConfig = {
        name: 'Circular Pipeline',
        version: '1.0',
        stages: [
          {
            name: 'stage_a',
            type: StageType.INPUT_VALIDATION,
            dependencies: ['stage_b'],
            config: {},
            enabled: true,
          },
          {
            name: 'stage_b',
            type: StageType.AI_ANALYSIS,
            dependencies: ['stage_a'], // Circular dependency
            config: {},
            enabled: true,
          },
        ],
        config: {
          timeout: 300000,
          maxRetries: 3,
          parallelExecution: false,
          failFast: true,
          errorHandling: ErrorHandlingStrategy.FAIL_FAST,
          monitoring: {
            enabled: true,
            stageMetrics: true,
            performanceTracking: true,
            costTracking: true,
            errorTracking: true,
          },
        },
        applicableTypes: ['loan'],
        isActive: true,
        isDefault: false,
        tags: [],
      };

      await expect(
        pipelineService.createPipeline('tenant-456', configWithCircularDeps)
      ).rejects.toThrow('Circular dependency detected');
    });
  });

  describe('Pipeline from Template', () => {
    it('should create pipeline from basic_underwriting template', async () => {
      mockPrisma.decisionPipeline.create.mockResolvedValue({
        id: 'template-pipeline-123',
        name: 'Basic Underwriting Pipeline - 2024-01-01',
      });

      const pipelineId = await pipelineService.createPipelineFromTemplate(
        'tenant-456',
        'basic_underwriting',
        {
          name: 'Custom Underwriting Pipeline',
          description: 'Customized underwriting pipeline',
        }
      );

      expect(pipelineId).toBe('template-pipeline-123');
      expect(mockPrisma.decisionPipeline.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            name: 'Custom Underwriting Pipeline',
            description: 'Customized underwriting pipeline',
            tenantId: 'tenant-456',
          }) as Record<string, unknown>,
        }) as { data: Record<string, unknown> }
      );
    });

    it('should apply stage overrides when creating from template', async () => {
      mockPrisma.decisionPipeline.create.mockResolvedValue({
        id: 'template-pipeline-123',
        name: 'Template Pipeline',
      });

      await pipelineService.createPipelineFromTemplate(
        'tenant-456',
        'basic_underwriting',
        {
          stageOverrides: {
            input_validation: {
              config: {
                requiredFields: [
                  'businessName',
                  'businessEmail',
                  'customField',
                ],
              },
            },
          },
        }
      );

      const createCallArgs = mockPrisma.decisionPipeline.create.mock.calls[0];
      if (createCallArgs) {
        const createData = createCallArgs[0].data as { stages: string };
        const stages = JSON.parse(createData.stages) as Array<{ name: string; config: { requiredFields: string[] } }>;
        const validationStage = stages.find(
          (stage) => stage.name === 'input_validation'
        );

        expect(validationStage?.config.requiredFields).toContain('customField');
      }
    });

    it('should throw error for non-existent template', async () => {
      await expect(
        pipelineService.createPipelineFromTemplate(
          'tenant-456',
          'non_existent_template'
        )
      ).rejects.toThrow('Template not found');
    });
  });

  describe('Pipeline Execution', () => {
    beforeEach(async () => {
      await pipelineService.initialize();
    });

    it('should execute pipeline successfully', async () => {
      // Mock successful execution record creation
      mockPrisma.pipelineExecution.create.mockResolvedValue({
        id: 'exec-123',
      });

      mockPrisma.pipelineStageExecution.create.mockResolvedValue({
        id: 'stage-exec-123',
      });

      mockPrisma.pipelineExecution.update.mockResolvedValue({});
      mockPrisma.pipelineStageExecution.updateMany.mockResolvedValue({ count: 1 });

      const result = await pipelineService.executePipeline(
        'pipeline-123',
        'app-123',
        {
          tenantId: 'tenant-456',
          userId: 'user-789',
          trigger: ExecutionTrigger.MANUAL,
        }
      );

      expect(result.status).toBe(PipelineStatus.COMPLETED);
      expect(result.stageResults.size).toBeGreaterThan(0);
      expect(mockPrisma.pipelineExecution.create).toHaveBeenCalled();
    }, 10000);

    it('should handle stage failures according to error strategy', async () => {
      // Mock AI service to fail
      mockAiModelManager.executeRequest.mockRejectedValue(
        new Error('AI service unavailable')
      );

      mockPrisma.pipelineExecution.create.mockResolvedValue({ id: 'exec-123' });
      mockPrisma.pipelineStageExecution.create.mockResolvedValue({
        id: 'stage-exec-123',
      });
      mockPrisma.pipelineExecution.update.mockResolvedValue({});
      mockPrisma.pipelineStageExecution.updateMany.mockResolvedValue({ count: 1 });

      const result = await pipelineService.executePipeline(
        'pipeline-123',
        'app-123',
        {
          tenantId: 'tenant-456',
          trigger: ExecutionTrigger.MANUAL,
        }
      );

      expect(result.status).toBe(PipelineStatus.FAILED);
      expect(result.error).toBeDefined();
    }, 10000);

    it('should track execution metrics', async () => {
      mockPrisma.pipelineExecution.create.mockResolvedValue({ id: 'exec-123' });
      mockPrisma.pipelineStageExecution.create.mockResolvedValue({
        id: 'stage-exec-123',
      });
      mockPrisma.pipelineExecution.update.mockResolvedValue({});
      mockPrisma.pipelineStageExecution.updateMany.mockResolvedValue({ count: 1 });

      const result = await pipelineService.executePipeline(
        'pipeline-123',
        'app-123',
        { tenantId: 'tenant-456' }
      );

      expect(result.metrics).toBeDefined();
      expect(result.metrics.totalStages).toBeGreaterThan(0);
      expect(result.cost).toBeGreaterThanOrEqual(0);
    }, 10000);
  });

  describe('Pipeline Management', () => {
    it('should list pipelines for tenant', async () => {
      const mockPipelines = [
        { id: 'p1', name: 'Pipeline 1', isActive: true },
        { id: 'p2', name: 'Pipeline 2', isActive: false },
      ];

      mockPrisma.decisionPipeline.findMany.mockResolvedValue(mockPipelines);
      mockPrisma.decisionPipeline.count.mockResolvedValue(2);

      const result = await pipelineService.listPipelines('tenant-456', {
        isActive: true,
        limit: 10,
      });

      expect(result.pipelines).toHaveLength(2);
      expect(result.total).toBe(2);
    });

    it('should update pipeline configuration', async () => {
      const updates = {
        name: 'Updated Pipeline Name',
        description: 'Updated description',
      };

      mockPrisma.decisionPipeline.update.mockResolvedValue({
        id: 'pipeline-123',
        ...updates,
      });

      await pipelineService.updatePipeline('pipeline-123', updates);

      expect(mockPrisma.decisionPipeline.update).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { id: 'pipeline-123' },
          data: expect.objectContaining({
            name: 'Updated Pipeline Name',
            description: 'Updated description',
          }) as Record<string, unknown>,
        }) as { where: { id: string }; data: Record<string, unknown> }
      );
    });

    it('should get pipeline metrics', async () => {
      const mockMetrics = [
        {
          id: 'metric-1',
          totalExecutions: 100,
          successfulExecutions: 95,
          averageDuration: 180000,
          totalCost: 25.5,
        },
      ];

      mockPrisma.pipelineMetrics.findMany.mockResolvedValue(mockMetrics);

      const metrics = await pipelineService.getPipelineMetrics('pipeline-123', {
        granularity: 'daily',
      });

      expect(metrics).toHaveLength(1);
      const firstMetric = metrics[0] as { totalExecutions: number };
      expect(firstMetric.totalExecutions).toBe(100);
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      mockPrisma.decisionPipeline.create.mockRejectedValue(
        new Error('Database connection failed')
      );

      const config: PipelineConfig = {
        name: 'Test Pipeline',
        version: '1.0',
        stages: [
          {
            name: 'test_stage',
            type: StageType.INPUT_VALIDATION,
            dependencies: [],
            config: {},
            enabled: true,
          },
        ],
        config: {
          timeout: 300000,
          maxRetries: 3,
          parallelExecution: false,
          failFast: true,
          errorHandling: ErrorHandlingStrategy.FAIL_FAST,
          monitoring: {
            enabled: true,
            stageMetrics: true,
            performanceTracking: true,
            costTracking: true,
            errorTracking: true,
          },
        },
        applicableTypes: ['loan'],
        isActive: true,
        isDefault: false,
        tags: [],
      };

      await expect(
        pipelineService.createPipeline('tenant-456', config)
      ).rejects.toThrow('Database connection failed');
    });

    it('should handle missing application data', async () => {
      mockPrisma.application.findUnique.mockResolvedValue(null);

      await expect(
        pipelineService.executePipeline('pipeline-123', 'non-existent-app')
      ).rejects.toThrow('Application not found');
    });

    it('should handle missing pipeline configuration', async () => {
      mockPrisma.decisionPipeline.findUnique.mockResolvedValue(null);

      await expect(
        pipelineService.executePipeline('non-existent-pipeline', 'app-123')
      ).rejects.toThrow('Pipeline not found');
    });
  });

  describe('Execution Status and Control', () => {
    it('should get execution status', async () => {
      const mockExecution = {
        id: 'exec-123',
        status: PipelineStatus.COMPLETED,
        startedAt: new Date(),
        completedAt: new Date(),
        totalDuration: 180000,
        inputData: { test: 'data' },
        outputData: { result: 'success' },
        totalCost: 0.05,
        stageExecutions: [
          {
            stageName: 'input_validation',
            status: StageStatus.COMPLETED,
            startedAt: new Date(),
            completedAt: new Date(),
            duration: 1000,
            cost: 0.001,
            retryCount: 0,
          },
        ],
      };

      mockPrisma.pipelineExecution.findUnique.mockResolvedValue(mockExecution);

      const status = await pipelineService.getExecutionStatus('exec-123');

      expect(status.executionId).toBe('exec-123');
      expect(status.status).toBe(PipelineStatus.COMPLETED);
      expect(status.stageResults.size).toBe(1);
    });

    it('should cancel running execution', async () => {
      await expect(
        pipelineService.cancelExecution('non-existent-exec')
      ).rejects.toThrow('No active execution found');
    });

    it('should retry failed execution', async () => {
      const mockFailedExecution = {
        id: 'failed-exec-123',
        pipelineId: 'pipeline-123',
        applicationId: 'app-123',
        tenantId: 'tenant-456',
        status: PipelineStatus.FAILED,
        inputData: { test: 'data' },
      };

      mockPrisma.pipelineExecution.findUnique.mockResolvedValue(
        mockFailedExecution
      );
      mockPrisma.pipelineExecution.create.mockResolvedValue({
        id: 'retry-exec-123',
      });
      mockPrisma.pipelineStageExecution.create.mockResolvedValue({
        id: 'stage-exec-123',
      });
      mockPrisma.pipelineExecution.update.mockResolvedValue({});
      mockPrisma.pipelineStageExecution.updateMany.mockResolvedValue({ count: 1 });

      const retryResult: PipelineExecutionResult =
        await pipelineService.retryExecution('failed-exec-123');

      expect(retryResult.executionId).toBeDefined();
      expect(mockPrisma.pipelineExecution.create).toHaveBeenCalled();
    }, 10000);
  });
});