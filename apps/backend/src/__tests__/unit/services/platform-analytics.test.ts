/**
 * Platform Analytics Service Unit Tests
 * Tests for Story 9.3: Platform Revenue & Analytics Dashboard
 */

import { PlatformAnalyticsService } from '../../../services/platform-analytics';
import { prisma } from '../../../prisma/client';
import { SubscriptionStatus, BillingCycle } from '@prisma/client';

// Mock Prisma client
jest.mock('../../../prisma/client', () => ({
  prisma: {
    subscription: {
      findMany: jest.fn(),
      count: jest.fn(),
    },
    tenant: {
      findMany: jest.fn(),
      count: jest.fn(),
      findUnique: jest.fn(),
    },
    payment: {
      count: jest.fn(),
    },
  },
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe('PlatformAnalyticsService', () => {
  let analyticsService: PlatformAnalyticsService;

  beforeEach(() => {
    analyticsService = new PlatformAnalyticsService();
    jest.clearAllMocks();
  });

  describe('getRevenueAnalytics', () => {
    it('should calculate MRR correctly for monthly subscriptions', async () => {
      // Mock active subscriptions
      mockPrisma.subscription.findMany.mockResolvedValue([
        {
          id: '1',
          monthlyFee: 100,
          billingCycle: BillingCycle.MONTHLY,
          status: SubscriptionStatus.ACTIVE,
          startDate: new Date('2024-01-01'),
          endDate: null,
        },
        {
          id: '2',
          monthlyFee: 200,
          billingCycle: BillingCycle.MONTHLY,
          status: SubscriptionStatus.ACTIVE,
          startDate: new Date('2024-01-15'),
          endDate: null,
        },
      ] as any);

      // Mock previous month data for growth calculation
      mockPrisma.subscription.findMany
        .mockResolvedValueOnce([
          {
            id: '1',
            monthlyFee: 100,
            billingCycle: BillingCycle.MONTHLY,
            status: SubscriptionStatus.ACTIVE,
            startDate: new Date('2024-01-01'),
            endDate: null,
          },
          {
            id: '2',
            monthlyFee: 200,
            billingCycle: BillingCycle.MONTHLY,
            status: SubscriptionStatus.ACTIVE,
            startDate: new Date('2024-01-15'),
            endDate: null,
          },
        ] as any)
        .mockResolvedValueOnce([
          {
            id: '1',
            monthlyFee: 100,
            billingCycle: BillingCycle.MONTHLY,
            status: SubscriptionStatus.ACTIVE,
            startDate: new Date('2024-01-01'),
            endDate: null,
          },
        ] as any);

      // Mock churn calculation
      mockPrisma.subscription.count
        .mockResolvedValueOnce(2) // Active at start
        .mockResolvedValueOnce(0); // Churned during month

      // Mock tenant data for cohort analysis
      mockPrisma.tenant.findMany.mockResolvedValue([]);

      const result = await analyticsService.getRevenueAnalytics();

      expect(result.mrr).toBe(300); // 100 + 200
      expect(result.arr).toBe(3600); // 300 * 12
      expect(result.growthRate).toBe(200); // (300 - 100) / 100 * 100
      expect(result.churnRate).toBe(0); // 0 / 2 * 100
    });

    it('should handle yearly subscriptions correctly', async () => {
      mockPrisma.subscription.findMany.mockResolvedValue([
        {
          id: '1',
          monthlyFee: 1200, // Yearly fee
          billingCycle: BillingCycle.YEARLY,
          status: SubscriptionStatus.ACTIVE,
          startDate: new Date('2024-01-01'),
          endDate: null,
        },
      ] as any);

      // Mock other required calls
      mockPrisma.subscription.findMany.mockResolvedValueOnce([]);
      mockPrisma.subscription.count.mockResolvedValue(0);
      mockPrisma.tenant.findMany.mockResolvedValue([]);

      const result = await analyticsService.getRevenueAnalytics();

      expect(result.mrr).toBe(100); // 1200 / 12
      expect(result.arr).toBe(1200); // 100 * 12
    });

    it('should calculate churn rate correctly', async () => {
      // Mock active subscriptions
      mockPrisma.subscription.findMany.mockResolvedValue([]);

      // Mock churn calculation
      mockPrisma.subscription.count
        .mockResolvedValueOnce(10) // Active at start of month
        .mockResolvedValueOnce(2); // Churned during month

      // Mock other required calls
      mockPrisma.tenant.findMany.mockResolvedValue([]);

      const result = await analyticsService.getRevenueAnalytics();

      expect(result.churnRate).toBe(20); // 2 / 10 * 100
    });
  });

  describe('getPlatformMetrics', () => {
    it('should calculate platform metrics correctly', async () => {
      // Mock tenant counts
      mockPrisma.tenant.count
        .mockResolvedValueOnce(100) // Total tenants
        .mockResolvedValueOnce(80) // Active tenants
        .mockResolvedValueOnce(15) // Churned tenants
        .mockResolvedValueOnce(90) // Tenants at start
        .mockResolvedValueOnce(100); // Tenants at end

      // Mock subscription data for MRR calculation
      mockPrisma.subscription.findMany.mockResolvedValue([
        {
          id: '1',
          monthlyFee: 100,
          billingCycle: BillingCycle.MONTHLY,
          status: SubscriptionStatus.ACTIVE,
        },
        {
          id: '2',
          monthlyFee: 200,
          billingCycle: BillingCycle.MONTHLY,
          status: SubscriptionStatus.ACTIVE,
        },
      ] as any);

      const result = await analyticsService.getPlatformMetrics();

      expect(result.totalTenants).toBe(100);
      expect(result.activeTenants).toBe(80);
      expect(result.churnedTenants).toBe(15);
      expect(result.totalRevenue).toBe(3600); // (100 + 200) * 12
      expect(result.averageRevenuePerTenant).toBe(45); // 3600 / 80
      expect(result.tenantGrowthRate).toBe(11.11); // (100 - 90) / 90 * 100
    });
  });

  describe('predictChurnRisk', () => {
    it('should predict churn risk for tenants', async () => {
      const mockTenants = [
        {
          id: 'tenant1',
          name: 'Test Tenant 1',
          subscriptions: [
            {
              status: SubscriptionStatus.ACTIVE,
              startDate: new Date('2024-01-01'),
            },
          ],
          users: [
            {
              lastLoginAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000), // 45 days ago
            },
          ],
        },
        {
          id: 'tenant2',
          name: 'Test Tenant 2',
          subscriptions: [
            {
              status: SubscriptionStatus.ACTIVE,
              startDate: new Date('2024-06-01'),
            },
          ],
          users: [
            {
              lastLoginAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
            },
          ],
        },
      ];

      mockPrisma.tenant.findMany.mockResolvedValue(mockTenants as any);
      mockPrisma.payment.count.mockResolvedValue(0); // No payment issues

      const result = await analyticsService.predictChurnRisk();

      expect(result).toHaveLength(2);
      expect(result[0].riskScore).toBeGreaterThan(result[1].riskScore);
      expect(result[0].tenantId).toBe('tenant1');
      expect(result[1].tenantId).toBe('tenant2');
    });

    it('should handle tenants with no users', async () => {
      const mockTenants = [
        {
          id: 'tenant1',
          name: 'Test Tenant 1',
          subscriptions: [
            {
              status: SubscriptionStatus.ACTIVE,
              startDate: new Date('2024-01-01'),
            },
          ],
          users: [],
        },
      ];

      mockPrisma.tenant.findMany.mockResolvedValue(mockTenants as any);
      mockPrisma.payment.count.mockResolvedValue(0);

      const result = await analyticsService.predictChurnRisk();

      expect(result).toHaveLength(1);
      expect(result[0].riskScore).toBeGreaterThan(0);
    });
  });

  describe('getTenantSegmentation', () => {
    it('should segment tenants by subscription tier', async () => {
      const mockTenants = [
        {
          id: 'tenant1',
          subscriptionTier: 'STARTER',
          subscriptions: [
            {
              status: SubscriptionStatus.ACTIVE,
              monthlyFee: 99,
              billingCycle: BillingCycle.MONTHLY,
            },
          ],
        },
        {
          id: 'tenant2',
          subscriptionTier: 'PROFESSIONAL',
          subscriptions: [
            {
              status: SubscriptionStatus.ACTIVE,
              monthlyFee: 299,
              billingCycle: BillingCycle.MONTHLY,
            },
          ],
        },
      ];

      mockPrisma.tenant.findMany
        .mockResolvedValueOnce([mockTenants[0]] as any) // STARTER
        .mockResolvedValueOnce([mockTenants[1]] as any) // PROFESSIONAL
        .mockResolvedValueOnce([] as any); // ENTERPRISE

      // Mock churn and growth calculations
      mockPrisma.tenant.count.mockResolvedValue(0);

      const result = await analyticsService.getTenantSegmentation();

      expect(result).toHaveLength(2);
      expect(result[0].segment).toBe('STARTER');
      expect(result[0].tenantCount).toBe(1);
      expect(result[0].totalRevenue).toBe(99);
      expect(result[1].segment).toBe('PROFESSIONAL');
      expect(result[1].tenantCount).toBe(1);
      expect(result[1].totalRevenue).toBe(299);
    });
  });

  describe('generateExecutiveReport', () => {
    it('should generate comprehensive executive report', async () => {
      const period = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31'),
      };

      // Mock all required data
      mockPrisma.subscription.findMany.mockResolvedValue([
        {
          id: '1',
          monthlyFee: 100,
          billingCycle: BillingCycle.MONTHLY,
          status: SubscriptionStatus.ACTIVE,
        },
      ] as any);

      mockPrisma.subscription.count.mockResolvedValue(0);
      mockPrisma.tenant.count.mockResolvedValue(10);
      mockPrisma.tenant.findMany.mockResolvedValue([]);

      const result = await analyticsService.generateExecutiveReport(period, {
        includeForecasts: true,
        includeSegmentation: true,
        includeRecommendations: true,
      });

      expect(result.id).toBeDefined();
      expect(result.title).toContain('Executive Report');
      expect(result.period).toEqual(period);
      expect(result.summary).toBeDefined();
      expect(result.metrics).toBeDefined();
      expect(result.segments).toBeDefined();
      expect(result.forecasts).toBeDefined();
      expect(result.recommendations).toBeDefined();
    });
  });

  describe('exportAnalyticsData', () => {
    it('should export data as JSON', async () => {
      const testData = { mrr: 1000, arr: 12000 };
      
      const result = await analyticsService.exportAnalyticsData('json', testData, 'test');
      
      expect(result).toBe(JSON.stringify(testData, null, 2));
    });

    it('should export data as CSV', async () => {
      const testData = [
        { metric: 'MRR', value: 1000 },
        { metric: 'ARR', value: 12000 },
      ];
      
      const result = await analyticsService.exportAnalyticsData('csv', testData, 'test');
      
      expect(result).toContain('metric,value');
      expect(result).toContain('MRR,1000');
      expect(result).toContain('ARR,12000');
    });

    it('should export data as PDF', async () => {
      const testData = { mrr: 1000, arr: 12000 };
      
      const result = await analyticsService.exportAnalyticsData('pdf', testData, 'test');
      
      expect(result).toBeInstanceOf(Buffer);
    });

    it('should throw error for unsupported format', async () => {
      const testData = { mrr: 1000 };
      
      await expect(
        analyticsService.exportAnalyticsData('xml' as any, testData, 'test')
      ).rejects.toThrow('Unsupported export format: xml');
    });
  });
});
