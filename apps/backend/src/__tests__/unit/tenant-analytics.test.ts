/**
 * Tenant Analytics Service Tests
 * Comprehensive test coverage for cross-tenant analytics and reporting
 */

// @ts-nocheck - Test file with mock data that doesn't match full Prisma schemas
import { jest } from '@jest/globals';
import { tenantAnalyticsService, AnalyticsFilters } from '../../services/tenant-analytics';
// import { PrismaClient } from '@prisma/client';

// Mock the prisma client import - define inline to avoid hoisting issues
jest.mock('../../prisma/client', () => ({
  prisma: {
    tenant: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
    },
    application: {
      findMany: jest.fn(),
    },
    user: {
      findMany: jest.fn(),
    },
    subscription: {
      findMany: jest.fn(),
    },
  },
}));

// Mock data
const mockTenant = {
  id: 'tenant-1',
  name: 'Test Tenant',
  createdAt: new Date('2024-01-01'),
  status: 'ACTIVE',
  subscriptions: [
    {
      id: 'sub-1',
      status: 'ACTIVE',
      tier: 'PROFESSIONAL',
      monthlyFee: 99.99,
    },
  ],
  users: [
    {
      id: 'user-1',
      email: '<EMAIL>',
      lastLoginAt: new Date('2024-01-15'),
    },
  ],
  applications: [
    {
      id: 'app-1',
      createdAt: new Date('2024-01-10'),
      completedAt: new Date('2024-01-12'),
      status: 'APPROVED',
    },
    {
      id: 'app-2',
      createdAt: new Date('2024-01-11'),
      completedAt: null,
      status: 'IN_PROGRESS',
    },
  ],
};

// Import the mocked prisma to get access to the mock functions
import { prisma } from '../../prisma/client';
jest.mocked(prisma);

describe('TenantAnalyticsService', () => {
  const mockPrisma = prisma as jest.Mocked<typeof prisma>;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getTenantAnalytics', () => {
    it('should return comprehensive tenant analytics', async () => {
      mockPrisma.tenant.findUnique.mockResolvedValue(mockTenant as any);
      mockPrisma.application.findMany.mockResolvedValue(mockTenant.applications as any);
      mockPrisma.user.findMany.mockResolvedValue(mockTenant.users as any);

      const analytics = await tenantAnalyticsService.getTenantAnalytics('tenant-1', {
        includeComparisons: true,
        includeTrends: true,
      });

      expect(analytics).toBeDefined();
      expect(analytics.tenantId).toBe('tenant-1');
      expect(analytics.tenantName).toBe('Test Tenant');
      expect(analytics.metrics).toBeDefined();
      expect(analytics.healthIndicators).toBeDefined();
      expect(analytics.riskFactors).toBeDefined();

      // Verify metrics structure
      expect(analytics.metrics.usageMetrics).toBeDefined();
      expect(analytics.metrics.performanceMetrics).toBeDefined();
      expect(analytics.metrics.businessMetrics).toBeDefined();
      expect(analytics.metrics.engagementMetrics).toBeDefined();

      // Check specific metric calculations
      expect(analytics.metrics.usageMetrics.applicationCount).toBe(2);
      expect(analytics.metrics.usageMetrics.activeApplications).toBe(1);
      expect(analytics.metrics.usageMetrics.processedApplications).toBe(1);
    });

    it('should calculate usage metrics correctly', async () => {
      mockPrisma.tenant.findUnique.mockResolvedValue(mockTenant as any);
      mockPrisma.application.findMany.mockResolvedValue(mockTenant.applications as any);
      mockPrisma.user.findMany.mockResolvedValue(mockTenant.users as any);

      const analytics = await tenantAnalyticsService.getTenantAnalytics('tenant-1');

      const usage = analytics.metrics.usageMetrics;
      expect(usage.applicationCount).toBe(2);
      expect(usage.activeApplications).toBe(1); // IN_PROGRESS
      expect(usage.processedApplications).toBe(1); // APPROVED
      expect(usage.averageProcessingTime).toBeGreaterThan(0);
    });

    it('should calculate business metrics correctly', async () => {
      mockPrisma.tenant.findUnique.mockResolvedValue(mockTenant as any);
      mockPrisma.application.findMany.mockResolvedValue(mockTenant.applications as any);
      mockPrisma.user.findMany.mockResolvedValue(mockTenant.users as any);
      mockPrisma.subscription.findMany.mockResolvedValue(mockTenant.subscriptions as any);

      const analytics = await tenantAnalyticsService.getTenantAnalytics('tenant-1');

      const business = analytics.metrics.businessMetrics;
      expect(business.monthlyRevenue).toBe(99.99);
      expect(business.yearlyRevenue).toBe(99.99 * 12);
      expect(business.customerLifetimeValue).toBeGreaterThan(0);
    });

    it('should calculate health indicators correctly', async () => {
      mockPrisma.tenant.findUnique.mockResolvedValue(mockTenant as any);
      mockPrisma.application.findMany.mockResolvedValue(mockTenant.applications as any);
      mockPrisma.user.findMany.mockResolvedValue(mockTenant.users as any);

      const analytics = await tenantAnalyticsService.getTenantAnalytics('tenant-1');

      const health = analytics.healthIndicators;
      expect(health.overallHealth).toBeGreaterThanOrEqual(0);
      expect(health.overallHealth).toBeLessThanOrEqual(100);
      expect(health.categories).toBeDefined();
      expect(health.categories.performance).toBeDefined();
      expect(health.categories.usage).toBeDefined();
      expect(health.categories.business).toBeDefined();
      expect(health.categories.engagement).toBeDefined();
    });

    it('should handle tenant not found', async () => {
      mockPrisma.tenant.findUnique.mockResolvedValue(null);

      await expect(
        tenantAnalyticsService.getTenantAnalytics('non-existent-tenant')
      ).rejects.toThrow('Tenant not found: non-existent-tenant');
    });

    it('should include trends when requested', async () => {
      mockPrisma.tenant.findUnique.mockResolvedValue(mockTenant as any);
      mockPrisma.application.findMany.mockResolvedValue(mockTenant.applications as any);
      mockPrisma.user.findMany.mockResolvedValue(mockTenant.users as any);

      const analytics = await tenantAnalyticsService.getTenantAnalytics('tenant-1', {
        includeTrends: true,
      });

      expect(analytics.trends).toBeDefined();
      expect(analytics.trends.data).toBeInstanceOf(Array);
      expect(analytics.trends.data.length).toBeGreaterThan(0);
      
      // Check trend data structure
      const trendPoint = analytics.trends.data[0];
      if (trendPoint) {
        expect(trendPoint.date).toBeInstanceOf(Date);
        expect(typeof trendPoint.value).toBe('number');
        expect(typeof trendPoint.change).toBe('number');
        expect(typeof trendPoint.changePercentage).toBe('number');
      }
    });

    it('should include comparisons when requested', async () => {
      mockPrisma.tenant.findUnique.mockResolvedValue(mockTenant as any);
      mockPrisma.application.findMany.mockResolvedValue(mockTenant.applications as any);
      mockPrisma.user.findMany.mockResolvedValue(mockTenant.users as any);

      const analytics = await tenantAnalyticsService.getTenantAnalytics('tenant-1', {
        includeComparisons: true,
      });

      expect(analytics.comparisons).toBeDefined();
      expect(analytics.comparisons.industryBenchmark).toBeGreaterThanOrEqual(0);
      expect(analytics.comparisons.platformAverage).toBeGreaterThanOrEqual(0);
      expect(analytics.comparisons.tierAverage).toBeGreaterThanOrEqual(0);
      expect(analytics.comparisons.ranking).toBeGreaterThan(0);
      expect(analytics.comparisons.percentile).toBeGreaterThan(0);
    });
  });

  describe('getCrossTenantAnalytics', () => {
    const mockTenants = [
      { id: 'tenant-1', name: 'Tenant 1' },
      { id: 'tenant-2', name: 'Tenant 2' },
    ];

    beforeEach(() => {
      mockPrisma.tenant.findMany.mockResolvedValue(mockTenants);
    });

    it('should return cross-tenant analytics', async () => {
      const filters: AnalyticsFilters = {
        dateRange: {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31'),
        },
        tenantIds: ['tenant-1', 'tenant-2'],
      };

      mockPrisma.tenant.findUnique
        .mockResolvedValueOnce({ ...mockTenant, id: 'tenant-1' })
        .mockResolvedValueOnce({ ...mockTenant, id: 'tenant-2' });
      mockPrisma.application.findMany.mockResolvedValue(mockTenant.applications as any);
      mockPrisma.user.findMany.mockResolvedValue(mockTenant.users as any);
      mockPrisma.subscription.findMany.mockResolvedValue(mockTenant.subscriptions);

      const result = await tenantAnalyticsService.getCrossTenantAnalytics(filters);

      expect(result).toBeDefined();
      expect(result.tenants).toHaveLength(2);
      expect(result.comparisons).toBeInstanceOf(Array);
      expect(result.platformSummary).toBeDefined();

      // Check platform summary
      expect(result.platformSummary.totalTenants).toBe(2);
      expect(result.platformSummary.totalRevenue).toBeGreaterThan(0);
      expect(result.platformSummary.averageHealth).toBeGreaterThanOrEqual(0);
      expect(result.platformSummary.topPerformers).toBeInstanceOf(Array);
      expect(result.platformSummary.riskTenants).toBeInstanceOf(Array);
    });

    it('should handle empty tenant list', async () => {
      const filters: AnalyticsFilters = {
        dateRange: {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31'),
        },
        tenantIds: [],
      };

      const result = await tenantAnalyticsService.getCrossTenantAnalytics(filters);

      expect(result.tenants).toHaveLength(0);
      expect(result.platformSummary.totalTenants).toBe(0);
    });
  });

  describe('generateAnalyticsReport', () => {
    it('should generate comprehensive analytics report', async () => {
      const filters: AnalyticsFilters = {
        dateRange: {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31'),
        },
      };

      mockPrisma.tenant.findMany.mockResolvedValue([mockTenant]);
      mockPrisma.tenant.findUnique.mockResolvedValue(mockTenant as any);
      mockPrisma.application.findMany.mockResolvedValue(mockTenant.applications as any);
      mockPrisma.user.findMany.mockResolvedValue(mockTenant.users as any);

      const report = await tenantAnalyticsService.generateAnalyticsReport(
        'Monthly Analytics Report',
        filters,
        {
          includeInsights: true,
          includeRecommendations: true,
        }
      );

      expect(report).toBeDefined();
      expect(report.id).toBeDefined();
      expect(report.title).toBe('Monthly Analytics Report');
      expect(report.generatedAt).toBeInstanceOf(Date);
      expect(report.filters).toEqual(filters);
      expect(report.data).toBeInstanceOf(Array);
      expect(report.comparisons).toBeInstanceOf(Array);
      expect(report.insights).toBeInstanceOf(Array);
      expect(report.recommendations).toBeInstanceOf(Array);
      expect(report.exportFormats).toContain('json');
      expect(report.exportFormats).toContain('csv');
    });

    it('should generate report without insights and recommendations', async () => {
      const filters: AnalyticsFilters = {
        dateRange: {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31'),
        },
      };

      mockPrisma.tenant.findMany.mockResolvedValue([mockTenant]);
      mockPrisma.tenant.findUnique.mockResolvedValue(mockTenant as any);
      mockPrisma.application.findMany.mockResolvedValue(mockTenant.applications as any);
      mockPrisma.user.findMany.mockResolvedValue(mockTenant.users as any);

      const report = await tenantAnalyticsService.generateAnalyticsReport(
        'Basic Report',
        filters
      );

      expect(report.insights).toEqual([]);
      expect(report.recommendations).toEqual([]);
    });
  });

  describe('Risk Assessment', () => {
    it('should identify high churn risk tenants', async () => {
      const mockHighRiskTenant = {
        ...mockTenant,
        subscriptions: [
          {
            id: 'sub-1',
            status: 'ACTIVE',
            tier: 'BASIC',
            amount: 19.99,
          },
        ],
        users: [
          {
            id: 'user-1',
            email: '<EMAIL>',
            lastLoginAt: new Date('2024-01-01'), // Very old login
          },
        ],
      };

      mockPrisma.tenant.findUnique.mockResolvedValue(mockHighRiskTenant as any);
      mockPrisma.application.findMany.mockResolvedValue([] as any);
      mockPrisma.user.findMany.mockResolvedValue(mockHighRiskTenant.users as any);

      const analytics = await tenantAnalyticsService.getTenantAnalytics('tenant-1');

      expect(analytics.riskFactors).toBeInstanceOf(Array);
      
      // Should have some risk factors for a low-activity tenant
      if (analytics.riskFactors.length > 0) {
        const riskFactor = analytics.riskFactors[0];
        if (riskFactor) {
          expect(riskFactor.type).toBeDefined();
          expect(riskFactor.severity).toBeDefined();
          expect(riskFactor.score).toBeGreaterThan(0);
          expect(riskFactor.description).toBeDefined();
          expect(riskFactor.recommendedActions).toBeInstanceOf(Array);
        }
      }
    });

    it('should generate appropriate alerts for poor health', async () => {
      // Mock a tenant with poor performance
      mockPrisma.tenant.findUnique.mockResolvedValue({
        ...mockTenant,
        // applications: [],
        // users: [],
      } as any);
      mockPrisma.application.findMany.mockResolvedValue([] as any);
      mockPrisma.user.findMany.mockResolvedValue([]);

      const analytics = await tenantAnalyticsService.getTenantAnalytics('tenant-1');

      expect(analytics.healthIndicators.alerts).toBeInstanceOf(Array);
      
      // Check alert structure if any exist
      if (analytics.healthIndicators.alerts.length > 0) {
        const alert = analytics.healthIndicators.alerts[0];
        if (alert) {
          expect(alert.type).toMatch(/warning|critical|info/);
          expect(alert.category).toBeDefined();
          expect(alert.message).toBeDefined();
          expect(alert.trend).toMatch(/improving|declining|stable/);
        }
      }
    });
  });

  describe('Data Validation', () => {
    it('should handle missing subscription data gracefully', async () => {
      const tenantWithoutSubs = {
        ...mockTenant,
        subscriptions: null,
      };

      mockPrisma.tenant.findUnique.mockResolvedValue(tenantWithoutSubs as any);
      mockPrisma.application.findMany.mockResolvedValue(mockTenant.applications as any);
      mockPrisma.user.findMany.mockResolvedValue(mockTenant.users as any);

      const analytics = await tenantAnalyticsService.getTenantAnalytics('tenant-1');

      expect(analytics.metrics.businessMetrics.monthlyRevenue).toBe(0);
      expect(analytics.metrics.businessMetrics.yearlyRevenue).toBe(0);
    });

    it('should handle empty application data', async () => {
      mockPrisma.tenant.findUnique.mockResolvedValue({
        ...mockTenant,
        // applications: [],
      } as any);
      mockPrisma.application.findMany.mockResolvedValue([] as any);
      mockPrisma.user.findMany.mockResolvedValue(mockTenant.users as any);

      const analytics = await tenantAnalyticsService.getTenantAnalytics('tenant-1');

      expect(analytics.metrics.usageMetrics.applicationCount).toBe(0);
      expect(analytics.metrics.usageMetrics.activeApplications).toBe(0);
      expect(analytics.metrics.usageMetrics.processedApplications).toBe(0);
    });

    it('should handle date range filtering', async () => {
      const dateRange = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-15'),
      };

      mockPrisma.tenant.findUnique.mockResolvedValue(mockTenant as any);
      mockPrisma.application.findMany.mockResolvedValue(
        mockTenant.applications.filter(app => 
          app.createdAt >= dateRange.start && app.createdAt <= dateRange.end
        )
      );
      mockPrisma.user.findMany.mockResolvedValue(mockTenant.users as any);

      const analytics = await tenantAnalyticsService.getTenantAnalytics('tenant-1', {
        dateRange,
      });

      // Should only count applications within date range
      expect(analytics.metrics.usageMetrics.applicationCount).toBe(2);
    });
  });

  describe('CSV Export', () => {
    it('should convert report data to CSV format', () => {
      const mockReport = {
        id: 'report-1',
        title: 'Test Report',
        description: 'Test description',
        generatedAt: new Date(),
        filters: {
          dateRange: {
            start: new Date('2024-01-01'),
            end: new Date('2024-01-31'),
          },
        },
        data: [
          {
            tenantId: 'tenant-1',
            tenantName: 'Test Tenant',
            healthIndicators: { overallHealth: 85 },
            metrics: {
              businessMetrics: { monthlyRevenue: 99.99 },
              usageMetrics: { applicationCount: 5 },
              performanceMetrics: { successRate: 95.5 },
            },
            riskFactors: [{ score: 15 }],
          },
        ],
        comparisons: [],
        insights: [],
        recommendations: [],
        exportFormats: ['csv'],
      } as Record<string, unknown>;

      // Access the private method for testing
      const csvData = (tenantAnalyticsService as any as { convertToCSV: (report: Record<string, unknown>) => string }).convertToCSV(mockReport);

      expect(typeof csvData).toBe('string');
      expect(csvData).toContain('Tenant ID');
      expect(csvData).toContain('Tenant Name');
      expect(csvData).toContain('tenant-1');
      expect(csvData).toContain('Test Tenant');
      expect(csvData).toContain('99.99');
    });
  });
});