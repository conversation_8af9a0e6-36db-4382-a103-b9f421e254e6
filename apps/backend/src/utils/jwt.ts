import jwt from 'jsonwebtoken';
import { getEnv } from '@underwriting/config';

const env = getEnv();

export interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  tenantId?: string;
  isSystemUser?: boolean;
  sessionId: string;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
}

export interface TempTokenPayload {
  userId: string;
  email: string;
  purpose: string;
  exp?: number;
}

export class JWTService {
  private static readonly ACCESS_TOKEN_SECRET = ((): string => {
    if (!env.JWT_SECRET) {
      throw new Error('JWT_SECRET environment variable is required');
    }
    if (env.JWT_SECRET.length < 32) {
      throw new Error('JWT_SECRET must be at least 32 characters long');
    }
    return env.JWT_SECRET;
  })();
  
  private static readonly REFRESH_TOKEN_SECRET = ((): string => {
    if (!env.JWT_SECRET) {
      throw new Error('JWT_SECRET environment variable is required');
    }
    return env.JWT_SECRET + '_refresh_salt_2024';
  })();

  /**
   * Generate access and refresh token pair
   */
  static generateTokenPair(
    payload: Omit<JWTPayload, 'sessionId'>,
    sessionId: string
  ): TokenPair {
    const fullPayload: JWTPayload = {
      ...payload,
      sessionId,
    };

    const accessToken = jwt.sign(fullPayload, this.ACCESS_TOKEN_SECRET, {
      expiresIn: '15m', // 15 minutes default
    });

    const refreshToken = jwt.sign(
      { userId: payload.userId, sessionId },
      this.REFRESH_TOKEN_SECRET,
      {
        expiresIn: '7d', // 7 days for refresh tokens
      }
    );

    return { accessToken, refreshToken };
  }

  /**
   * Verify and decode access token
   */
  static verifyAccessToken(token: string): JWTPayload | null {
    try {
      const decoded = jwt.verify(token, this.ACCESS_TOKEN_SECRET) as JWTPayload;

      return decoded;
    } catch (error) {
      // Log error without exposing token details
      console.error('Access token verification failed:', error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  }

  /**
   * Verify and decode refresh token
   */
  static verifyRefreshToken(
    token: string
  ): { userId: string; sessionId: string } | null {
    try {
      const decoded = jwt.verify(token, this.REFRESH_TOKEN_SECRET) as {
        userId: string;
        sessionId: string;
      };

      return decoded;
    } catch (error) {
      // Log error without exposing token details
      console.error('Refresh token verification failed:', error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  }

  /**
   * Extract token from Authorization header
   */
  static extractTokenFromHeader(authHeader: string | undefined): string | null {
    if (!authHeader) return null;

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') return null;

    return parts[1] || null;
  }

  /**
   * Get token expiration time
   */
  static getTokenExpiration(token: string): Date | null {
    try {
      const decoded = jwt.decode(token) as { exp?: number } | null;
      if (!decoded?.exp) return null;

      return new Date(decoded.exp * 1000);
    } catch {
      return null;
    }
  }

  /**
   * Check if token is expired
   */
  static isTokenExpired(token: string): boolean {
    const expiration = this.getTokenExpiration(token);
    if (!expiration) return true;

    return expiration < new Date();
  }

  /**
   * Generate temporary token for MFA verification
   */
  static generateTempToken(payload: Omit<TempTokenPayload, 'exp'>): string {
    const tempPayload: TempTokenPayload = {
      ...payload,
    };

    return jwt.sign(tempPayload, this.ACCESS_TOKEN_SECRET, {
      expiresIn: '5m', // 5 minutes for MFA verification
    });
  }

  /**
   * Verify and decode temporary token
   */
  static verifyTempToken(token: string): TempTokenPayload | null {
    try {
      const decoded = jwt.verify(token, this.ACCESS_TOKEN_SECRET) as TempTokenPayload;
      return decoded;
    } catch (error) {
      // Log error without exposing token details
      console.error('Temporary token verification failed:', error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  }
}
