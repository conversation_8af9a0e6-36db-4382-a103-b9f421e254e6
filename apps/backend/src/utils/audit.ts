import { prisma } from '../prisma/client';
import { AuditRiskLevel } from '@prisma/client';

export interface AuditLogData {
  action: string;
  entityType: string;
  entityId: string;
  userId: string;
  tenantId?: string;
  ipAddress: string;
  userAgent: string;
  oldValues?: unknown;
  newValues?: unknown;
  metadata?: unknown;
  reason?: string;
  riskLevel?: AuditRiskLevel;
}

export interface SystemAuditLogData {
  action: string;
  entityType: string;
  entityId: string;
  userId: string;
  ipAddress: string;
  userAgent: string;
  oldValues?: unknown;
  newValues?: unknown;
  metadata?: unknown;
}

export class AuditLogger {
  /**
   * Log tenant-scoped audit event
   */
  static async log(data: AuditLogData): Promise<void> {
    try {
      // Skip if no userId or tenantId provided for tenant audit logs
      if (!data.userId || !data.tenantId) {
        return;
      }

      await prisma.auditLog.create({
        data: {
          action: data.action,
          entityType: data.entityType,
          entityId: data.entityId,
          userId: data.userId,
          tenantId: data.tenantId,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          oldValues: data.oldValues
            ? JSON.parse(JSON.stringify(data.oldValues))
            : null,
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          newValues: data.newValues
            ? JSON.parse(JSON.stringify(data.newValues))
            : null,
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          metadata: data.metadata
            ? JSON.parse(JSON.stringify(data.metadata))
            : null,
          reason: data.reason,
          riskLevel: data.riskLevel || 'LOW',
        },
      });
    } catch (error) {
      console.error('Audit logging failed:', error);
      // Don't throw error to avoid breaking main operations
    }
  }

  /**
   * Log system-level audit event (platform administration)
   */
  static async logSystem(data: SystemAuditLogData): Promise<void> {
    try {
      await prisma.systemUserAuditLog.create({
        data: {
          action: data.action,
          entityType: data.entityType,
          entityId: data.entityId || undefined,
          userId: data.userId,
          ipAddress: data.ipAddress || undefined,
          userAgent: data.userAgent || undefined,
          oldValues: data.oldValues ? this.safeCloneForDatabase(data.oldValues) : undefined,
          newValues: data.newValues ? this.safeCloneForDatabase(data.newValues) : undefined,
          metadata: data.metadata ? this.safeCloneForDatabase(data.metadata) : undefined,
        },
      });
    } catch (error) {
      console.error('System audit logging failed:', error);
      // Don't throw error to avoid breaking main operations
    }
  }

  /**
   * Log authentication events
   */
  static async logAuth(
    action:
      | 'LOGIN'
      | 'LOGOUT'
      | 'LOGIN_FAILED'
      | 'PASSWORD_CHANGED'
      | 'SESSION_EXPIRED',
    userId: string,
    ipAddress: string,
    userAgent: string,
    email?: string,
    isSystemUser: boolean = false,
    metadata?: unknown
  ): Promise<void> {
    const auditData = {
      action,
      entityType: isSystemUser ? 'SystemUser' : 'User',
      entityId: userId,
      userId,
      ipAddress,
      userAgent,
      metadata: {
        email,
        isSystemUser,
        ...((metadata as Record<string, unknown>) || {}),
      },
      riskLevel:
        action === 'LOGIN_FAILED'
          ? ('MEDIUM' as AuditRiskLevel)
          : ('LOW' as AuditRiskLevel),
    };

    if (isSystemUser) {
      await this.logSystem({
        action: auditData.action,
        entityType: auditData.entityType,
        entityId: auditData.entityId,
        userId: userId,
        ipAddress: auditData.ipAddress,
        userAgent: auditData.userAgent,
        metadata: auditData.metadata,
      });
    } else {
      await this.log(auditData);
    }
  }

  /**
   * Log user management events
   */
  static async logUserManagement(
    action:
      | 'USER_CREATED'
      | 'USER_UPDATED'
      | 'USER_DELETED'
      | 'USER_SUSPENDED'
      | 'USER_ACTIVATED',
    targetUserId: string,
    performedBy: string,
    ipAddress: string,
    userAgent: string,
    tenantId?: string,
    oldValues?: unknown,
    newValues?: unknown
  ): Promise<void> {
    await this.log({
      action,
      entityType: 'User',
      entityId: targetUserId,
      userId: performedBy,
      tenantId,
      oldValues,
      newValues,
      ipAddress,
      userAgent,
      riskLevel: 'MEDIUM',
    });
  }

  /**
   * Log tenant management events
   */
  static async logTenantManagement(
    action:
      | 'TENANT_CREATED'
      | 'TENANT_UPDATED'
      | 'TENANT_SUSPENDED'
      | 'TENANT_ACTIVATED'
      | 'TENANT_DELETED',
    tenantId: string,
    performedBy: string,
    ipAddress: string,
    userAgent: string,
    oldValues?: unknown,
    newValues?: unknown,
    isSystemUser: boolean = false
  ): Promise<void> {
    const auditData = {
      action,
      entityType: 'Tenant',
      entityId: tenantId,
      userId: performedBy,
      oldValues,
      newValues,
      ipAddress,
      userAgent,
      metadata: { isSystemUser },
      riskLevel: 'HIGH' as AuditRiskLevel,
    };

    if (isSystemUser) {
      await this.logSystem({
        action: auditData.action,
        entityType: auditData.entityType,
        entityId: auditData.entityId,
        userId: auditData.userId,
        ipAddress: auditData.ipAddress,
        userAgent: auditData.userAgent,
        oldValues: auditData.oldValues,
        newValues: auditData.newValues,
        metadata: auditData.metadata,
      });
    } else {
      await this.log(auditData);
    }
  }

  /**
   * Log application events
   */
  static async logApplication(
    action:
      | 'APPLICATION_CREATED'
      | 'APPLICATION_UPDATED'
      | 'APPLICATION_SUBMITTED'
      | 'APPLICATION_APPROVED'
      | 'APPLICATION_REJECTED',
    applicationId: string,
    userId: string,
    ipAddress: string,
    userAgent: string,
    tenantId?: string,
    oldValues?: unknown,
    newValues?: unknown,
    reason?: string
  ): Promise<void> {
    await this.log({
      action,
      entityType: 'Application',
      entityId: applicationId,
      userId,
      tenantId,
      oldValues,
      newValues,
      ipAddress,
      userAgent,
      reason,
      riskLevel:
        action.includes('APPROVED') || action.includes('REJECTED')
          ? 'HIGH'
          : 'LOW',
    });
  }

  /**
   * Log document events
   */
  static async logDocument(
    action:
      | 'DOCUMENT_UPLOADED'
      | 'DOCUMENT_PROCESSED'
      | 'DOCUMENT_APPROVED'
      | 'DOCUMENT_REJECTED'
      | 'DOCUMENT_DELETED',
    documentId: string,
    applicationId: string,
    userId: string,
    ipAddress: string,
    userAgent: string,
    tenantId?: string,
    metadata?: unknown
  ): Promise<void> {
    await this.log({
      action,
      entityType: 'Document',
      entityId: documentId,
      userId,
      tenantId,
      metadata: {
        applicationId,
        ...((metadata as Record<string, unknown>) || {}),
      },
      ipAddress,
      userAgent,
      riskLevel: action === 'DOCUMENT_DELETED' ? 'MEDIUM' : 'LOW',
    });
  }

  /**
   * Log security events
   */
  static async logSecurity(
    action:
      | 'SECURITY_VIOLATION'
      | 'UNAUTHORIZED_ACCESS'
      | 'SUSPICIOUS_ACTIVITY'
      | 'DATA_BREACH'
      | 'COMPLIANCE_VIOLATION',
    entityType: string,
    entityId: string,
    userId: string,
    ipAddress: string,
    userAgent: string,
    tenantId?: string,
    details?: unknown
  ): Promise<void> {
    await this.log({
      action,
      entityType,
      entityId,
      userId,
      tenantId,
      metadata: details,
      ipAddress,
      userAgent,
      riskLevel: 'CRITICAL',
    });
  }

  /**
   * Get audit logs for entity
   */
  static async getEntityAuditLogs(
    entityType: string,
    entityId: string,
    tenantId?: string,
    limit: number = 50
  ): Promise<unknown[]> {
    const where: Record<string, unknown> = {
      entityType,
      entityId,
    };

    if (tenantId) {
      where.tenantId = tenantId;
    }

    return await prisma.auditLog.findMany({
      where,
      orderBy: { timestamp: 'desc' },
      take: limit,
      include: {
        user: {
          select: {
            email: true,
            name: true,
          },
        },
      },
    });
  }

  /**
   * Get audit logs for user
   */
  static async getUserAuditLogs(
    userId: string,
    tenantId?: string,
    limit: number = 50
  ): Promise<unknown[]> {
    const where: Record<string, unknown> = {
      userId,
    };

    if (tenantId) {
      where.tenantId = tenantId;
    }

    return await prisma.auditLog.findMany({
      where,
      orderBy: { timestamp: 'desc' },
      take: limit,
    });
  }

  /**
   * Get system audit logs
   */
  static async getSystemAuditLogs(
    userId?: string,
    limit: number = 50
  ): Promise<unknown[]> {
    const where: Record<string, unknown> = {};

    if (userId) {
      where.userId = userId;
    }

    return await prisma.systemUserAuditLog.findMany({
      where,
      orderBy: { timestamp: 'desc' },
      take: limit,
      include: {
        user: {
          select: {
            email: true,
            name: true,
          },
        },
      },
    });
  }

  /**
   * Get audit logs by risk level
   */
  static async getAuditLogsByRisk(
    riskLevel: AuditRiskLevel,
    tenantId?: string,
    limit: number = 50
  ): Promise<unknown[]> {
    const where: Record<string, unknown> = {
      riskLevel,
    };

    if (tenantId) {
      where.tenantId = tenantId;
    }

    return await prisma.auditLog.findMany({
      where,
      orderBy: { timestamp: 'desc' },
      take: limit,
      include: {
        user: {
          select: {
            email: true,
            name: true,
          },
        },
      },
    });
  }

  /**
   * Safely clone data for database storage, ensuring JSON compatibility
   */
  private static safeCloneForDatabase(data: unknown): object | string | number | boolean | undefined {
    if (data === null || data === undefined) {
      return undefined;
    }
    
    try {
      // Use JSON serialization to ensure database compatibility
      const jsonString = JSON.stringify(data);
      const serialized: unknown = JSON.parse(jsonString);
      
      // Return undefined if the result is null to avoid Prisma type issues
      if (serialized === null || serialized === undefined) {
        return undefined;
      }
      
      // Type guard to ensure we return the correct types
      if (typeof serialized === 'string' || 
          typeof serialized === 'number' || 
          typeof serialized === 'boolean' ||
          (typeof serialized === 'object' && serialized !== null)) {
        return serialized;
      }
      
      return undefined;
    } catch (error) {
      console.warn('Failed to serialize data for database:', error);
      return undefined;
    }
  }
}
