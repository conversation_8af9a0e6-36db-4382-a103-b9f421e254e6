/**
 * Decision Pipeline Types and Interfaces
 *
 * Defines the types and interfaces for the decision pipeline framework
 * that orchestrates AI-powered underwriting decisions through standardized stages.
 */

import { AiScenario } from '../ai/types';

/**
 * Core Pipeline Types
 */
export interface PipelineConfig {
  name: string;
  description?: string;
  version: string;
  stages: PipelineStageConfig[];
  config: PipelineGlobalConfig;
  applicableTypes: string[];
  isActive: boolean;
  isDefault: boolean;
  tags: string[];
}

export interface PipelineGlobalConfig {
  timeout: number; // Global pipeline timeout in ms
  maxRetries: number; // Global retry limit
  parallelExecution: boolean; // Allow parallel stage execution
  failFast: boolean; // Stop on first failure
  errorHandling: ErrorHandlingStrategy;
  monitoring: MonitoringConfig;
  costLimits?: CostLimits;
}

export interface PipelineStageConfig {
  name: string;
  type: StageType;
  description?: string;
  dependencies: string[]; // Names of stages this depends on
  config: Record<string, unknown>; // Stage-specific configuration
  timeout?: number; // Stage-specific timeout
  retryPolicy?: RetryPolicy;
  enabled: boolean;
  conditional?: ConditionalExecution;
  parallel?: boolean; // Can run in parallel with other stages
}

/**
 * Stage Types
 */
export enum StageType {
  INPUT_VALIDATION = 'input_validation',
  DATA_ENRICHMENT = 'data_enrichment',
  AI_ANALYSIS = 'ai_analysis',
  RULE_APPLICATION = 'rule_application',
  RISK_ASSESSMENT = 'risk_assessment',
  FINAL_DECISION = 'final_decision',
  NOTIFICATION = 'notification',
  AUDIT_LOG = 'audit_log',
  CUSTOM = 'custom',
}

/**
 * Execution Context and Results
 */
export interface PipelineExecutionContext {
  executionId: string;
  pipelineId: string;
  applicationId: string;
  tenantId: string;
  userId?: string;
  trigger: ExecutionTrigger;
  inputData: Record<string, unknown>;
  environment: 'development' | 'staging' | 'production' | 'test';
  timestamp: Date;
  metadata?: Record<string, unknown>;
}

export interface PipelineExecutionResult {
  executionId: string;
  status: PipelineStatus;
  startedAt: Date;
  completedAt?: Date;
  duration?: number;
  inputData: Record<string, unknown>;
  outputData?: PipelineOutput;
  stageResults: Map<string, StageExecutionResult>;
  error?: PipelineError;
  metrics: PipelineMetrics;
  cost: number;
}

export interface StageExecutionResult {
  stageName: string;
  status: StageStatus;
  startedAt: Date;
  completedAt?: Date;
  duration?: number;
  inputData?: Record<string, unknown>;
  outputData?: Record<string, unknown>;
  error?: StageError;
  retryCount: number;
  cost: number;
  metrics?: Record<string, unknown>;
}

export interface PipelineOutput {
  decision: UnderwritingDecision;
  confidence: number;
  reasoning: string[];
  riskScore: number;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  factors: DecisionFactor[];
  recommendations: string[];
  metadata: Record<string, unknown>;
}

export interface UnderwritingDecision {
  result: 'APPROVE' | 'DENY' | 'REVIEW' | 'REQUEST_MORE_INFO';
  amount?: number;
  terms?: LoanTerms;
  conditions?: string[];
  expiresAt?: Date;
}

export interface LoanTerms {
  interestRate: number;
  termMonths: number;
  fees?: Fee[];
  collateralRequired?: boolean;
  guarantorRequired?: boolean;
}

export interface Fee {
  type: string;
  amount: number;
  description: string;
}

export interface DecisionFactor {
  name: string;
  value: unknown;
  weight: number;
  impact: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL';
  source: string;
  confidence: number;
}

/**
 * Error Handling
 */
export interface PipelineError {
  code: string;
  message: string;
  stage?: string;
  details?: Record<string, unknown>;
  timestamp: Date;
  recoverable: boolean;
}

export interface StageError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  timestamp: Date;
  recoverable: boolean;
}

export enum ErrorHandlingStrategy {
  FAIL_FAST = 'fail_fast',
  CONTINUE_ON_ERROR = 'continue_on_error',
  FALLBACK_TO_DEFAULT = 'fallback_to_default',
  RETRY_WITH_BACKOFF = 'retry_with_backoff',
}

export interface RetryPolicy {
  maxAttempts: number;
  initialDelayMs: number;
  maxDelayMs: number;
  backoffMultiplier: number;
  retryableErrors: string[];
}

/**
 * Conditional Execution
 */
export interface ConditionalExecution {
  condition: string; // JavaScript expression
  context: string[]; // Required context variables
  skipIfTrue?: boolean; // Skip stage if condition is true
  skipIfFalse?: boolean; // Skip stage if condition is false
}

/**
 * Monitoring and Metrics
 */
export interface MonitoringConfig {
  enabled: boolean;
  stageMetrics: boolean;
  performanceTracking: boolean;
  costTracking: boolean;
  errorTracking: boolean;
  alerting?: AlertingConfig;
}

export interface AlertingConfig {
  enabled: boolean;
  channels: string[];
  thresholds: {
    errorRate?: number;
    responseTime?: number;
    cost?: number;
  };
}

export interface PipelineMetrics {
  totalStages: number;
  completedStages: number;
  failedStages: number;
  skippedStages: number;
  totalCost: number;
  averageStageTime: number;
  bottleneckStage?: string;
}

export interface CostLimits {
  maxTotalCost: number;
  maxStageCost: number;
  currency: string;
}

/**
 * Enums
 */
export enum PipelineStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  TIMEOUT = 'TIMEOUT',
}

export enum StageStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  SKIPPED = 'SKIPPED',
  TIMEOUT = 'TIMEOUT',
}

export enum ExecutionTrigger {
  MANUAL = 'manual',
  AUTOMATIC = 'automatic',
  RETRY = 'retry',
  WEBHOOK = 'webhook',
  SCHEDULED = 'scheduled',
}

/**
 * Stage Processor Interface
 */
export interface IPipelineStage {
  readonly name: string;
  readonly type: StageType;
  readonly dependencies: string[];

  /**
   * Execute the stage with given context and input data
   */
  execute(
    context: PipelineExecutionContext,
    inputData: Record<string, unknown>,
    config: Record<string, unknown>
  ): Promise<StageExecutionResult>;

  /**
   * Validate stage configuration
   */
  validateConfig(config: Record<string, unknown>): Promise<boolean>;

  /**
   * Check if stage can be executed (dependencies met, conditions satisfied)
   */
  canExecute(
    context: PipelineExecutionContext,
    completedStages: string[],
    stageResults: Map<string, StageExecutionResult>
  ): Promise<boolean>;

  /**
   * Estimate cost for stage execution
   */
  estimateCost(
    context: PipelineExecutionContext,
    inputData: Record<string, unknown>
  ): Promise<number>;
}

/**
 * Pipeline Engine Interface
 */
export interface IPipelineEngine {
  /**
   * Execute a pipeline for an application
   */
  executePipeline(
    pipelineId: string,
    applicationId: string,
    context: Partial<PipelineExecutionContext>
  ): Promise<PipelineExecutionResult>;

  /**
   * Get pipeline configuration
   */
  getPipelineConfig(pipelineId: string): Promise<PipelineConfig>;

  /**
   * Register a stage processor
   */
  registerStage(stageType: StageType, processor: IPipelineStage): void;

  /**
   * Get pipeline execution status
   */
  getExecutionStatus(executionId: string): Promise<PipelineExecutionResult>;

  /**
   * Cancel a running pipeline execution
   */
  cancelExecution(executionId: string): Promise<void>;

  /**
   * Retry a failed pipeline execution
   */
  retryExecution(executionId: string): Promise<PipelineExecutionResult>;
}

/**
 * Built-in Stage Configurations
 */
export interface InputValidationConfig {
  requiredFields: string[];
  validationRules: ValidationRule[];
  sanitization: boolean;
}

export interface ValidationRule {
  field: string;
  type: 'string' | 'number' | 'email' | 'phone' | 'date' | 'custom';
  required: boolean;
  pattern?: string;
  min?: number;
  max?: number;
  customValidator?: string;
}

export interface DataEnrichmentConfig {
  sources: DataSource[];
  caching: CachingConfig;
  fallbackData?: Record<string, unknown>;
}

export interface DataSource {
  name: string;
  provider: string;
  enabled: boolean;
  priority: number;
  config: Record<string, unknown>;
  timeout: number;
}

export interface CachingConfig {
  enabled: boolean;
  ttlSeconds: number;
  keyStrategy: 'application' | 'tenant' | 'custom';
}

export interface AiAnalysisConfig {
  scenario: AiScenario;
  modelSelection: ModelSelectionCriteria;
  prompts: PromptConfig[];
  outputFormat: 'json' | 'text' | 'structured';
}

export interface ModelSelectionCriteria {
  preferredProviders?: string[];
  maxCost?: number;
  maxLatency?: number;
  requiredCapabilities?: string[];
}

export interface PromptConfig {
  name: string;
  template: string;
  variables: string[];
  required: boolean;
}

export interface RuleApplicationConfig {
  ruleEngine: 'javascript' | 'json-rules' | 'custom';
  rules: BusinessRule[];
  defaultAction: 'approve' | 'deny' | 'review';
}

export interface BusinessRule {
  id: string;
  name: string;
  description: string;
  condition: string;
  action: RuleAction;
  priority: number;
  enabled: boolean;
}

export interface RuleAction {
  type: 'approve' | 'deny' | 'review' | 'modify' | 'flag';
  parameters?: Record<string, unknown>;
  reason?: string;
}
