/**
 * Pipeline Service
 *
 * High-level service for managing decision pipelines, providing a clean interface
 * for pipeline operations, template management, and monitoring.
 */

import { PrismaClient } from '@prisma/client';
import { PipelineEngine } from '../engine/PipelineEngine';
import { AiModelManager } from '../../ai/services/AiModelManager';
import {
  PipelineConfig,
  PipelineStageConfig,
  PipelineGlobalConfig,
  PipelineExecutionContext,
  PipelineExecutionResult,
  StageType,
  IPipelineStage,
  ExecutionTrigger,
  ErrorHandlingStrategy,
} from '../types';

// Import stage implementations
import { InputValidationStage } from '../stages/InputValidationStage';
import { AiAnalysisStage } from '../stages/AiAnalysisStage';

/**
 * Pipeline Service Configuration
 */
export interface PipelineServiceConfig {
  enableMetrics: boolean;
  enableAuditLog: boolean;
  maxConcurrentExecutions: number;
  defaultTimeout: number;
}

/**
 * Pipeline Template for common scenarios
 */
export interface PipelineTemplate {
  name: string;
  description: string;
  applicableTypes: string[];
  stages: PipelineStageConfig[];
  config: PipelineGlobalConfig;
  tags: string[];
}

/**
 * Database pipeline record interface
 */
export interface DatabasePipelineRecord {
  id: string;
  name: string;
  description: string | null;
  version: string;
  stages: unknown;
  config: unknown;
  applicableTypes: string[];
  isActive: boolean;
  isDefault: boolean;
  tags: string[];
  tenantId: string;
}

/**
 * Pipeline listing options
 */
export interface PipelineListOptions {
  isActive?: boolean;
  applicableType?: string;
  tags?: string[];
  limit?: number;
  offset?: number;
}

/**
 * Pipeline metrics options
 */
export interface PipelineMetricsOptions {
  startDate?: Date;
  endDate?: Date;
  granularity?: string;
}

/**
 * Main Pipeline Service
 */
export class PipelineService {
  private prisma: PrismaClient;
  private engine: PipelineEngine;
  private aiModelManager: AiModelManager;

  constructor(
    prisma: PrismaClient,
    aiModelManager: AiModelManager,
    config: PipelineServiceConfig = {
      enableMetrics: true,
      enableAuditLog: true,
      maxConcurrentExecutions: 100,
      defaultTimeout: 300000,
    }
  ) {
    this.prisma = prisma;
    this.aiModelManager = aiModelManager;
    // Configuration stored locally but not as instance property

    // Initialize pipeline engine
    this.engine = new PipelineEngine(prisma, {
      maxConcurrentExecutions: config.maxConcurrentExecutions,
      defaultTimeout: config.defaultTimeout,
      enableMetrics: config.enableMetrics,
      enableAuditLog: config.enableAuditLog,
    });

    // Register built-in stages
    this.registerBuiltInStages();
  }

  /**
   * Initialize the pipeline service
   */
  public async initialize(): Promise<void> {
    console.log('Pipeline Service initialized successfully');
  }

  /**
   * Execute a pipeline for an application
   */
  public async executePipeline(
    pipelineId: string,
    applicationId: string,
    options: {
      tenantId?: string;
      userId?: string;
      trigger?: ExecutionTrigger;
      metadata?: Record<string, unknown>;
    } = {}
  ): Promise<PipelineExecutionResult> {
    const nodeEnv = process.env.NODE_ENV;
    const environment = (nodeEnv === 'development' || nodeEnv === 'staging' || nodeEnv === 'production' || nodeEnv === 'test') 
      ? nodeEnv 
      : 'production' as const;

    const context: Partial<PipelineExecutionContext> = {
      tenantId: options.tenantId,
      userId: options.userId,
      trigger: options.trigger || ExecutionTrigger.MANUAL,
      environment,
      metadata: options.metadata,
    };

    return this.engine.executePipeline(pipelineId, applicationId, context);
  }

  /**
   * Get pipeline execution status
   */
  public async getExecutionStatus(
    executionId: string
  ): Promise<PipelineExecutionResult> {
    return this.engine.getExecutionStatus(executionId);
  }

  /**
   * Cancel a running pipeline execution
   */
  public async cancelExecution(executionId: string): Promise<void> {
    return this.engine.cancelExecution(executionId);
  }

  /**
   * Retry a failed pipeline execution
   */
  public async retryExecution(
    executionId: string
  ): Promise<PipelineExecutionResult> {
    return this.engine.retryExecution(executionId);
  }

  /**
   * Create a new pipeline from configuration
   */
  public async createPipeline(
    tenantId: string,
    config: Omit<PipelineConfig, 'isActive' | 'isDefault'>,
    options: {
      isActive?: boolean;
      isDefault?: boolean;
      createdBy?: string;
    } = {}
  ): Promise<string> {
    // Validate pipeline configuration
    const fullConfig = { ...config, isActive: true, isDefault: false };
    await this.validatePipelineConfig(fullConfig);

    // Create pipeline in database
    const pipeline = await this.prisma.decisionPipeline.create({
      data: {
        tenantId,
        name: config.name,
        description: config.description,
        version: config.version,
        stages: JSON.stringify(config.stages),
        config: JSON.stringify(config.config),
        applicableTypes: config.applicableTypes,
        isActive: options.isActive ?? true,
        isDefault: options.isDefault ?? false,
        tags: config.tags,
        createdBy: options.createdBy,
      },
    });

    console.log(`Created pipeline: ${pipeline.name} (${pipeline.id})`);
    return pipeline.id;
  }

  /**
   * Update an existing pipeline
   */
  public async updatePipeline(
    pipelineId: string,
    updates: Partial<PipelineConfig>
  ): Promise<void> {
    const existingPipeline = await this.prisma.decisionPipeline.findUnique({
      where: { id: pipelineId },
    });

    if (!existingPipeline) {
      throw new Error(`Pipeline not found: ${pipelineId}`);
    }

    // If updating stages or config, validate the configuration
    if (updates.stages || updates.config) {
      const fullConfig = {
        ...existingPipeline,
        ...updates,
        stages: updates.stages || existingPipeline.stages,
        config: updates.config || existingPipeline.config,
      } as PipelineConfig;

      await this.validatePipelineConfig(fullConfig);
    }

    await this.prisma.decisionPipeline.update({
      where: { id: pipelineId },
      data: {
        ...(updates.name && { name: updates.name }),
        ...(updates.description && { description: updates.description }),
        ...(updates.version && { version: updates.version }),
        ...(updates.stages && { stages: JSON.stringify(updates.stages) }),
        ...(updates.config && { config: JSON.stringify(updates.config) }),
        ...(updates.applicableTypes && {
          applicableTypes: updates.applicableTypes,
        }),
        ...(updates.tags && { tags: updates.tags }),
        updatedAt: new Date(),
      },
    });

    console.log(`Updated pipeline: ${pipelineId}`);
  }

  /**
   * Get pipeline configuration
   */
  public async getPipelineConfig(pipelineId: string): Promise<PipelineConfig> {
    return this.engine.getPipelineConfig(pipelineId);
  }

  /**
   * List pipelines for a tenant
   */
  public async listPipelines(
    tenantId: string,
    options: PipelineListOptions = {}
  ): Promise<{ pipelines: DatabasePipelineRecord[]; total: number }> {
    const where: Record<string, unknown> = { tenantId };

    if (options.isActive !== undefined) {
      where.isActive = options.isActive;
    }

    if (options.applicableType) {
      where.applicableTypes = {
        has: options.applicableType,
      };
    }

    if (options.tags && options.tags.length > 0) {
      where.tags = {
        hasEvery: options.tags,
      };
    }

    const [pipelinesResult, total] = await Promise.all([
      this.prisma.decisionPipeline.findMany({
        where,
        orderBy: [{ isDefault: 'desc' }, { createdAt: 'desc' }],
        take: options.limit || 50,
        skip: options.offset || 0,
        include: {
          metrics: {
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
          _count: {
            select: { executions: true },
          },
        },
      }),
      this.prisma.decisionPipeline.count({ where }),
    ]);

    const pipelines = pipelinesResult as DatabasePipelineRecord[];
    return { pipelines, total };
  }

  /**
   * Create pipeline from template
   */
  public async createPipelineFromTemplate(
    tenantId: string,
    templateName: string,
    customization: {
      name?: string;
      description?: string;
      stageOverrides?: Record<string, unknown>;
      configOverrides?: Record<string, unknown>;
    } = {}
  ): Promise<string> {
    const template = this.getBuiltInTemplate(templateName);
    if (!template) {
      throw new Error(`Template not found: ${templateName}`);
    }

    // Apply customizations
    const config: PipelineConfig = {
      name:
        customization.name ||
        `${template.name} - ${new Date().toISOString().split('T')[0]}`,
      description: customization.description || template.description,
      version: '1.0',
      stages: this.applyStageOverrides(
        template.stages,
        customization.stageOverrides || {}
      ),
      config: { ...template.config, ...customization.configOverrides },
      applicableTypes: template.applicableTypes,
      isActive: true,
      isDefault: false,
      tags: [...template.tags, 'template', templateName],
    };

    return this.createPipeline(tenantId, config);
  }

  /**
   * Get pipeline execution metrics
   */
  public async getPipelineMetrics(
    pipelineId: string,
    options: PipelineMetricsOptions = {}
  ): Promise<Record<string, unknown>[]> {
    const where: Record<string, unknown> = { pipelineId };

    if (options.startDate || options.endDate) {
      const periodStart: Record<string, unknown> = {};
      if (options.startDate) periodStart.gte = options.startDate;
      if (options.endDate) periodStart.lte = options.endDate;
      where.periodStart = periodStart;
    }

    if (options.granularity) {
      where.granularity = options.granularity;
    }

    const metricsResult = await this.prisma.pipelineMetrics.findMany({
      where,
      orderBy: { periodStart: 'desc' },
      take: 100,
    });

    return metricsResult as Record<string, unknown>[];
  }

  /**
   * Register built-in stage processors
   */
  private registerBuiltInStages(): void {
    // Register input validation stage
    this.engine.registerStage(
      StageType.INPUT_VALIDATION,
      new InputValidationStage()
    );

    // Register AI analysis stage
    this.engine.registerStage(
      StageType.AI_ANALYSIS,
      new AiAnalysisStage(this.aiModelManager)
    );

    console.log('Registered built-in pipeline stages');
  }

  /**
   * Validate pipeline configuration
   */
  private async validatePipelineConfig(config: PipelineConfig): Promise<void> {
    // Basic validation
    if (!config.name || !config.version) {
      throw new Error('Pipeline name and version are required');
    }

    if (!config.stages || config.stages.length === 0) {
      throw new Error('Pipeline must have at least one stage');
    }

    // Validate stage dependencies
    const stageNames = config.stages.map((stage) => stage.name);
    for (const stage of config.stages) {
      for (const dependency of stage.dependencies || []) {
        if (!stageNames.includes(dependency)) {
          throw new Error(
            `Stage ${stage.name} depends on non-existent stage: ${dependency}`
          );
        }
      }
    }

    // Check for circular dependencies
    this.checkCircularDependencies(config.stages);

    // Validate individual stage configurations
    for (const stage of config.stages) {
      const processor = this.getStageProcessor(stage.type);
      if (processor) {
        const isValid = await processor.validateConfig(stage.config || {});
        if (!isValid) {
          throw new Error(`Invalid configuration for stage: ${stage.name}`);
        }
      }
    }
  }

  /**
   * Check for circular dependencies in stages
   */
  private checkCircularDependencies(stages: PipelineStageConfig[]): void {
    const graph = new Map<string, string[]>();
    for (const stage of stages) {
      graph.set(stage.name, stage.dependencies || []);
    }

    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (node: string): boolean => {
      if (recursionStack.has(node)) {
        return true;
      }
      if (visited.has(node)) {
        return false;
      }

      visited.add(node);
      recursionStack.add(node);

      const dependencies = graph.get(node) || [];
      for (const dependency of dependencies) {
        if (hasCycle(dependency)) {
          return true;
        }
      }

      recursionStack.delete(node);
      return false;
    };

    for (const stage of stages) {
      if (hasCycle(stage.name)) {
        throw new Error('Circular dependency detected in pipeline stages');
      }
    }
  }

  /**
   * Get stage processor by type
   */
  private getStageProcessor(stageType: StageType): IPipelineStage | undefined {
    // Note: This is a simplified implementation for validation
    // In a real implementation, this would access the engine's registry properly
    try {
      // Create a temporary stage instance for validation
      switch (stageType) {
        case StageType.INPUT_VALIDATION:
          return new InputValidationStage();
        case StageType.AI_ANALYSIS:
          return new AiAnalysisStage(this.aiModelManager);
        default:
          return undefined;
      }
    } catch {
      return undefined;
    }
  }

  /**
   * Get built-in pipeline template
   */
  private getBuiltInTemplate(templateName: string): PipelineTemplate | null {
    const templates: Record<string, PipelineTemplate> = {
      basic_underwriting: {
        name: 'Basic Underwriting Pipeline',
        description:
          'Standard underwriting pipeline with validation, AI analysis, and decision',
        applicableTypes: ['loan', 'credit'],
        stages: [
          {
            name: 'input_validation',
            type: StageType.INPUT_VALIDATION,
            dependencies: [],
            config: {
              requiredFields: [
                'businessName',
                'businessEmail',
                'requestedAmount',
                'monthlyRevenue',
              ],
              validationRules: [
                {
                  field: 'businessEmail',
                  type: 'email',
                  required: true,
                },
                {
                  field: 'requestedAmount',
                  type: 'number',
                  required: true,
                  min: 1000,
                  max: 1000000,
                },
                {
                  field: 'monthlyRevenue',
                  type: 'number',
                  required: true,
                  min: 0,
                },
              ],
              sanitization: true,
            },
            enabled: true,
            timeout: 30000,
          },
          {
            name: 'ai_analysis',
            type: StageType.AI_ANALYSIS,
            dependencies: ['input_validation'],
            config: {
              scenario: 'underwriting_analysis',
              prompts: [
                {
                  name: 'system_prompt',
                  template:
                    'You are an expert underwriter. Analyze the following loan application and provide a detailed risk assessment.',
                  variables: [],
                  required: true,
                },
                {
                  name: 'analysis_prompt',
                  template: `Analyze this loan application:
Business Name: \\{\\{businessName\\}\\}
Business Email: \\{\\{businessEmail\\}\\}
Requested Amount: $\\{\\{requestedAmount\\}\\}
Monthly Revenue: $\\{\\{monthlyRevenue\\}\\}
Business Type: \\{\\{businessType\\}\\}

Provide a structured analysis including:
1. Risk Level (Low/Medium/High)
2. Risk Score (0-100)
3. Key Risk Factors
4. Recommendation (Approve/Deny/Review)
5. Reasoning`,
                  variables: [
                    'businessName',
                    'businessEmail',
                    'requestedAmount',
                    'monthlyRevenue',
                    'businessType',
                  ],
                  required: true,
                },
              ],
              outputFormat: 'structured',
              modelSelection: {
                scenario: 'underwriting_analysis',
                maxCost: 0.1,
                maxLatency: 10000,
              },
            },
            enabled: true,
            timeout: 60000,
          },
        ],
        config: {
          timeout: 300000,
          maxRetries: 3,
          parallelExecution: false,
          failFast: true,
          errorHandling: ErrorHandlingStrategy.FAIL_FAST,
          monitoring: {
            enabled: true,
            stageMetrics: true,
            performanceTracking: true,
            costTracking: true,
            errorTracking: true,
          },
        },
        tags: ['underwriting', 'basic', 'ai-powered'],
      },
    };

    return templates[templateName] || null;
  }

  /**
   * Apply stage overrides to template stages
   */
  private applyStageOverrides(
    templateStages: PipelineStageConfig[],
    overrides: Record<string, unknown>
  ): PipelineStageConfig[] {
    return templateStages.map((stage) => {
      const override = overrides[stage.name] as Record<string, unknown> | undefined;
      if (override && typeof override === 'object') {
        const overrideConfig = override.config as Record<string, unknown> | undefined;
        return {
          ...stage,
          ...override,
          config: { ...stage.config, ...(overrideConfig || {}) },
        } as PipelineStageConfig;
      }
      return stage;
    });
  }
}
