/**
 * Pipeline Controller
 *
 * REST API endpoints for managing decision pipelines, executing pipelines,
 * and monitoring pipeline performance.
 */

import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import Jo<PERSON> from 'joi';
import { PipelineService } from '../services/PipelineService';
import { AiModelManager } from '../../ai/services/AiModelManager';
import {
  StageType,
  ExecutionTrigger,
  PipelineConfig,
  ErrorHandlingStrategy,
  MonitoringConfig,
  CostLimits,
} from '../types';

/**
 * TypeScript interfaces for validation results
 */
interface ValidatedCreatePipelineData {
  name: string;
  description?: string;
  version: string;
  stages: Array<{
    name: string;
    type: StageType;
    description?: string;
    dependencies: string[];
    config: Record<string, unknown>;
    timeout?: number;
    enabled: boolean;
    parallel: boolean;
  }>;
  config: {
    timeout: number;
    maxRetries: number;
    parallelExecution: boolean;
    failFast: boolean;
    errorHandling: ErrorHandlingStrategy;
    monitoring: MonitoringConfig;
    costLimits?: CostLimits;
  };
  applicableTypes: string[];
  tags: string[];
  isActive: boolean;
  isDefault: boolean;
}

interface ValidatedExecutePipelineData {
  pipelineId: string;
  trigger?: ExecutionTrigger;
  metadata?: Record<string, unknown>;
}

interface ValidatedCreateFromTemplateData {
  templateName: string;
  name?: string;
  description?: string;
  stageOverrides?: Record<string, unknown>;
  configOverrides?: Record<string, unknown>;
}

interface PipelineMetricsOptions {
  startDate?: Date;
  endDate?: Date;
  granularity?: string;
}

/**
 * Validation schemas
 */
const createPipelineSchema = Joi.object({
  name: Joi.string().min(1).max(200).required(),
  description: Joi.string().max(1000).optional(),
  version: Joi.string().max(50).optional().default('1.0'),
  stages: Joi.array()
    .items(
      Joi.object({
        name: Joi.string().required(),
        type: Joi.string()
          .valid(...Object.values(StageType))
          .required(),
        description: Joi.string().optional(),
        dependencies: Joi.array().items(Joi.string()).optional().default([]),
        config: Joi.object().required(),
        timeout: Joi.number().min(1000).max(600000).optional(),
        enabled: Joi.boolean().optional().default(true),
        parallel: Joi.boolean().optional().default(false),
      })
    )
    .min(1)
    .required(),
  config: Joi.object({
    timeout: Joi.number().min(1000).max(3600000).optional().default(300000),
    maxRetries: Joi.number().min(0).max(10).optional().default(3),
    parallelExecution: Joi.boolean().optional().default(false),
    failFast: Joi.boolean().optional().default(true),
    errorHandling: Joi.string()
      .valid(...Object.values(ErrorHandlingStrategy))
      .required(),
    monitoring: Joi.object().optional(),
    costLimits: Joi.object().optional(),
  }).required(),
  applicableTypes: Joi.array().items(Joi.string()).min(1).required(),
  tags: Joi.array().items(Joi.string().max(50)).optional().default([]),
  isActive: Joi.boolean().optional().default(true),
  isDefault: Joi.boolean().optional().default(false),
});

const executePipelineSchema = Joi.object({
  pipelineId: Joi.string().required(),
  trigger: Joi.string()
    .valid(...Object.values(ExecutionTrigger))
    .optional(),
  metadata: Joi.object().optional(),
});

const createFromTemplateSchema = Joi.object({
  templateName: Joi.string().required(),
  name: Joi.string().max(200).optional(),
  description: Joi.string().max(1000).optional(),
  stageOverrides: Joi.object().optional(),
  configOverrides: Joi.object().optional(),
});

/**
 * Pipeline Controller
 */
export class PipelineController {
  private prisma: PrismaClient;
  private pipelineService: PipelineService;

  constructor(prisma: PrismaClient, aiModelManager: AiModelManager) {
    this.prisma = prisma;
    this.pipelineService = new PipelineService(prisma, aiModelManager);
  }

  /**
   * Initialize the pipeline service
   */
  public async initialize(): Promise<void> {
    await this.pipelineService.initialize();
  }

  /**
   * Create a new pipeline
   */
  public createPipeline = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      const validation = createPipelineSchema.validate(req.body);
      const error = validation.error;
      const value = validation.value as ValidatedCreatePipelineData;
      
      if (error) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: error.details,
        });
        return;
      }

      const tenantId = req.user?.tenantId;
      if (!tenantId) {
        res.status(401).json({
          success: false,
          error: 'Tenant ID required',
        });
        return;
      }

      const pipelineConfig: Omit<PipelineConfig, 'isActive' | 'isDefault'> = {
        name: value.name,
        description: value.description,
        version: value.version,
        stages: value.stages,
        config: value.config,
        applicableTypes: value.applicableTypes,
        tags: value.tags,
      };

      const pipelineId = await this.pipelineService.createPipeline(
        tenantId,
        pipelineConfig,
        {
          isActive: value.isActive,
          isDefault: value.isDefault,
          createdBy: req.user?.userId,
        }
      );

      const createdPipeline =
        await this.pipelineService.getPipelineConfig(pipelineId);

      res.status(201).json({
        success: true,
        data: {
          id: pipelineId,
          ...createdPipeline,
        },
      });
    } catch (error) {
      console.error('Failed to create pipeline:', error);
      res.status(500).json({
        success: false,
        error:
          error instanceof Error ? error.message : 'Failed to create pipeline',
      });
    }
  };

  /**
   * Get all pipelines for tenant
   */
  public getPipelines = async (req: Request, res: Response): Promise<void> => {
    try {
      const tenantId = req.user?.tenantId;
      if (!tenantId) {
        res.status(401).json({
          success: false,
          error: 'Tenant ID required',
        });
        return;
      }

      const {
        isActive,
        applicableType,
        tags,
        page = 1,
        limit = 20,
      } = req.query;

      const offset = (Number(page) - 1) * Number(limit);

      const result = await this.pipelineService.listPipelines(tenantId, {
        isActive:
          isActive === 'true' ? true : isActive === 'false' ? false : undefined,
        applicableType: applicableType as string,
        tags: tags ? (tags as string).split(',') : undefined,
        limit: Number(limit),
        offset,
      });

      res.json({
        success: true,
        data: {
          pipelines: result.pipelines,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total: result.total,
            pages: Math.ceil(result.total / Number(limit)),
          },
        },
      });
    } catch (error) {
      console.error('Failed to get pipelines:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve pipelines',
      });
    }
  };

  /**
   * Get a specific pipeline
   */
  public getPipeline = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      if (!id) {
        res.status(400).json({ success: false, error: 'Pipeline ID is required' });
        return;
      }

      const pipeline = await this.pipelineService.getPipelineConfig(id);

      res.json({
        success: true,
        data: pipeline,
      });
    } catch (error) {
      console.error('Failed to get pipeline:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({
          success: false,
          error: 'Pipeline not found',
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to retrieve pipeline',
        });
      }
    }
  };

  /**
   * Update a pipeline
   */
  public updatePipeline = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      const { id } = req.params;
      if (!id) {
        res.status(400).json({ success: false, error: 'Pipeline ID is required' });
        return;
      }

      // Use same validation schema but make fields optional for updates
      const updateSchema = createPipelineSchema.fork(
        ['name', 'stages', 'config', 'applicableTypes'],
        (schema) => schema.optional()
      );

      const validation = updateSchema.validate(req.body);
      const error = validation.error;
      const value = validation.value as Partial<ValidatedCreatePipelineData>;
      
      if (error) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: error.details,
        });
        return;
      }

      await this.pipelineService.updatePipeline(id, value);

      const updatedPipeline = await this.pipelineService.getPipelineConfig(id);

      res.json({
        success: true,
        data: updatedPipeline,
      });
    } catch (error) {
      console.error('Failed to update pipeline:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({
          success: false,
          error: 'Pipeline not found',
        });
      } else {
        res.status(500).json({
          success: false,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to update pipeline',
        });
      }
    }
  };

  /**
   * Execute a pipeline for an application
   */
  public executePipeline = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      const { applicationId } = req.params;
      const validation = executePipelineSchema.validate(req.body);
      const error = validation.error;
      const value = validation.value as ValidatedExecutePipelineData;

      if (error) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: error.details,
        });
        return;
      }

      if (!applicationId) {
        res.status(400).json({ success: false, error: 'Application ID required' });
        return;
      }

      const tenantId = req.user?.tenantId;
      const userId = req.user?.userId;

      if (!tenantId || !userId) {
        res.status(401).json({ success: false, error: 'User context required' });
        return;
      }

      const result = await this.pipelineService.executePipeline(
        value.pipelineId,
        applicationId,
        {
          tenantId,
          userId,
          trigger: value.trigger,
          metadata: value.metadata,
        }
      );

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      console.error('Failed to execute pipeline:', error);
      res.status(500).json({
        success: false,
        error:
          error instanceof Error ? error.message : 'Failed to execute pipeline',
      });
    }
  };

  /**
   * Get pipeline execution status
   */
  public getExecutionStatus = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      const { executionId } = req.params;
      if (!executionId) {
        res.status(400).json({ success: false, error: 'Execution ID is required' });
        return;
      }

      const result = await this.pipelineService.getExecutionStatus(executionId);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      console.error('Failed to get execution status:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({
          success: false,
          error: 'Execution not found',
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to retrieve execution status',
        });
      }
    }
  };

  /**
   * Cancel a pipeline execution
   */
  public cancelExecution = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      const { executionId } = req.params;

      if (!executionId) {
        res.status(400).json({ success: false, error: 'Execution ID required' });
        return;
      }

      await this.pipelineService.cancelExecution(executionId);

      res.json({
        success: true,
        message: 'Pipeline execution cancelled successfully',
      });
    } catch (error) {
      console.error('Failed to cancel execution:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({
          success: false,
          error: 'Execution not found',
        });
      } else {
        res.status(500).json({
          success: false,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to cancel execution',
        });
      }
    }
  };

  /**
   * Retry a failed pipeline execution
   */
  public retryExecution = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      const { executionId } = req.params;

      if (!executionId) {
        res.status(400).json({ success: false, error: 'Execution ID required' });
        return;
      }

      const result = await this.pipelineService.retryExecution(executionId);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      console.error('Failed to retry execution:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({
          success: false,
          error: 'Execution not found',
        });
      } else {
        res.status(500).json({
          success: false,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to retry execution',
        });
      }
    }
  };

  /**
   * Create pipeline from template
   */
  public createFromTemplate = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      const validation = createFromTemplateSchema.validate(req.body);
      const error = validation.error;
      const value = validation.value as ValidatedCreateFromTemplateData;
      
      if (error) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: error.details,
        });
        return;
      }

      const tenantId = req.user?.tenantId;
      if (!tenantId) {
        res.status(401).json({
          success: false,
          error: 'Tenant ID required',
        });
        return;
      }

      const pipelineId = await this.pipelineService.createPipelineFromTemplate(
        tenantId,
        value.templateName,
        {
          name: value.name,
          description: value.description,
          stageOverrides: value.stageOverrides,
          configOverrides: value.configOverrides,
        }
      );

      const createdPipeline =
        await this.pipelineService.getPipelineConfig(pipelineId);

      res.status(201).json({
        success: true,
        data: {
          id: pipelineId,
          ...createdPipeline,
        },
      });
    } catch (error) {
      console.error('Failed to create pipeline from template:', error);
      res.status(500).json({
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to create pipeline from template',
      });
    }
  };

  /**
   * Get pipeline metrics
   */
  public getPipelineMetrics = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const { startDate, endDate, granularity } = req.query;

      if (!id) {
        res.status(400).json({ success: false, error: 'Pipeline ID required' });
        return;
      }

      const options: PipelineMetricsOptions = {};
      if (startDate) options.startDate = new Date(startDate as string);
      if (endDate) options.endDate = new Date(endDate as string);
      if (granularity) options.granularity = granularity as string;

      const metrics = await this.pipelineService.getPipelineMetrics(
        id,
        options
      );

      res.json({
        success: true,
        data: metrics,
      });
    } catch (error) {
      console.error('Failed to get pipeline metrics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve pipeline metrics',
      });
    }
  };

  /**
   * Get available pipeline templates
   */
  public getTemplates = async (_req: Request, res: Response): Promise<void> => {
    try {
      // Return list of available templates
      const templates = [
        {
          name: 'basic_underwriting',
          displayName: 'Basic Underwriting Pipeline',
          description:
            'Standard underwriting pipeline with validation, AI analysis, and decision',
          applicableTypes: ['loan', 'credit'],
          tags: ['underwriting', 'basic', 'ai-powered'],
          stages: ['input_validation', 'ai_analysis'],
          estimatedDuration: '2-5 minutes',
          complexity: 'basic',
        },
      ];

      res.json({
        success: true,
        data: templates,
      });
    } catch (error) {
      console.error('Failed to get templates:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve templates',
      });
    }
  };

  /**
   * Get pipeline execution history for an application
   */
  public getApplicationExecutions = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      const { applicationId } = req.params;
      const { page = 1, limit = 10 } = req.query;

      const skip = (Number(page) - 1) * Number(limit);

      const results = await Promise.all([
        this.prisma.pipelineExecution.findMany({
          where: { applicationId },
          include: {
            pipeline: {
              select: { name: true, version: true },
            },
            stageExecutions: {
              select: {
                stageName: true,
                status: true,
                duration: true,
                cost: true,
              },
            },
          },
          orderBy: { startedAt: 'desc' },
          take: Number(limit),
          skip,
        }),
        this.prisma.pipelineExecution.count({
          where: { applicationId },
        }),
      ]);
      
      const executions = results[0];
      const total = results[1];

      res.json({
        success: true,
        data: {
          executions,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total,
            pages: Math.ceil(total / Number(limit)),
          },
        },
      });
    } catch (error) {
      console.error('Failed to get application executions:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve application executions',
      });
    }
  };
}

// Express Request type extension defined in src/types/express.d.ts