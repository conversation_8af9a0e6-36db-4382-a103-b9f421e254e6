/**
 * Pipeline Routes
 *
 * Express routes for decision pipeline management and execution.
 */

import { Router, Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import { PipelineController } from '../controllers/PipelineController';
import { AiModelManager } from '../../ai/services/AiModelManager';
import { JWTPayload } from '../../utils/jwt';

// Extended Request type for user information
interface AuthenticatedRequest extends Request {
  user?: JWTPayload;
}

/**
 * Create pipeline routes
 */
export function createPipelineRoutes(
  prisma: PrismaClient,
  aiModelManager: AiModelManager
): Router {
  const router = Router();
  const controller = new PipelineController(prisma, aiModelManager);

  // Initialize controller
  controller.initialize().catch(console.error);

  // Pipeline Management Routes
  router.post('/', controller.createPipeline);
  router.get('/', controller.getPipelines);
  router.get('/templates', controller.getTemplates);
  router.post('/from-template', controller.createFromTemplate);
  router.get('/:id', controller.getPipeline);
  router.put('/:id', controller.updatePipeline);
  router.get('/:id/metrics', controller.getPipelineMetrics);

  // Pipeline Execution Routes
  router.post(
    '/applications/:applicationId/execute',
    controller.executePipeline
  );
  router.get(
    '/applications/:applicationId/executions',
    controller.getApplicationExecutions
  );
  router.get('/executions/:executionId', controller.getExecutionStatus);
  router.post('/executions/:executionId/cancel', controller.cancelExecution);
  router.post('/executions/:executionId/retry', controller.retryExecution);

  return router;
}

/**
 * Authentication middleware placeholder
 */
export const authMiddleware = (req: AuthenticatedRequest, _res: Response, next: NextFunction): void => {
  // Placeholder - extract user from JWT token
  req.user = {
    userId: 'user_123',
    email: '<EMAIL>',
    role: 'admin',
    tenantId: 'tenant_456',
    sessionId: 'session_789',
  };
  next();
};

/**
 * Admin-only middleware
 */
export const adminMiddleware = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  if (req.user?.role !== 'admin') {
    res.status(403).json({
      success: false,
      error: 'Admin access required',
    });
    return;
  }
  next();
};
