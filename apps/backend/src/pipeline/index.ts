/**
 * Pipeline Module - Main Entry Point
 *
 * This module provides the main entry point for the decision pipeline framework,
 * including pipeline management, execution, and monitoring capabilities.
 */

// Core types and interfaces
export * from './types';

// Engine and services
export { PipelineEngine } from './engine/PipelineEngine';
export { PipelineService } from './services/PipelineService';

// Controllers and routes
export { PipelineController } from './controllers/PipelineController';
export {
  createPipelineRoutes,
  authMiddleware,
  adminMiddleware,
} from './routes/pipelineRoutes';

// Base stage implementation
export { BasePipelineStage } from './stages/BasePipelineStage';

// Built-in stage implementations
export { InputValidationStage } from './stages/InputValidationStage';
export { AiAnalysisStage } from './stages/AiAnalysisStage';

// Main pipeline service class
import { PrismaClient } from '@prisma/client';
import { PipelineService, PipelineListOptions, DatabasePipelineRecord } from './services/PipelineService';
import { AiModelManager } from '../ai/services/AiModelManager';
import { PipelineExecutionResult, ExecutionTrigger } from './types';

/**
 * Main Pipeline Module Class
 *
 * Provides a high-level interface for pipeline operations across the platform.
 */
export class PipelineModule {
  private pipelineService: PipelineService;

  constructor(prisma: PrismaClient, aiModelManager: AiModelManager) {
    this.pipelineService = new PipelineService(prisma, aiModelManager);
  }

  /**
   * Initialize the pipeline module
   */
  public async initialize(): Promise<void> {
    await this.pipelineService.initialize();
    console.log('Pipeline Module initialized successfully');
  }

  /**
   * Get the pipeline service instance
   */
  public getPipelineService(): PipelineService {
    return this.pipelineService;
  }

  /**
   * Execute a pipeline for an application
   */
  public async executePipeline(
    pipelineId: string,
    applicationId: string,
    options: {
      tenantId?: string;
      userId?: string;
      trigger?: ExecutionTrigger;
      metadata?: Record<string, unknown>;
    } = {}
  ): Promise<PipelineExecutionResult> {
    return this.pipelineService.executePipeline(pipelineId, applicationId, {
      tenantId: options.tenantId,
      userId: options.userId,
      trigger: options.trigger,
      metadata: options.metadata,
    });
  }

  /**
   * Create a basic underwriting pipeline for a tenant
   */
  public async createBasicUnderwritingPipeline(
    tenantId: string,
    options: {
      name?: string;
      customization?: Record<string, unknown>;
    } = {}
  ): Promise<string> {
    const customization = options.customization as Record<string, Record<string, unknown>> | undefined;
    return this.pipelineService.createPipelineFromTemplate(
      tenantId,
      'basic_underwriting',
      {
        name: options.name || 'Basic Underwriting Pipeline',
        stageOverrides: customization?.stages || {},
        configOverrides: customization?.config || {},
      }
    );
  }

  /**
   * Get pipeline execution status
   */
  public async getExecutionStatus(executionId: string): Promise<PipelineExecutionResult> {
    return this.pipelineService.getExecutionStatus(executionId);
  }

  /**
   * List pipelines for a tenant
   */
  public async listPipelines(
    tenantId: string, 
    options: PipelineListOptions = {}
  ): Promise<{ pipelines: DatabasePipelineRecord[]; total: number }> {
    return this.pipelineService.listPipelines(tenantId, options);
  }
}

/**
 * Default pipeline module instance (singleton)
 */
let defaultPipelineModule: PipelineModule | null = null;

/**
 * Get or create the default pipeline module instance
 */
export function getPipelineModule(
  prisma?: PrismaClient,
  aiModelManager?: AiModelManager
): PipelineModule {
  if (!defaultPipelineModule) {
    if (!prisma || !aiModelManager) {
      throw new Error(
        'Prisma client and AI model manager required to initialize pipeline module'
      );
    }
    defaultPipelineModule = new PipelineModule(prisma, aiModelManager);
  }
  return defaultPipelineModule;
}

/**
 * Initialize the default pipeline module
 */
export async function initializePipelineModule(
  prisma: PrismaClient,
  aiModelManager: AiModelManager
): Promise<PipelineModule> {
  const module = getPipelineModule(prisma, aiModelManager);
  await module.initialize();
  return module;
}
