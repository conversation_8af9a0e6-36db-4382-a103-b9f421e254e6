/**
 * Input Validation Stage
 *
 * Validates and sanitizes application data before processing through the pipeline.
 * Ensures data quality and consistency for downstream stages.
 */

import { BasePipelineStage } from './BasePipelineStage';
import {
  StageType,
  PipelineExecutionContext,
  InputValidationConfig,
  ValidationRule,
} from '../types';

export class InputValidationStage extends BasePipelineStage {
  constructor() {
    super('input_validation', StageType.INPUT_VALIDATION, []);
  }

  protected async executeStage(
    _context: PipelineExecutionContext,
    inputData: Record<string, unknown>,
    config: Record<string, unknown>
  ): Promise<Record<string, unknown>> {
    const validationConfig = this.validateAndCastConfig(config);

    // Validate required fields
    this.validateRequiredFields(
      inputData,
      validationConfig.requiredFields || []
    );

    // Apply validation rules
    const validationErrors = await this.applyValidationRules(
      inputData,
      validationConfig.validationRules || []
    );

    if (validationErrors.length > 0) {
      throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
    }

    // Sanitize data if enabled
    const sanitizedData = validationConfig.sanitization
      ? this.sanitizeData(inputData)
      : inputData;

    return {
      validatedData: sanitizedData,
      validationPassed: true,
      validationTimestamp: new Date(),
      appliedRules:
        validationConfig.validationRules?.map((rule) => rule.field) || [],
    };
  }

  private validateRequiredFields(
    data: Record<string, unknown>,
    requiredFields: string[]
  ): void {
    const missingFields = requiredFields.filter((field) => {
      const value = this.getNestedValue(data, field);
      return value === undefined || value === null || value === '';
    });

    if (missingFields.length > 0) {
      throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
    }
  }

  private async applyValidationRules(
    data: Record<string, unknown>,
    rules: ValidationRule[]
  ): Promise<string[]> {
    const errors: string[] = [];

    for (const rule of rules) {
      try {
        const value = this.getNestedValue(data, rule.field);

        // Check if field is required
        if (
          rule.required &&
          (value === undefined || value === null || value === '')
        ) {
          errors.push(`Field ${rule.field} is required`);
          continue;
        }

        // Skip validation if field is empty and not required
        if (
          !rule.required &&
          (value === undefined || value === null || value === '')
        ) {
          continue;
        }

        // Apply type-specific validation
        const typeError = this.validateType(rule.field, value, rule);
        if (typeError) {
          errors.push(typeError);
        }
      } catch (error) {
        errors.push(
          `Validation error for field ${rule.field}: ${(error as Error).message}`
        );
      }
    }

    return errors;
  }

  private validateType(
    field: string,
    value: unknown,
    rule: ValidationRule
  ): string | null {
    switch (rule.type) {
      case 'string':
        if (typeof value !== 'string') {
          return `Field ${field} must be a string`;
        }
        if (rule.pattern && !new RegExp(rule.pattern).test(value)) {
          return `Field ${field} does not match required pattern`;
        }
        if (rule.min !== undefined && value.length < rule.min) {
          return `Field ${field} must be at least ${rule.min} characters`;
        }
        if (rule.max !== undefined && value.length > rule.max) {
          return `Field ${field} must not exceed ${rule.max} characters`;
        }
        break;

      case 'number': {
        const numValue = Number(value);
        if (isNaN(numValue)) {
          return `Field ${field} must be a valid number`;
        }
        if (rule.min !== undefined && numValue < rule.min) {
          return `Field ${field} must be at least ${rule.min}`;
        }
        if (rule.max !== undefined && numValue > rule.max) {
          return `Field ${field} must not exceed ${rule.max}`;
        }
        break;
      }

      case 'email': {
        if (typeof value !== 'string') {
          return `Field ${field} must be a string`;
        }
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailPattern.test(value)) {
          return `Field ${field} must be a valid email address`;
        }
        break;
      }

      case 'phone': {
        if (typeof value !== 'string') {
          return `Field ${field} must be a string`;
        }
        const phonePattern = /^\+?[\d\s-()]+$/;
        if (!phonePattern.test(value)) {
          return `Field ${field} must be a valid phone number`;
        }
        break;
      }

      case 'date': {
        const dateValue = new Date(value as string);
        if (isNaN(dateValue.getTime())) {
          return `Field ${field} must be a valid date`;
        }
        break;
      }

      case 'custom':
        if (rule.customValidator) {
          try {
            // Evaluate custom validator (simplified - in production, use safer evaluation)
            const isValid = this.evaluateCustomValidator(rule.customValidator, value);
            if (!isValid) {
              return `Field ${field} failed custom validation`;
            }
          } catch (error) {
            return `Field ${field} custom validation error: ${(error as Error).message}`;
          }
        }
        break;
    }

    return null;
  }

  private sanitizeData(data: Record<string, unknown>): Record<string, unknown> {
    const sanitized: Record<string, unknown> = {};

    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'string') {
        // Basic string sanitization
        sanitized[key] = value
          .trim()
          .replace(/[<>"'&]/g, '') // Remove potentially dangerous characters
          .substring(0, 10000); // Limit length
      } else if (typeof value === 'object' && value !== null) {
        // Recursively sanitize objects
        sanitized[key] = this.sanitizeData(value as Record<string, unknown>);
      } else {
        // Keep other types as-is
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  private getNestedValue(obj: Record<string, unknown>, path: string): unknown {
    return path.split('.').reduce((current: unknown, key) => {
      return current && typeof current === 'object' && current !== null ? 
        (current as Record<string, unknown>)[key] : undefined;
    }, obj);
  }

  public async validateConfig(
    config: Record<string, unknown>
  ): Promise<boolean> {
    try {
      this.validateAndCastConfig(config);
      return true;
    } catch {
      return false;
    }
  }

  private validateAndCastConfig(config: Record<string, unknown>): InputValidationConfig {
    if (typeof config !== 'object' || config === null) {
      throw new Error('Invalid validation configuration: must be an object');
    }

    // Allow missing or empty configuration for test scenarios
    const requiredFields = config.requiredFields ?? [];
    if (!Array.isArray(requiredFields)) {
      throw new Error('Invalid validation configuration: requiredFields must be an array');
    }

    const validationRules = config.validationRules ?? [];
    if (!Array.isArray(validationRules)) {
      throw new Error('Invalid validation configuration: validationRules must be an array');
    }

    return {
      requiredFields: requiredFields as string[],
      validationRules: validationRules as ValidationRule[],
      sanitization: Boolean(config.sanitization),
    };
  }

  private evaluateCustomValidator(validator: string, value: unknown): boolean {
    // Simple validator evaluation - in production, use a proper sandbox
    const validatorFunction = new Function('value', `return ${validator}`) as (value: unknown) => boolean;
    return validatorFunction(value);
  }

  protected async calculateCost(
    _context: PipelineExecutionContext,
    inputData: Record<string, unknown>,
    _outputData: Record<string, unknown>
  ): Promise<number> {
    // Input validation is typically very low cost
    const dataSize = JSON.stringify(inputData).length;
    const baseCost = 0.001; // $0.001 base cost
    const sizeFactor = dataSize / 1000; // Additional cost per KB

    return baseCost + sizeFactor * 0.0001;
  }

  public async estimateCost(
    _context: PipelineExecutionContext,
    _inputData: Record<string, unknown>
  ): Promise<number> {
    // Input validation is very low cost
    return 0.001;
  }
}
