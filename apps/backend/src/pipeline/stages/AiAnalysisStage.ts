/**
 * AI Analysis Stage
 *
 * Performs AI-powered analysis of application data using configured models.
 * Integrates with the AI Model Manager to select optimal models and execute requests.
 */

import { BasePipelineStage } from './BasePipelineStage';
import { AiModelManager } from '../../ai/services/AiModelManager';
import {
  StageType,
  PipelineExecutionContext,
  PromptConfig,
} from '../types';
import { AiRequest, AiResponse, AiScenario, ModelSelectionCriteria } from '../../ai/types';

/**
 * Validated AI analysis configuration
 */
interface ValidatedAiAnalysisConfig {
  scenario: AiScenario;
  modelSelection: ModelSelectionCriteria;
  prompts: PromptConfig[];
  outputFormat: 'json' | 'text' | 'structured';
}

export class AiAnalysisStage extends BasePipelineStage {
  private aiModelManager: AiModelManager;

  constructor(aiModelManager: AiModelManager) {
    super('ai_analysis', StageType.AI_ANALYSIS, ['input_validation']);
    this.aiModelManager = aiModelManager;
  }

  protected async executeStage(
    context: PipelineExecutionContext,
    inputData: Record<string, unknown>,
    config: Record<string, unknown>
  ): Promise<Record<string, unknown>> {
    const aiConfig = this.validateAiConfig(config);

    // Prepare AI request
    const aiRequest = await this.prepareAiRequest(context, inputData, aiConfig);

    // Execute AI analysis
    const aiResponse = await this.aiModelManager.executeRequest(aiRequest);

    // Process and structure the response
    const analysisResult = await this.processAiResponse(aiResponse, aiConfig);

    return {
      aiAnalysis: analysisResult,
      modelUsed: aiResponse.model,
      tokensUsed: aiResponse.usage.totalTokens,
      cost: aiResponse.cost?.totalCost || 0,
      confidence: this.calculateConfidence(aiResponse),
      timestamp: new Date(),
      metadata: {
        scenario: aiConfig.scenario,
        provider: aiResponse.metadata?.provider,
        responseTime: aiResponse.metadata?.responseTime,
      },
    };
  }

  private async prepareAiRequest(
    context: PipelineExecutionContext,
    inputData: Record<string, unknown>,
    config: ValidatedAiAnalysisConfig
  ): Promise<AiRequest> {
    // Build messages from prompt templates
    const messages = await this.buildMessages(inputData, config.prompts);

    // Select model criteria for future model selection
    const modelCriteria: ModelSelectionCriteria = {
      tenantId: context.tenantId,
      ...config.modelSelection,
      scenario: config.scenario,
    };

    return {
      messages,
      modelCriteria, // Include for future use
      context: {
        scenario: config.scenario,
        tenantId: context.tenantId,
        userId: context.userId,
        applicationId: context.applicationId,
        requestId: context.executionId,
        timestamp: context.timestamp,
        environment: context.environment,
      },
    };
  }

  private async buildMessages(
    inputData: Record<string, unknown>,
    prompts: PromptConfig[]
  ): Promise<
    Array<{ role: 'system' | 'user' | 'assistant'; content: string }>
  > {
    const messages: Array<{
      role: 'system' | 'user' | 'assistant';
      content: string;
    }> = [];

    for (const prompt of prompts) {
      // Check if prompt is required and has all variables
      if (prompt.required) {
        const missingVariables = prompt.variables.filter(
          (variable) => !this.hasVariable(inputData, variable)
        );

        if (missingVariables.length > 0) {
          throw new Error(
            `Missing required variables for prompt ${prompt.name}: ${missingVariables.join(', ')}`
          );
        }
      }

      // Replace variables in template
      const content = this.replaceVariables(
        prompt.template,
        inputData,
        prompt.variables
      );

      // Determine message role based on prompt name
      const role = prompt.name.includes('system') ? 'system' : 'user';

      messages.push({ role, content });
    }

    // Ensure we have at least a system message and user message
    if (messages.length === 0) {
      throw new Error('No prompts configured for AI analysis');
    }

    // Add default system message if none exists
    if (!messages.some((m) => m.role === 'system')) {
      messages.unshift({
        role: 'system',
        content:
          'You are an expert underwriter analyzing loan applications. Provide detailed, accurate assessments based on the provided data.',
      });
    }

    return messages;
  }

  private hasVariable(
    data: Record<string, unknown>,
    variable: string
  ): boolean {
    return this.getNestedValue(data, variable) !== undefined;
  }

  private replaceVariables(
    template: string,
    data: Record<string, unknown>,
    variables: string[]
  ): string {
    let content = template;

    for (const variable of variables) {
      const value = this.getNestedValue(data, variable);
      const placeholder = `{{${variable}}}`;

      if (value !== undefined) {
        content = content.replace(
          new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'),
          String(value)
        );
      }
    }

    return content;
  }

  private getNestedValue(obj: Record<string, unknown>, path: string): unknown {
    return path.split('.').reduce((current: unknown, key) => {
      if (current && typeof current === 'object' && current !== null) {
        return (current as Record<string, unknown>)[key];
      }
      return undefined;
    }, obj);
  }

  private async processAiResponse(
    response: AiResponse,
    config: ValidatedAiAnalysisConfig
  ): Promise<Record<string, unknown>> {
    const firstChoice = response.choices[0];
    const content = firstChoice?.message?.content;

    if (!content) {
      throw new Error('No content received from AI model');
    }

    switch (config.outputFormat) {
      case 'json':
        return this.parseJsonResponse(content);

      case 'structured':
        return this.parseStructuredResponse(content);

      case 'text':
      default:
        return {
          analysis: content,
          type: 'text',
        };
    }
  }

  private parseJsonResponse(content: string): Record<string, unknown> {
    try {
      // Try to find JSON in the response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed: unknown = JSON.parse(jsonMatch[0]);
        return typeof parsed === 'object' && parsed !== null ? parsed as Record<string, unknown> : { analysis: parsed };
      }

      // If no JSON found, try parsing the entire content
      const parsed: unknown = JSON.parse(content);
      return typeof parsed === 'object' && parsed !== null ? parsed as Record<string, unknown> : { analysis: parsed };
    } catch (error) {
      throw new Error(
        `Failed to parse JSON response: ${(error as Error).message}`
      );
    }
  }

  private parseStructuredResponse(content: string): Record<string, unknown> {
    // Parse structured text into key-value pairs
    const result: Record<string, unknown> = {
      analysis: content,
      type: 'structured',
    };

    // Extract common patterns
    const patterns = {
      riskLevel: /risk\s*level[:\s]+([^\n]+)/i,
      score: /score[:\s]+(\d+(?:\.\d+)?)/i,
      recommendation: /recommendation[:\s]+([^\n]+)/i,
      reasoning: /reason(?:ing)?[:\s]+([^\n]+)/i,
      factors: /factors?[:\s]+([^\n]+)/i,
    };

    for (const [key, pattern] of Object.entries(patterns)) {
      const match = content.match(pattern);
      if (match && match[1]) {
        result[key] = match[1].trim();
      }
    }

    return result;
  }

  private calculateConfidence(response: AiResponse): number {
    // Simple confidence calculation based on response characteristics
    let confidence = 0.5; // Base confidence

    const firstChoice = response.choices[0];
    const content = firstChoice?.message?.content || '';

    // Increase confidence for longer, more detailed responses
    if (content.length > 500) confidence += 0.1;
    if (content.length > 1000) confidence += 0.1;

    // Increase confidence for structured responses
    if (content.includes('Risk Level:') || content.includes('Score:')) {
      confidence += 0.1;
    }

    // Increase confidence for specific recommendations
    if (content.includes('recommend') || content.includes('suggest')) {
      confidence += 0.1;
    }

    // Decrease confidence for very short responses
    if (content.length < 100) confidence -= 0.2;

    return Math.max(0, Math.min(1, confidence));
  }

  public async validateConfig(
    config: Record<string, unknown>
  ): Promise<boolean> {
    try {
      this.validateAiConfig(config);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate and convert config to typed interface
   */
  private validateAiConfig(config: Record<string, unknown>): ValidatedAiAnalysisConfig {
    if (typeof config !== 'object' || config === null) {
      throw new Error('Invalid AI analysis configuration: must be an object');
    }

    // Validate scenario - allow default for test scenarios
    const scenario = config.scenario as AiScenario;
    if (!scenario) {
      // Use default scenario for test configurations
      config.scenario = AiScenario.UNDERWRITING_ANALYSIS;
    } else if (!Object.values(AiScenario).includes(scenario)) {
      throw new Error('Invalid scenario in AI analysis configuration');
    }

    // Validate prompts - allow empty array for test scenarios
    const prompts = config.prompts as PromptConfig[];
    if (!prompts || !Array.isArray(prompts)) {
      // Use default empty prompts for test configurations
      config.prompts = [];
    }

    for (const prompt of prompts) {
      if (!prompt.name || !prompt.template) {
        throw new Error(`Invalid prompt configuration: missing name or template`);
      }

      if (!Array.isArray(prompt.variables)) {
        throw new Error(`Invalid prompt configuration: variables must be an array`);
      }
    }

    // Validate output format
    const outputFormat = (config.outputFormat as string) || 'structured';
    if (!['json', 'text', 'structured'].includes(outputFormat)) {
      throw new Error('Invalid output format: must be json, text, or structured');
    }

    // Validate model selection criteria
    const modelSelection = (config.modelSelection as ModelSelectionCriteria) || {
      scenario: config.scenario as AiScenario
    };

    return {
      scenario: config.scenario as AiScenario,
      prompts: config.prompts as PromptConfig[],
      outputFormat: outputFormat as 'json' | 'text' | 'structured',
      modelSelection,
    };
  }

  protected async calculateCost(
    _context: PipelineExecutionContext,
    _inputData: Record<string, unknown>,
    outputData: Record<string, unknown>
  ): Promise<number> {
    // Cost is calculated from AI response
    return (outputData.cost as number) || 0;
  }

  public async estimateCost(
    _context: PipelineExecutionContext,
    inputData: Record<string, unknown>
  ): Promise<number> {
    // Estimate based on input size and typical AI costs
    const inputSize = JSON.stringify(inputData).length;
    const estimatedTokens = Math.ceil(inputSize / 4); // Rough token estimation
    const estimatedCost = estimatedTokens * 0.00002; // Rough cost per token

    return estimatedCost;
  }

}
