/**
 * Base Pipeline Stage
 *
 * Abstract base class that provides common functionality for all pipeline stages.
 * Implements retry logic, error handling, metrics collection, and cost tracking.
 */

import {
  IPipelineStage,
  StageType,
  PipelineExecutionContext,
  StageExecutionResult,
  StageStatus,
  StageError,
  RetryPolicy,
} from '../types';

export abstract class BasePipelineStage implements IPipelineStage {
  public readonly name: string;
  public readonly type: StageType;
  public readonly dependencies: string[];

  protected defaultRetryPolicy: RetryPolicy = {
    maxAttempts: 3,
    initialDelayMs: 1000,
    maxDelayMs: 30000,
    backoffMultiplier: 2,
    retryableErrors: [
      'TIMEOUT',
      'NETWORK_ERROR',
      'SERVICE_UNAVAILABLE',
      'RATE_LIMIT_EXCEEDED',
    ],
  };

  constructor(name: string, type: StageType, dependencies: string[] = []) {
    this.name = name;
    this.type = type;
    this.dependencies = dependencies;
  }

  /**
   * Main execution method with retry logic and error handling
   */
  public async execute(
    context: PipelineExecutionContext,
    inputData: Record<string, unknown>,
    config: Record<string, unknown>
  ): Promise<StageExecutionResult> {
    const startTime = Date.now();
    const retryPolicy = this.getRetryPolicy(config);

    let lastError: StageError | null = null;
    let retryCount = 0;

    while (retryCount <= retryPolicy.maxAttempts) {
      try {
        this.logStageStart(context, retryCount);

        // Validate inputs before execution
        await this.validateInputs(inputData, config);

        // Execute the stage-specific logic
        const outputData = await this.executeStage(context, inputData, config);

        // Validate outputs after execution
        await this.validateOutputs(outputData, config);

        const duration = Date.now() - startTime;
        const cost = await this.calculateCost(context, inputData, outputData);

        this.logStageSuccess(context, duration, cost);

        return {
          stageName: this.name,
          status: StageStatus.COMPLETED,
          startedAt: new Date(startTime),
          completedAt: new Date(),
          duration,
          inputData,
          outputData,
          retryCount,
          cost,
          metrics: await this.collectMetrics(
            context,
            inputData,
            outputData,
            duration
          ),
        };
      } catch (error) {
        retryCount++;
        lastError = this.createStageError(error, retryCount);

        this.logStageError(context, lastError, retryCount);

        // Check if we should retry
        if (
          retryCount <= retryPolicy.maxAttempts &&
          this.shouldRetry(lastError, retryPolicy)
        ) {
          const delay = this.calculateBackoffDelay(retryCount, retryPolicy);
          await this.sleep(delay);
          continue;
        }

        // Max retries exceeded or non-retryable error
        break;
      }
    }

    // Return failure result
    const duration = Date.now() - startTime;
    return {
      stageName: this.name,
      status: StageStatus.FAILED,
      startedAt: new Date(startTime),
      completedAt: new Date(),
      duration,
      inputData,
      error:
        lastError ||
        this.createStageError(new Error('Unknown error'), retryCount),
      retryCount: retryCount - 1,
      cost: 0,
    };
  }

  /**
   * Abstract method that subclasses must implement for stage-specific logic
   */
  protected abstract executeStage(
    context: PipelineExecutionContext,
    inputData: Record<string, unknown>,
    config: Record<string, unknown>
  ): Promise<Record<string, unknown>>;

  /**
   * Validate stage configuration - can be overridden by subclasses
   */
  public async validateConfig(
    config: Record<string, unknown>
  ): Promise<boolean> {
    // Basic validation - subclasses should override for specific validation
    return typeof config === 'object' && config !== null;
  }

  /**
   * Check if stage can be executed - can be overridden by subclasses
   */
  public async canExecute(
    _context: PipelineExecutionContext,
    completedStages: string[],
    stageResults: Map<string, StageExecutionResult>
  ): Promise<boolean> {
    // Check if all dependencies are satisfied
    for (const dependency of this.dependencies) {
      if (!completedStages.includes(dependency)) {
        return false;
      }

      const dependencyResult = stageResults.get(dependency);
      if (
        !dependencyResult ||
        dependencyResult.status !== StageStatus.COMPLETED
      ) {
        return false;
      }
    }

    return true;
  }

  /**
   * Estimate cost for stage execution - can be overridden by subclasses
   */
  public async estimateCost(
    _context: PipelineExecutionContext,
    _inputData: Record<string, unknown>
  ): Promise<number> {
    // Default implementation returns 0 - subclasses should override
    return 0;
  }

  /**
   * Validate input data - can be overridden by subclasses
   */
  protected async validateInputs(
    inputData: Record<string, unknown>,
    _config: Record<string, unknown>
  ): Promise<void> {
    if (!inputData || typeof inputData !== 'object') {
      throw new Error('Invalid input data: must be an object');
    }
  }

  /**
   * Validate output data - can be overridden by subclasses
   */
  protected async validateOutputs(
    outputData: Record<string, unknown>,
    _config: Record<string, unknown>
  ): Promise<void> {
    if (!outputData || typeof outputData !== 'object') {
      throw new Error('Invalid output data: must be an object');
    }
  }

  /**
   * Calculate cost for stage execution - can be overridden by subclasses
   */
  protected async calculateCost(
    _context: PipelineExecutionContext,
    _inputData: Record<string, unknown>,
    _outputData: Record<string, unknown>
  ): Promise<number> {
    return 0;
  }

  /**
   * Collect stage-specific metrics - can be overridden by subclasses
   */
  protected async collectMetrics(
    _context: PipelineExecutionContext,
    inputData: Record<string, unknown>,
    outputData: Record<string, unknown>,
    duration: number
  ): Promise<Record<string, unknown>> {
    return {
      executionTime: duration,
      inputSize: JSON.stringify(inputData).length,
      outputSize: JSON.stringify(outputData).length,
      timestamp: new Date(),
    };
  }

  /**
   * Get retry policy for this stage
   */
  protected getRetryPolicy(config: Record<string, unknown>): RetryPolicy {
    const stageRetryPolicy = config.retryPolicy as RetryPolicy;
    return {
      ...this.defaultRetryPolicy,
      ...stageRetryPolicy,
    };
  }

  /**
   * Determine if error is retryable
   */
  protected shouldRetry(error: StageError, retryPolicy: RetryPolicy): boolean {
    if (!error.recoverable) {
      return false;
    }

    return retryPolicy.retryableErrors.some(
      (pattern) =>
        error.code.includes(pattern) || error.message.includes(pattern)
    );
  }

  /**
   * Calculate backoff delay for retry
   */
  protected calculateBackoffDelay(
    attempt: number,
    retryPolicy: RetryPolicy
  ): number {
    const exponentialDelay =
      retryPolicy.initialDelayMs *
      Math.pow(retryPolicy.backoffMultiplier, attempt - 1);
    return Math.min(exponentialDelay, retryPolicy.maxDelayMs);
  }

  /**
   * Create standardized stage error
   */
  protected createStageError(error: unknown, retryCount: number): StageError {
    if (error instanceof Error) {
      return {
        code: this.getErrorCode(error),
        message: error.message,
        timestamp: new Date(),
        recoverable: this.isRecoverableError(error),
        details: {
          stageName: this.name,
          stageType: this.type,
          retryCount,
          stack: error.stack,
        },
      };
    }

    return {
      code: 'UNKNOWN_ERROR',
      message: 'An unknown error occurred',
      timestamp: new Date(),
      recoverable: false,
      details: {
        stageName: this.name,
        stageType: this.type,
        retryCount,
        error: String(error),
      },
    };
  }

  /**
   * Determine error code from error
   */
  protected getErrorCode(error: Error): string {
    // Check for common error patterns
    if (error.message.includes('timeout')) return 'TIMEOUT';
    if (error.message.includes('network')) return 'NETWORK_ERROR';
    if (error.message.includes('rate limit')) return 'RATE_LIMIT_EXCEEDED';
    if (error.message.includes('validation')) return 'VALIDATION_ERROR';
    if (error.message.includes('unauthorized')) return 'AUTHORIZATION_ERROR';
    if (error.message.includes('forbidden')) return 'AUTHORIZATION_ERROR';
    if (error.message.includes('not found')) return 'RESOURCE_NOT_FOUND';
    if (error.message.includes('service unavailable'))
      return 'SERVICE_UNAVAILABLE';

    return 'STAGE_EXECUTION_ERROR';
  }

  /**
   * Determine if error is recoverable
   */
  protected isRecoverableError(error: Error): boolean {
    const recoverablePatterns = [
      'timeout',
      'network',
      'connection',
      'rate limit',
      'service unavailable',
      'temporarily unavailable',
    ];

    const errorMessage = error.message.toLowerCase();
    return recoverablePatterns.some((pattern) =>
      errorMessage.includes(pattern)
    );
  }

  /**
   * Sleep for specified duration
   */
  protected sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Logging methods - can be overridden to integrate with logging system
   */
  protected logStageStart(
    context: PipelineExecutionContext,
    retryCount: number
  ): void {
    const logMessage =
      retryCount === 0
        ? `Stage ${this.name} starting for execution ${context.executionId}`
        : `Stage ${this.name} retrying (attempt ${retryCount + 1}) for execution ${context.executionId}`;

    console.log(logMessage, {
      executionId: context.executionId,
      stageName: this.name,
      stageType: this.type,
      retryCount,
      timestamp: new Date(),
    });
  }

  protected logStageSuccess(
    context: PipelineExecutionContext,
    duration: number,
    cost: number
  ): void {
    console.log(
      `Stage ${this.name} completed successfully for execution ${context.executionId}`,
      {
        executionId: context.executionId,
        stageName: this.name,
        stageType: this.type,
        duration,
        cost,
        timestamp: new Date(),
      }
    );
  }

  protected logStageError(
    context: PipelineExecutionContext,
    error: StageError,
    retryCount: number
  ): void {
    const logLevel = error.recoverable ? 'warn' : 'error';
    const message = `Stage ${this.name} failed for execution ${context.executionId}: ${error.message}`;

    console[logLevel](message, {
      executionId: context.executionId,
      stageName: this.name,
      stageType: this.type,
      errorCode: error.code,
      retryCount,
      recoverable: error.recoverable,
      timestamp: new Date(),
    });
  }
}
