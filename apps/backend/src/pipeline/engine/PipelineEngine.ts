/**
 * Pipeline Engine
 *
 * Core orchestration engine that manages pipeline execution, stage coordination,
 * error handling, and monitoring for AI-powered underwriting decisions.
 */

import { PrismaClient } from '@prisma/client';
import {
  IPipelineEngine,
  IPipelineStage,
  StageType,
  PipelineConfig,
  PipelineStageConfig,
  PipelineGlobalConfig,
  PipelineExecutionContext,
  PipelineExecutionResult,
  StageExecutionResult,
  PipelineStatus,
  StageStatus,
  ExecutionTrigger,
  PipelineError,
  PipelineOutput,
  ErrorHandlingStrategy,
  MonitoringConfig,
  CostLimits,
} from '../types';

/**
 * Database pipeline record interface
 */
interface DatabasePipelineRecord {
  id: string;
  name: string;
  description: string | null;
  version: string;
  stages: unknown;
  config: unknown;
  applicableTypes: string[];
  isActive: boolean;
  isDefault: boolean;
  tags: string[];
}

/**
 * Database execution record interface
 */
interface DatabaseExecutionRecord {
  id: string;
  status: string;
  startedAt: Date;
  completedAt: Date | null;
  totalDuration: number | null;
  inputData: unknown;
  outputData: unknown;
  totalCost: number | bigint;
  errorCode: string | null;
  errorMessage: string | null;
  updatedAt: Date;
  stageExecutions: DatabaseStageExecution[];
}

/**
 * Database stage execution record interface
 */
interface DatabaseStageExecution {
  stageName: string;
  status: string;
  startedAt: Date;
  completedAt: Date | null;
  duration: number | null;
  inputData: unknown;
  outputData: unknown;
  errorCode: string | null;
  errorMessage: string | null;
  retryCount: number;
  cost: number | bigint;
  updatedAt: Date;
  metrics: unknown;
}

/**
 * Application data interface
 */
interface ApplicationData {
  id: string;
  tenantId: string;
  businessName: string;
  businessEmail: string;
  requestedAmount: number;
  monthlyRevenue: number;
  businessType: string;
  applicationData: Array<{
    fieldName: string;
    value: unknown;
  }>;
}

/**
 * Validated execution record interface
 */
interface ValidatedExecutionRecord {
  id: string;
  pipelineId: string;
  applicationId: string;
  tenantId: string;
  status: string;
  inputData: Record<string, unknown>;
}

/**
 * Pipeline Engine Configuration
 */
export interface PipelineEngineConfig {
  maxConcurrentExecutions: number;
  defaultTimeout: number;
  enableMetrics: boolean;
  enableAuditLog: boolean;
}

/**
 * Main Pipeline Engine Implementation
 */
export class PipelineEngine implements IPipelineEngine {
  private prisma: PrismaClient;
  private config: PipelineEngineConfig;
  private stageProcessors: Map<StageType, IPipelineStage> = new Map();
  private activeExecutions: Map<string, PipelineExecutionContext> = new Map();

  constructor(
    prisma: PrismaClient,
    config: PipelineEngineConfig = {
      maxConcurrentExecutions: 100,
      defaultTimeout: 300000, // 5 minutes
      enableMetrics: true,
      enableAuditLog: true,
    }
  ) {
    this.prisma = prisma;
    this.config = config;
  }

  /**
   * Register a stage processor
   */
  public registerStage(stageType: StageType, processor: IPipelineStage): void {
    this.stageProcessors.set(stageType, processor);
    console.log(`Registered stage processor for type: ${stageType}`);
  }

  /**
   * Execute a pipeline for an application
   */
  public async executePipeline(
    pipelineId: string,
    applicationId: string,
    context: Partial<PipelineExecutionContext>
  ): Promise<PipelineExecutionResult> {
    // Check concurrent execution limits
    if (this.activeExecutions.size >= this.config.maxConcurrentExecutions) {
      throw new Error('Maximum concurrent executions reached');
    }

    const executionId = this.generateExecutionId();
    const startTime = new Date();

    // Load pipeline configuration
    const pipelineConfig = await this.getPipelineConfig(pipelineId);

    // Load application data
    const applicationData = await this.loadApplicationData(applicationId);

    // Build execution context
    const executionContext: PipelineExecutionContext = {
      executionId,
      pipelineId,
      applicationId,
      tenantId: context.tenantId || (applicationData.tenantId as string),
      userId: context.userId,
      trigger: context.trigger || ExecutionTrigger.MANUAL,
      inputData: {
        ...applicationData,
        ...context.inputData,
      },
      environment: context.environment || 'production',
      timestamp: startTime,
      metadata: context.metadata,
    };

    // Create database execution record
    await this.createExecutionRecord(executionContext, pipelineConfig);

    // Track active execution
    this.activeExecutions.set(executionId, executionContext);

    try {
      // Execute pipeline with timeout
      const result = await Promise.race([
        this.executeStages(executionContext, pipelineConfig),
        this.createTimeoutPromise(
          pipelineConfig.config.timeout || this.config.defaultTimeout
        ),
      ]);

      // Update execution record with results
      await this.updateExecutionRecord(executionId, result);

      return result;
    } catch (error) {
      // Handle execution error
      const errorResult: PipelineExecutionResult = {
        executionId: executionContext.executionId,
        status: PipelineStatus.FAILED,
        startedAt: executionContext.timestamp,
        completedAt: new Date(),
        duration: Date.now() - executionContext.timestamp.getTime(),
        inputData: executionContext.inputData,
        stageResults: new Map(),
        error: this.createPipelineError(error),
        metrics: {
          totalStages: 0,
          completedStages: 0,
          failedStages: 0,
          skippedStages: 0,
          totalCost: 0,
          averageStageTime: 0,
        },
        cost: 0,
      };
      await this.updateExecutionRecord(executionId, errorResult);
      return errorResult;
    } finally {
      // Clean up active execution tracking
      this.activeExecutions.delete(executionId);
    }
  }

  /**
   * Get pipeline configuration
   */
  public async getPipelineConfig(pipelineId: string): Promise<PipelineConfig> {
    const dbPipeline = await this.prisma.decisionPipeline.findUnique({
      where: { id: pipelineId },
      include: { metrics: true },
    }) as DatabasePipelineRecord | null;

    if (!dbPipeline) {
      throw new Error(`Pipeline not found: ${pipelineId}`);
    }

    if (!dbPipeline.isActive) {
      throw new Error(`Pipeline is not active: ${pipelineId}`);
    }

    // Validate and cast stages configuration
    const stages = this.validateStagesConfig(dbPipeline.stages);
    const config = this.validateGlobalConfig(dbPipeline.config);

    return {
      name: dbPipeline.name,
      description: dbPipeline.description || undefined,
      version: dbPipeline.version,
      stages,
      config,
      applicableTypes: dbPipeline.applicableTypes,
      isActive: dbPipeline.isActive,
      isDefault: dbPipeline.isDefault,
      tags: dbPipeline.tags,
    };
  }

  /**
   * Validate and cast stages configuration from database
   */
  private validateStagesConfig(stages: unknown): PipelineStageConfig[] {
    if (!Array.isArray(stages)) {
      throw new Error('Invalid stages configuration: must be an array');
    }

    return stages.map((stage: unknown, index: number) => {
      if (typeof stage !== 'object' || stage === null) {
        throw new Error(`Invalid stage configuration at index ${index}`);
      }

      const stageObj = stage as Record<string, unknown>;
      
      if (typeof stageObj.name !== 'string' || typeof stageObj.type !== 'string') {
        throw new Error(`Invalid stage configuration at index ${index}: missing name or type`);
      }

      return {
        name: stageObj.name,
        type: stageObj.type as StageType,
        description: typeof stageObj.description === 'string' ? stageObj.description : undefined,
        dependencies: Array.isArray(stageObj.dependencies) ? stageObj.dependencies as string[] : [],
        config: typeof stageObj.config === 'object' && stageObj.config !== null ? stageObj.config as Record<string, unknown> : {},
        timeout: typeof stageObj.timeout === 'number' ? stageObj.timeout : undefined,
        enabled: typeof stageObj.enabled === 'boolean' ? stageObj.enabled : true,
        parallel: typeof stageObj.parallel === 'boolean' ? stageObj.parallel : false,
      };
    });
  }

  /**
   * Validate and cast global configuration from database
   */
  private validateGlobalConfig(config: unknown): PipelineGlobalConfig {
    if (typeof config !== 'object' || config === null) {
      throw new Error('Invalid pipeline configuration: must be an object');
    }

    const configObj = config as Record<string, unknown>;

    return {
      timeout: typeof configObj.timeout === 'number' ? configObj.timeout : 300000,
      maxRetries: typeof configObj.maxRetries === 'number' ? configObj.maxRetries : 3,
      parallelExecution: typeof configObj.parallelExecution === 'boolean' ? configObj.parallelExecution : false,
      failFast: typeof configObj.failFast === 'boolean' ? configObj.failFast : true,
      errorHandling: typeof configObj.errorHandling === 'string' ? configObj.errorHandling as ErrorHandlingStrategy : ErrorHandlingStrategy.FAIL_FAST,
      monitoring: typeof configObj.monitoring === 'object' && configObj.monitoring !== null ? configObj.monitoring as MonitoringConfig : { enabled: true, stageMetrics: true, performanceTracking: true, costTracking: true, errorTracking: true },
      costLimits: typeof configObj.costLimits === 'object' && configObj.costLimits !== null ? configObj.costLimits as CostLimits : undefined,
    };
  }

  /**
   * Get pipeline execution status
   */
  public async getExecutionStatus(
    executionId: string
  ): Promise<PipelineExecutionResult> {
    const execution = await this.prisma.pipelineExecution.findUnique({
      where: { id: executionId },
      include: {
        stageExecutions: {
          orderBy: { stageIndex: 'asc' },
        },
      },
    }) as unknown as (DatabaseExecutionRecord & { createdAt: Date }) | null;

    if (!execution) {
      throw new Error(`Execution not found: ${executionId}`);
    }

    // Build stage results map
    const stageResults = new Map<string, StageExecutionResult>();
    for (const stageExec of execution.stageExecutions) {
      stageResults.set(stageExec.stageName, {
        stageName: stageExec.stageName,
        status: stageExec.status as StageStatus,
        startedAt: stageExec.startedAt,
        completedAt: stageExec.completedAt || undefined,
        duration: stageExec.duration || undefined,
        inputData: this.safeParseInputData(stageExec.inputData),
        outputData: this.safeParseOutputData(stageExec.outputData),
        error: stageExec.errorCode
          ? {
              code: stageExec.errorCode,
              message: stageExec.errorMessage || 'Unknown error',
              timestamp: stageExec.updatedAt,
              recoverable: false,
            }
          : undefined,
        retryCount: stageExec.retryCount,
        cost: parseFloat(stageExec.cost.toString()),
      });
    }

    const totalCost = parseFloat(execution.totalCost.toString());
    const inputData = this.safeParseInputData(execution.inputData);
    const outputData = this.safeParseOutputData(execution.outputData) as unknown as PipelineOutput | undefined;

    return {
      executionId: execution.id,
      status: execution.status as PipelineStatus,
      startedAt: execution.startedAt,
      completedAt: execution.completedAt || undefined,
      duration: execution.totalDuration || undefined,
      inputData: inputData || {},
      outputData,
      stageResults,
      error: execution.errorCode
        ? {
            code: execution.errorCode,
            message: execution.errorMessage || 'Unknown error',
            timestamp: new Date(),
            recoverable: false,
          }
        : undefined,
      metrics: {
        totalStages: stageResults.size,
        completedStages: Array.from(stageResults.values()).filter(
          (s) => s.status === StageStatus.COMPLETED
        ).length,
        failedStages: Array.from(stageResults.values()).filter(
          (s) => s.status === StageStatus.FAILED
        ).length,
        skippedStages: Array.from(stageResults.values()).filter(
          (s) => s.status === StageStatus.SKIPPED
        ).length,
        totalCost,
        averageStageTime: execution.totalDuration
          ? execution.totalDuration / stageResults.size
          : 0,
      },
      cost: totalCost,
    };
  }

  /**
   * Safely parse input data from database
   */
  private safeParseInputData(data: unknown): Record<string, unknown> | undefined {
    if (data === null || data === undefined) {
      return undefined;
    }
    if (typeof data === 'object') {
      return data as Record<string, unknown>;
    }
    return undefined;
  }

  /**
   * Safely parse output data from database
   */
  private safeParseOutputData(data: unknown): Record<string, unknown> | undefined {
    if (data === null || data === undefined) {
      return undefined;
    }
    if (typeof data === 'object') {
      return data as Record<string, unknown>;
    }
    return undefined;
  }

  /**
   * Cancel a running pipeline execution
   */
  public async cancelExecution(executionId: string): Promise<void> {
    const execution = this.activeExecutions.get(executionId);
    if (!execution) {
      throw new Error(`No active execution found: ${executionId}`);
    }

    // Update database record
    await this.prisma.pipelineExecution.update({
      where: { id: executionId },
      data: {
        status: PipelineStatus.CANCELLED,
        completedAt: new Date(),
        errorCode: 'EXECUTION_CANCELLED',
        errorMessage: 'Pipeline execution was cancelled by user',
      },
    });

    // Remove from active executions
    this.activeExecutions.delete(executionId);

    console.log(`Pipeline execution cancelled: ${executionId}`);
  }

  /**
   * Retry a failed pipeline execution
   */
  public async retryExecution(
    executionId: string
  ): Promise<PipelineExecutionResult> {
    const originalExecution = await this.prisma.pipelineExecution.findUnique({
      where: { id: executionId },
    }) as ValidatedExecutionRecord | null;

    if (!originalExecution) {
      throw new Error(`Execution not found: ${executionId}`);
    }

    if (originalExecution.status !== PipelineStatus.FAILED) {
      throw new Error(`Can only retry failed executions: ${executionId}`);
    }

    // Create new execution with retry context
    return this.executePipeline(
      originalExecution.pipelineId,
      originalExecution.applicationId,
      {
        tenantId: originalExecution.tenantId,
        trigger: ExecutionTrigger.RETRY,
        inputData: originalExecution.inputData,
        metadata: {
          originalExecutionId: executionId,
          retryTimestamp: new Date(),
        },
      }
    );
  }

  /**
   * Execute all stages in the pipeline
   */
  private async executeStages(
    context: PipelineExecutionContext,
    config: PipelineConfig
  ): Promise<PipelineExecutionResult> {
    const startTime = Date.now();
    const stageResults = new Map<string, StageExecutionResult>();
    const completedStages: string[] = [];
    let totalCost = 0;

    // Build stage dependency graph
    const stageGraph = this.buildStageGraph(config.stages);

    try {
      // Process stages in dependency order
      while (completedStages.length < config.stages.length) {
        const readyStages = this.getReadyStages(
          config.stages,
          completedStages,
          stageGraph
        );

        if (readyStages.length === 0) {
          throw new Error('Pipeline deadlock detected - no stages can execute');
        }

        // Execute ready stages (possibly in parallel)
        const stagePromises = readyStages.map((stageConfig) =>
          this.executeStage(context, stageConfig, stageResults)
        );

        const stageExecutionResults = await Promise.allSettled(stagePromises);

        // Process stage results
        for (let i = 0; i < stageExecutionResults.length; i++) {
          const result = stageExecutionResults[i];
          const stageConfig = readyStages[i];

          if (!result || !stageConfig) {
            continue;
          }

          if (result.status === 'fulfilled') {
            const stageResult = result.value;
            stageResults.set(stageConfig.name, stageResult);
            totalCost += stageResult.cost;

            if (stageResult.status === StageStatus.COMPLETED) {
              completedStages.push(stageConfig.name);
            } else if (stageResult.status === StageStatus.FAILED) {
              // Handle stage failure based on error strategy
              await this.handleStageFailure(
                context,
                config,
                stageConfig,
                stageResult
              );
            }
          } else if (result.status === 'rejected') {
            // Handle unexpected stage error
            const error = result.reason as Error;
            throw new Error(
              `Stage ${stageConfig.name} failed unexpectedly: ${error.message || 'Unknown error'}`
            );
          }
        }
      }

      // All stages completed successfully
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Generate final output
      const output = this.generatePipelineOutput(stageResults);

      return {
        executionId: context.executionId,
        status: PipelineStatus.COMPLETED,
        startedAt: new Date(startTime),
        completedAt: new Date(endTime),
        duration,
        inputData: context.inputData,
        outputData: output,
        stageResults,
        metrics: {
          totalStages: config.stages.length,
          completedStages: completedStages.length,
          failedStages: Array.from(stageResults.values()).filter(
            (s) => s.status === StageStatus.FAILED
          ).length,
          skippedStages: Array.from(stageResults.values()).filter(
            (s) => s.status === StageStatus.SKIPPED
          ).length,
          totalCost,
          averageStageTime: duration / config.stages.length,
        },
        cost: totalCost,
      };
    } catch (error) {
      // Pipeline execution failed
      const endTime = Date.now();
      const duration = endTime - startTime;

      return {
        executionId: context.executionId,
        status: PipelineStatus.FAILED,
        startedAt: new Date(startTime),
        completedAt: new Date(endTime),
        duration,
        inputData: context.inputData,
        stageResults,
        error: this.createPipelineError(error),
        metrics: {
          totalStages: config.stages.length,
          completedStages: completedStages.length,
          failedStages: Array.from(stageResults.values()).filter(
            (s) => s.status === StageStatus.FAILED
          ).length,
          skippedStages: Array.from(stageResults.values()).filter(
            (s) => s.status === StageStatus.SKIPPED
          ).length,
          totalCost,
          averageStageTime: duration / Math.max(completedStages.length, 1),
        },
        cost: totalCost,
      };
    }
  }

  /**
   * Execute a single stage
   */
  private async executeStage(
    context: PipelineExecutionContext,
    stageConfig: PipelineStageConfig,
    stageResults: Map<string, StageExecutionResult>
  ): Promise<StageExecutionResult> {
    // Get stage processor
    const processor = this.stageProcessors.get(stageConfig.type);
    if (!processor) {
      throw new Error(
        `No processor registered for stage type: ${stageConfig.type}`
      );
    }

    // Check if stage can execute
    const canExecute = await processor.canExecute(
      context,
      Array.from(stageResults.keys()).filter(
        (name) => stageResults.get(name)?.status === StageStatus.COMPLETED
      ),
      stageResults
    );

    if (!canExecute) {
      return {
        stageName: stageConfig.name,
        status: StageStatus.SKIPPED,
        startedAt: new Date(),
        completedAt: new Date(),
        duration: 0,
        retryCount: 0,
        cost: 0,
      };
    }

    // Prepare input data from dependencies
    const inputData = this.prepareStageInput(
      context,
      stageConfig,
      stageResults
    );

    // Create stage execution record
    await this.createStageExecutionRecord(context.executionId, stageConfig, 0);

    try {
      // Execute stage
      const result = await processor.execute(
        context,
        inputData,
        stageConfig.config
      );

      // Update stage execution record
      await this.updateStageExecutionRecord(
        context.executionId,
        stageConfig.name,
        result
      );

      return result;
    } catch (error) {
      // Handle stage error
      const errorResult: StageExecutionResult = {
        stageName: stageConfig.name,
        status: StageStatus.FAILED,
        startedAt: new Date(),
        completedAt: new Date(),
        duration: 0,
        inputData,
        error: {
          code: 'STAGE_EXECUTION_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date(),
          recoverable: false,
        },
        retryCount: 0,
        cost: 0,
      };

      await this.updateStageExecutionRecord(
        context.executionId,
        stageConfig.name,
        errorResult
      );
      return errorResult;
    }
  }

  // Additional helper methods...

  private generateExecutionId(): string {
    return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async createTimeoutPromise(
    timeoutMs: number
  ): Promise<PipelineExecutionResult> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Pipeline execution timed out after ${timeoutMs}ms`));
      }, timeoutMs);
    });
  }

  private buildStageGraph(stages: PipelineStageConfig[]): Map<string, string[]> {
    const graph = new Map<string, string[]>();
    for (const stage of stages) {
      graph.set(stage.name, stage.dependencies || []);
    }
    return graph;
  }

  private getReadyStages(
    stages: PipelineStageConfig[],
    completed: string[],
    graph: Map<string, string[]>
  ): PipelineStageConfig[] {
    return stages.filter((stage) => {
      if (completed.includes(stage.name)) return false;
      const dependencies = graph.get(stage.name) || [];
      return dependencies.every((dep) => completed.includes(dep));
    });
  }

  private prepareStageInput(
    context: PipelineExecutionContext,
    stageConfig: PipelineStageConfig,
    stageResults: Map<string, StageExecutionResult>
  ): Record<string, unknown> {
    let inputData = { ...context.inputData };

    // Merge outputs from dependency stages
    for (const dependency of stageConfig.dependencies || []) {
      const dependencyResult = stageResults.get(dependency);
      if (dependencyResult && dependencyResult.outputData) {
        inputData = { ...inputData, ...dependencyResult.outputData };
      }
    }

    return inputData;
  }

  private generatePipelineOutput(
    stageResults: Map<string, StageExecutionResult>
  ): PipelineOutput {
    // This is a simplified implementation - would be more sophisticated in production
    // TODO: Implement logic to extract decision from final decision stage results

    return {
      decision: {
        result: 'REVIEW', // Default
        amount: 100000,
        terms: {
          interestRate: 5.5,
          termMonths: 36,
        },
      },
      confidence: 0.75,
      reasoning: ['Analysis completed successfully'],
      riskScore: 65,
      riskLevel: 'MEDIUM',
      factors: [],
      recommendations: ['Standard underwriting approved'],
      metadata: {
        pipelineCompleted: true,
        stagesExecuted: stageResults.size,
      },
    };
  }

  private createPipelineError(error: unknown): PipelineError {
    return {
      code: 'PIPELINE_EXECUTION_ERROR',
      message:
        error instanceof Error ? error.message : 'Unknown pipeline error',
      timestamp: new Date(),
      recoverable: false,
      details: {
        error: String(error),
      },
    };
  }

  private async handleStageFailure(
    _context: PipelineExecutionContext,
    config: PipelineConfig,
    stageConfig: PipelineStageConfig,
    stageResult: StageExecutionResult
  ): Promise<void> {
    const strategy =
      config.config.errorHandling || ErrorHandlingStrategy.FAIL_FAST;

    switch (strategy) {
      case ErrorHandlingStrategy.FAIL_FAST:
        throw new Error(
          `Stage ${stageConfig.name} failed: ${stageResult.error?.message || 'Unknown error'}`
        );

      case ErrorHandlingStrategy.CONTINUE_ON_ERROR:
        console.warn(
          `Stage ${stageConfig.name} failed, continuing pipeline`,
          stageResult.error
        );
        break;

      case ErrorHandlingStrategy.FALLBACK_TO_DEFAULT:
        // Implement fallback logic
        console.warn(
          `Stage ${stageConfig.name} failed, using fallback`,
          stageResult.error
        );
        break;

      default:
        throw new Error(
          `Stage ${stageConfig.name} failed: ${stageResult.error?.message || 'Unknown error'}`
        );
    }
  }

  private async loadApplicationData(
    applicationId: string
  ): Promise<Record<string, unknown>> {
    const application = await this.prisma.application.findUnique({
      where: { id: applicationId },
      include: {
        documents: true,
        riskAssessments: true,
        applicationData: true,
      },
    }) as unknown as ApplicationData | null;

    if (!application) {
      throw new Error(`Application not found: ${applicationId}`);
    }

    return {
      ...application,
      applicationData: application.applicationData.reduce(
        (acc, data) => {
          acc[data.fieldName] = data.value;
          return acc;
        },
        {} as Record<string, unknown>
      ),
    };
  }

  private async createExecutionRecord(
    context: PipelineExecutionContext,
    config: PipelineConfig
  ): Promise<void> {
    await this.prisma.pipelineExecution.create({
      data: {
        id: context.executionId,
        pipelineId: context.pipelineId,
        applicationId: context.applicationId,
        tenantId: context.tenantId,
        version: config.version,
        trigger: context.trigger,
        initiatedBy: context.userId,
        status: PipelineStatus.RUNNING,
        inputData: JSON.stringify(context.inputData),
        startedAt: context.timestamp,
        maxRetries: 3,
        totalCost: 0,
      },
    });
  }

  private async updateExecutionRecord(
    executionId: string,
    result: PipelineExecutionResult
  ): Promise<void> {
    await this.prisma.pipelineExecution.update({
      where: { id: executionId },
      data: {
        status: result.status,
        completedAt: result.completedAt,
        totalDuration: result.duration,
        outputData: result.outputData ? JSON.stringify(result.outputData) : undefined,
        totalCost: result.cost,
        errorCode: result.error?.code,
        errorMessage: result.error?.message,
      },
    });
  }

  private async createStageExecutionRecord(
    executionId: string,
    stageConfig: PipelineStageConfig,
    stageIndex: number
  ): Promise<void> {
    await this.prisma.pipelineStageExecution.create({
      data: {
        executionId,
        stageName: stageConfig.name,
        stageType: stageConfig.type,
        stageIndex,
        status: StageStatus.RUNNING,
        config: JSON.stringify(stageConfig.config),
        cost: 0,
      },
    });
  }

  private async updateStageExecutionRecord(
    executionId: string,
    stageName: string,
    result: StageExecutionResult
  ): Promise<void> {
    await this.prisma.pipelineStageExecution.updateMany({
      where: {
        executionId,
        stageName,
      },
      data: {
        status: result.status,
        completedAt: result.completedAt,
        duration: result.duration,
        inputData: result.inputData ? JSON.stringify(result.inputData) : undefined,
        outputData: result.outputData ? JSON.stringify(result.outputData) : undefined,
        errorCode: result.error?.code,
        errorMessage: result.error?.message,
        retryCount: result.retryCount,
        cost: result.cost,
        metrics: result.metrics ? JSON.stringify(result.metrics) : undefined,
      },
    });
  }
}
