/**
 * System User Authentication Middleware
 * Provides authentication for system users (separate from tenant users)
 */

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { JWTPayload } from '../utils/jwt';
// import { auditService } from '../services/audit-enhanced';

export interface SystemUser {
  id: string;
  email: string;
  role: SystemRole;
  permissions: string[];
  mfaEnabled: boolean;
  isActive: boolean;
}

export enum SystemRole {
  SUPER_ADMIN = 'super_admin',
  PLATFORM_ADMIN = 'platform_admin',
  FINANCE_ADMIN = 'finance_admin',
  SUPPORT_ADMIN = 'support_admin',
  ANALYTICS_VIEWER = 'analytics_viewer',
  BILLING_ADMIN = 'billing_admin'
}

// Role permissions mapping
const ROLE_PERMISSIONS: Record<SystemRole, string[]> = {
  [SystemRole.SUPER_ADMIN]: ['*'], // All permissions
  [SystemRole.PLATFORM_ADMIN]: [
    'tenant:read', 'tenant:write', 'tenant:manage',
    'analytics:read', 'bulk_operations:execute',
    'communication:send', 'system:monitor'
  ],
  [SystemRole.FINANCE_ADMIN]: [
    'tenant:read', 'billing:read', 'billing:write',
    'analytics:read', 'reports:generate'
  ],
  [SystemRole.SUPPORT_ADMIN]: [
    'tenant:read', 'tenant:support', 'communication:send',
    'tickets:manage', 'analytics:read'
  ],
  [SystemRole.ANALYTICS_VIEWER]: [
    'analytics:read', 'reports:read', 'tenant:read'
  ],
  [SystemRole.BILLING_ADMIN]: [
    'billing:read', 'billing:write', 'tenant:read',
    'analytics:read', 'reports:generate'
  ]
};

// Extend Express Request type
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      user?: SystemUser;
    }
  }
}

/**
 * System user authentication middleware
 */
export const systemUserAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        message: 'Authorization token required'
      });
      return;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    if (!token) {
      res.status(401).json({
        success: false,
        message: 'Authorization token required'
      });
      return;
    }

    const jwtSecret = process.env.SYSTEM_JWT_SECRET;
    if (!jwtSecret) {
      throw new Error('SYSTEM_JWT_SECRET environment variable is required');
    }
    if (jwtSecret.length < 32) {
      throw new Error('SYSTEM_JWT_SECRET must be at least 32 characters long');
    }
    
    try {
      const decoded = jwt.verify(token, jwtSecret) as {
        id: string;
        email: string;
        role: string;
        type: string;
        iat?: number;
        exp?: number;
      };
      
      // Validate token payload
      if (!decoded.id || !decoded.email || !decoded.role || !decoded.type || decoded.type !== 'system') {
        res.status(401).json({
          success: false,
          message: 'Invalid token format'
        });
        return;
      }

      // Get system user details (in real implementation, this would query database)
      const systemUser = await getSystemUser(decoded.id);
      
      if (!systemUser) {
        res.status(401).json({
          success: false,
          message: 'System user not found'
        });
        return;
      }

      if (!systemUser.isActive) {
        res.status(401).json({
          success: false,
          message: 'System user account is disabled'
        });
        return;
      }

      // Attach user to request (extend with required JWT fields)
      req.user = {
        ...systemUser,
        userId: systemUser.id, // Map id to userId for JWT compatibility
        sessionId: (decoded as { sessionId?: string }).sessionId || 'system-session'
      } as SystemUser & JWTPayload;

      // Log system user access (audit service temporarily disabled)
      console.log('System user access:', {
        userId: systemUser.id,
        role: systemUser.role,
        endpoint: req.path,
        method: req.method
      });

      next();

    } catch (jwtError) {
      if (jwtError instanceof jwt.TokenExpiredError) {
        res.status(401).json({
          success: false,
          message: 'Token expired'
        });
        return;
      }
      
      if (jwtError instanceof jwt.JsonWebTokenError) {
        res.status(401).json({
          success: false,
          message: 'Invalid token'
        });
        return;
      }
      
      throw jwtError;
    }

  } catch (error) {
    console.error('System authentication error:', error);
    res.status(500).json({
      success: false,
      message: 'Authentication error'
    });
  }
};

/**
 * Permission-based authorization middleware
 */
export const requirePermission = (permission: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
      return;
    }

    const userPermissions = (req.user as { permissions?: string[] }).permissions || [];
    const hasPermission = userPermissions.includes('*') || userPermissions.includes(permission);
    
    if (!hasPermission) {
      res.status(403).json({
        success: false,
        message: 'Insufficient permissions'
      });
      return;
    }

    next();
  };
};

/**
 * Role-based authorization middleware
 */
export const requireRole = (roles: SystemRole | SystemRole[]) => {
  const allowedRoles = Array.isArray(roles) ? roles : [roles];
  
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
      return;
    }

    const userRole = (req.user as { role: string }).role;
    if (!allowedRoles.includes(userRole as SystemRole)) {
      res.status(403).json({
        success: false,
        message: 'Insufficient role permissions'
      });
      return;
    }

    next();
  };
};

/**
 * Generate system user JWT token
 */
export const generateSystemToken = (user: SystemUser): string => {
  const jwtSecret = process.env.SYSTEM_JWT_SECRET || 'system-secret-key';
  const expiresIn = process.env.SYSTEM_JWT_EXPIRES || '8h';

  const payload = {
    id: user.id,
    email: user.email,
    role: user.role,
    type: 'system', // Distinguish from tenant user tokens
    permissions: user.permissions
  };
  
  return jwt.sign(payload, jwtSecret, { expiresIn: expiresIn || '1h' } as jwt.SignOptions);
};

/**
 * Get system user by ID (mock implementation)
 */
async function getSystemUser(userId: string): Promise<SystemUser | null> {
  // Mock system users - in real implementation, this would query the database
  const mockSystemUsers: Record<string, SystemUser> = {
    'system_user_1': {
      id: 'system_user_1',
      email: '<EMAIL>',
      role: SystemRole.SUPER_ADMIN,
      permissions: ROLE_PERMISSIONS[SystemRole.SUPER_ADMIN],
      mfaEnabled: true,
      isActive: true
    },
    'system_user_2': {
      id: 'system_user_2',
      email: '<EMAIL>',
      role: SystemRole.PLATFORM_ADMIN,
      permissions: ROLE_PERMISSIONS[SystemRole.PLATFORM_ADMIN],
      mfaEnabled: true,
      isActive: true
    },
    'system_user_3': {
      id: 'system_user_3',
      email: '<EMAIL>',
      role: SystemRole.FINANCE_ADMIN,
      permissions: ROLE_PERMISSIONS[SystemRole.FINANCE_ADMIN],
      mfaEnabled: true,
      isActive: true
    },
    'system_user_4': {
      id: 'system_user_4',
      email: '<EMAIL>',
      role: SystemRole.SUPPORT_ADMIN,
      permissions: ROLE_PERMISSIONS[SystemRole.SUPPORT_ADMIN],
      mfaEnabled: false,
      isActive: true
    },
    'system_user_5': {
      id: 'system_user_5',
      email: '<EMAIL>',
      role: SystemRole.ANALYTICS_VIEWER,
      permissions: ROLE_PERMISSIONS[SystemRole.ANALYTICS_VIEWER],
      mfaEnabled: false,
      isActive: true
    }
  };

  return mockSystemUsers[userId] || null;
}

/**
 * Create mock system token for testing
 */
export const createMockSystemToken = (role: SystemRole = SystemRole.PLATFORM_ADMIN): string => {
  const mockUser: SystemUser = {
    id: 'mock_system_user',
    email: '<EMAIL>',
    role,
    permissions: ROLE_PERMISSIONS[role],
    mfaEnabled: false,
    isActive: true
  };

  return generateSystemToken(mockUser);
};

export default systemUserAuth;