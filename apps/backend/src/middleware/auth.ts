import { Request, Response, NextFunction } from 'express';
import { JWTService, JWTPayload } from '../utils/jwt';
import { setUserContext, clearUserContext } from '../prisma/middleware';
import { prisma } from '../prisma/client';

// Extend Express Request type to include user
declare module 'express-serve-static-core' {
  interface Request {
    user?: JWTPayload;
  }
}

/**
 * Authentication middleware - validates JW<PERSON> token and sets user context
 */
export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = JWTService.extractTokenFromHeader(authHeader);

    if (!token) {
      clearUserContext();
      res.status(401).json({
        success: false,
        error: 'Access token required',
      });
      return;
    }

    const payload = JWTService.verifyAccessToken(token);
    if (!payload) {
      clearUserContext();
      res.status(401).json({
        success: false,
        error: 'Invalid or expired token',
      });
      return;
    }

    // Verify session exists and is valid using sessionId from JWT payload
    let session: { id: string; expiresAt: Date; user?: unknown } | null = null;

    if (payload.isSystemUser) {
      session = await prisma.systemUserSession.findUnique({
        where: { id: payload.sessionId },
        include: { user: true },
      });
    } else {
      session = await prisma.userSession.findUnique({
        where: { id: payload.sessionId },
        include: { user: true },
      });
    }

    if (!session || session.expiresAt < new Date()) {
      clearUserContext();
      res.status(401).json({
        success: false,
        error: 'Session expired or invalid',
      });
      return;
    }

    // Update last active time
    if (payload.isSystemUser) {
      await prisma.systemUserSession.update({
        where: { id: session.id },
        data: { lastActiveAt: new Date() },
      });
    } else {
      await prisma.userSession.update({
        where: { id: session.id },
        data: { lastActiveAt: new Date() },
      });
    }

    // Set user context for Prisma middleware
    setUserContext({
      userId: payload.userId,
      tenantId: payload.tenantId,
      role: payload.role,
      isSystemUser: payload.isSystemUser,
    });

    // Attach user to request
    req.user = payload;
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    clearUserContext();
    res.status(500).json({
      success: false,
      error: 'Authentication failed',
    });
  }
};

/**
 * Optional authentication middleware - doesn't fail if no token provided
 */
export const optionalAuthenticate = async (
  req: Request,
  _res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = JWTService.extractTokenFromHeader(authHeader);

    if (!token) {
      clearUserContext();
      return next();
    }

    const payload = JWTService.verifyAccessToken(token);
    if (!payload) {
      clearUserContext();
      return next();
    }

    // Verify session exists and is valid using sessionId from JWT payload
    let session: { id: string; expiresAt: Date } | null = null;

    if (payload.isSystemUser) {
      session = await prisma.systemUserSession.findUnique({
        where: { id: payload.sessionId },
      });
    } else {
      session = await prisma.userSession.findUnique({
        where: { id: payload.sessionId },
      });
    }

    if (!session || session.expiresAt < new Date()) {
      clearUserContext();
      return next();
    }

    // Set user context for Prisma middleware
    setUserContext({
      userId: payload.userId,
      tenantId: payload.tenantId,
      role: payload.role,
      isSystemUser: payload.isSystemUser,
    });

    // Attach user to request
    req.user = payload;
    next();
  } catch (error) {
    console.error('Optional authentication error:', error);
    clearUserContext();
    next();
  }
};

/**
 * Role-based authorization middleware
 */
export const authorize = (allowedRoles: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required',
      });
      return;
    }

    if (!allowedRoles.includes(req.user.role)) {
      res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
      });
      return;
    }

    next();
  };
};

/**
 * Tenant access control middleware
 */
export const requireTenantAccess = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user) {
    res.status(401).json({
      success: false,
      error: 'Authentication required',
    });
    return;
  }

  // System users and super admins can access any tenant
  if (req.user.isSystemUser || req.user.role === 'SUPER_ADMIN') {
    return next();
  }

  // Regular users must have a tenant
  if (!req.user.tenantId) {
    res.status(403).json({
      success: false,
      error: 'Tenant access required',
    });
    return;
  }

  // Check if user is trying to access their own tenant's resources
  const requestedTenantId =
    req.params.tenantId ||
    (req.body as Record<string, unknown>)?.tenantId ||
    req.query.tenantId;
    
  // Validate tenantId format to prevent injection attacks
  if (requestedTenantId) {
    if (typeof requestedTenantId !== 'string' || !/^[a-zA-Z0-9-_]+$/.test(requestedTenantId)) {
      res.status(400).json({
        success: false,
        error: 'Invalid tenant ID format',
      });
      return;
    }
    
    if (requestedTenantId !== req.user?.tenantId) {
      res.status(403).json({
        success: false,
        error: 'Access denied to requested tenant',
      });
      return;
    }
  }

  next();
};

/**
 * System admin authorization middleware
 */
export const requireSystemAdmin = authorize(['SUPER_ADMIN', 'PLATFORM_ADMIN']);

/**
 * Tenant admin authorization middleware
 */
export const requireTenantAdmin = authorize([
  'SUPER_ADMIN',
  'ADMIN',
  'MANAGER',
]);

/**
 * Platform admin authorization middleware (system users only)
 */
export const requirePlatformAdmin = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user) {
    res.status(401).json({
      success: false,
      error: 'Authentication required',
    });
    return;
  }

  if (!req.user.isSystemUser) {
    res.status(403).json({
      success: false,
      error: 'Platform admin access required',
    });
    return;
  }

  const allowedRoles = ['SUPER_ADMIN', 'PLATFORM_ADMIN', 'FINANCE_ADMIN'];
  if (!allowedRoles.includes(req.user.role)) {
    res.status(403).json({
      success: false,
      error: 'Insufficient platform permissions',
    });
    return;
  }

  next();
};
