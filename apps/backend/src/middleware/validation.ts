/**
 * Unified Input Validation Middleware
 * Enterprise-grade input sanitization and validation with security hardening
 */

import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { ErrorHandlingService, ErrorContext } from '../services/error-handling';

interface UploadedFile {
  size: number;
  originalname: string;
  mimetype: string;
}

// Security-focused validation schemas
export const ValidationSchemas = {
  // Common field validations
  id: Joi.string()
    .pattern(/^[a-zA-Z0-9-_]+$/)
    .min(1)
    .max(100)
    .required()
    .messages({
      'string.pattern.base': 'ID must contain only alphanumeric characters, hyphens, and underscores',
      'string.min': 'ID cannot be empty',
      'string.max': 'ID is too long (max 100 characters)',
    }),

  email: Joi.string()
    .email({ tlds: { allow: false } })
    .max(254)
    .lowercase()
    .trim()
    .required()
    .messages({
      'string.email': 'Must be a valid email address',
      'string.max': 'Email is too long (max 254 characters)',
    }),

  password: Joi.string()
    .min(8)
    .max(128)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]).{8,}$/)
    .required()
    .messages({
      'string.min': 'Password must be at least 8 characters long',
      'string.max': 'Password must not exceed 128 characters',
      'string.pattern.base': 'Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character',
    }),

  name: Joi.string()
    .trim()
    .min(1)
    .max(100)
    .pattern(/^[a-zA-Z\s'-]+$/)
    .required()
    .messages({
      'string.min': 'Name cannot be empty',
      'string.max': 'Name is too long (max 100 characters)',
      'string.pattern.base': 'Name can only contain letters, spaces, hyphens, and apostrophes',
    }),

  tenantId: Joi.string()
    .pattern(/^[a-zA-Z0-9-_]+$/)
    .min(1)
    .max(50)
    .required()
    .messages({
      'string.pattern.base': 'Tenant ID must contain only alphanumeric characters, hyphens, and underscores',
      'string.min': 'Tenant ID cannot be empty',
      'string.max': 'Tenant ID is too long (max 50 characters)',
    }),

  phoneNumber: Joi.string()
    .pattern(/^\+?[1-9]\d{1,14}$/)
    .optional()
    .messages({
      'string.pattern.base': 'Phone number must be a valid international format',
    }),

  url: Joi.string()
    .uri({ scheme: ['http', 'https'] })
    .max(2048)
    .optional()
    .messages({
      'string.uri': 'Must be a valid HTTP/HTTPS URL',
      'string.max': 'URL is too long (max 2048 characters)',
    }),

  // Pagination and query parameters
  page: Joi.number()
    .integer()
    .min(1)
    .max(1000)
    .default(1)
    .messages({
      'number.min': 'Page must be at least 1',
      'number.max': 'Page cannot exceed 1000',
    }),

  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100',
    }),

  sortBy: Joi.string()
    .pattern(/^[a-zA-Z][a-zA-Z0-9_]*$/)
    .max(50)
    .optional()
    .messages({
      'string.pattern.base': 'Sort field must be a valid field name',
      'string.max': 'Sort field name is too long',
    }),

  sortOrder: Joi.string()
    .valid('asc', 'desc')
    .default('asc')
    .messages({
      'any.only': 'Sort order must be either "asc" or "desc"',
    }),

  // Date validations
  dateString: Joi.string()
    .isoDate()
    .optional()
    .messages({
      'string.isoDate': 'Date must be in ISO 8601 format (YYYY-MM-DDTHH:mm:ss.sssZ)',
    }),

  // Text content with XSS protection
  safeText: Joi.string()
    .trim()
    .max(1000)
    .pattern(/^[^<>]*$/)  // No HTML tags allowed
    .optional()
    .messages({
      'string.max': 'Text is too long (max 1000 characters)',
      'string.pattern.base': 'HTML tags are not allowed',
    }),

  longText: Joi.string()
    .trim()
    .max(10000)
    .pattern(/^[^<>]*$/)  // No HTML tags allowed
    .optional()
    .messages({
      'string.max': 'Text is too long (max 10000 characters)',
      'string.pattern.base': 'HTML tags are not allowed',
    }),

  // Compound schemas for endpoints - formatted for validate() function
  login: {
    body: Joi.object({
      email: Joi.string().email().required(),
      password: Joi.string().required(),
    })
  },

  refreshToken: {
    body: Joi.object({
      refreshToken: Joi.string().required(),
    })
  },

  changePassword: {
    body: Joi.object({
      currentPassword: Joi.string().required(),
      newPassword: Joi.string().min(8).required(),
    })
  },

  createSystemUser: {
    body: Joi.object({
      email: Joi.string().email().required(),
      name: Joi.string().min(1).max(100).required(),
      role: Joi.string().valid('ADMIN', 'MANAGER').required(),
    })
  },

  updateSystemUser: {
    body: Joi.object({
      name: Joi.string().min(1).max(100).optional(),
      role: Joi.string().valid('ADMIN', 'MANAGER').optional(),
      isActive: Joi.boolean().optional(),
    })
  },

  userIdParam: {
    params: Joi.object({
      userId: Joi.string().pattern(/^[a-zA-Z0-9-_]+$/).required(),
    })
  },

  pagination: {
    query: Joi.object({
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(10),
      sortBy: Joi.string().optional(),
      sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
      search: Joi.string().optional(),
    })
  },

  userRegistration: {
    body: Joi.object({
      email: Joi.string().email().required(),
      password: Joi.string().min(8).required(),
      name: Joi.string().min(1).max(100).required(),
    })
  },

  updateProfile: {
    body: Joi.object({
      name: Joi.string().min(1).max(100).optional(),
      email: Joi.string().email().optional(),
    })
  },

  updateUserRole: {
    body: Joi.object({
      role: Joi.string().valid('ADMIN', 'MANAGER', 'USER').required(),
    })
  },
};

/**
 * Create validation middleware for request bodies, params, or query
 */
export const validateRequest = (schema: {
  body?: Joi.ObjectSchema;
  params?: Joi.ObjectSchema;
  query?: Joi.ObjectSchema;
}) => {
  return (req: Request, _res: Response, next: NextFunction): void => {
    const context: ErrorContext = ErrorHandlingService.extractRequestContext(req);
    const validationErrors: string[] = [];

    // Validate request body
    if (schema.body) {
      const { error } = schema.body.validate(req.body, {
        abortEarly: false,
        stripUnknown: true,
        convert: true,
      });

      if (error) {
        validationErrors.push(...error.details.map(detail => `Body: ${detail.message}`));
      }
    }

    // Validate path parameters
    if (schema.params) {
      const { error } = schema.params.validate(req.params, {
        abortEarly: false,
        stripUnknown: true,
        convert: true,
      });

      if (error) {
        validationErrors.push(...error.details.map(detail => `Params: ${detail.message}`));
      }
    }

    // Validate query parameters
    if (schema.query) {
      const { error } = schema.query.validate(req.query, {
        abortEarly: false,
        stripUnknown: true,
        convert: true,
      });

      if (error) {
        validationErrors.push(...error.details.map(detail => `Query: ${detail.message}`));
      }
    }

    if (validationErrors.length > 0) {
      const appError = ErrorHandlingService.createValidationError(
        `Validation failed: ${validationErrors.join(', ')}`,
        'Invalid input provided',
        {
          ...context,
          additionalData: {
            validationErrors,
            requestBody: req.body ? Object.keys(req.body) : [],
            requestParams: req.params ? Object.keys(req.params) : [],
            requestQuery: req.query ? Object.keys(req.query) : [],
          },
        }
      );

      return next(appError);
    }

    next();
  };
};

// Alias for backward compatibility
export const validate = validateRequest;

/**
 * Sanitize HTML content to prevent XSS attacks
 */
export const sanitizeHtml = (content: string): string => {
  return content
    .replace(/[<>]/g, '')  // Remove HTML tags
    .replace(/javascript:/gi, '')  // Remove javascript: URLs
    .replace(/on\w+=/gi, '')  // Remove event handlers
    .trim();
};

/**
 * Rate limiting validation (requests per minute)
 */
export const validateRateLimit = (maxRequests: number = 60, _windowMs: number = 15 * 60 * 1000) => {
  const requestCounts = new Map<string, { count: number; resetTime: number }>();

  return (req: Request, res: Response, next: NextFunction): void => {
    const clientId = req.ip || 'unknown';
    const now = Date.now();
    const windowMs = 60 * 1000; // 1 minute window

    let clientData = requestCounts.get(clientId);

    // Reset count if window has passed
    if (!clientData || now > clientData.resetTime) {
      clientData = { count: 0, resetTime: now + windowMs };
      requestCounts.set(clientId, clientData);
    }

    clientData.count++;

    if (clientData.count > maxRequests) {
      const context: ErrorContext = ErrorHandlingService.extractRequestContext(req);
      const error = ErrorHandlingService.createRateLimitError(
        'Too many requests, please try again later',
        {
          ...context,
          additionalData: {
            requestCount: clientData.count,
            maxRequests,
            resetTime: new Date(clientData.resetTime),
          },
        }
      );

      return next(error);
    }

    // Add rate limit headers
    res.set({
      'X-RateLimit-Limit': maxRequests.toString(),
      'X-RateLimit-Remaining': Math.max(0, maxRequests - clientData.count).toString(),
      'X-RateLimit-Reset': new Date(clientData.resetTime).toISOString(),
    });

    next();
  };
};

/**
 * File upload validation
 */
export const validateFileUpload = (options: {
  maxSize?: number;
  allowedMimeTypes?: string[];
  maxFiles?: number;
}) => {
  const {
    maxSize = 5 * 1024 * 1024, // 5MB default
    allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
    maxFiles = 1,
  } = options;

  return (req: Request, _res: Response, next: NextFunction): void => {
    const files = req.files as UploadedFile[] | { [fieldname: string]: UploadedFile[] } | undefined;
    const context: ErrorContext = ErrorHandlingService.extractRequestContext(req);

    if (!files) {
      return next();
    }

    let fileArray: UploadedFile[] = [];
    
    if (Array.isArray(files)) {
      fileArray = files;
    } else {
      fileArray = Object.values(files).flat();
    }

    // Check file count
    if (fileArray.length > maxFiles) {
      const error = ErrorHandlingService.createValidationError(
        `Too many files uploaded. Maximum allowed: ${maxFiles}`,
        `Maximum ${maxFiles} files allowed`,
        {
          ...context,
          additionalData: { fileCount: fileArray.length, maxFiles },
        }
      );
      return next(error);
    }

    // Validate each file
    for (const file of fileArray) {
      // Check file size
      if (file.size > maxSize) {
        const error = ErrorHandlingService.createValidationError(
          `File ${file.originalname} exceeds maximum size of ${maxSize} bytes`,
          'File is too large',
          {
            ...context,
            additionalData: { fileName: file.originalname, fileSize: file.size, maxSize },
          }
        );
        return next(error);
      }

      // Check MIME type
      if (!allowedMimeTypes.includes(file.mimetype)) {
        const error = ErrorHandlingService.createValidationError(
          `File ${file.originalname} has unsupported type: ${file.mimetype}`,
          'Unsupported file type',
          {
            ...context,
            additionalData: { 
              fileName: file.originalname, 
              mimeType: file.mimetype, 
              allowedTypes: allowedMimeTypes 
            },
          }
        );
        return next(error);
      }
    }

    next();
  };
};

/**
 * Input sanitization middleware for all requests
 */
export const sanitizeInput = (req: Request, _res: Response, next: NextFunction): void => {
  // Basic input sanitization - strip null bytes and normalize strings
  const sanitizeObject = (obj: unknown): unknown => {
    if (obj === null || obj === undefined) return obj;
    if (typeof obj === 'string') {
      return obj.replace(/\0/g, '').trim();
    }
    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject);
    }
    if (typeof obj === 'object') {
      const sanitized: Record<string, unknown> = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = sanitizeObject(value);
      }
      return sanitized;
    }
    return obj;
  };

  if (req.body) {
    req.body = sanitizeObject(req.body) as Record<string, unknown>;
  }
  if (req.query) {
    req.query = sanitizeObject(req.query) as typeof req.query;
  }
  if (req.params) {
    req.params = sanitizeObject(req.params) as typeof req.params;
  }

  next();
};

/**
 * Common validation schemas for specific endpoints
 */
export const CommonValidationSchemas = {
  // Authentication
  login: {
    body: Joi.object({
      email: ValidationSchemas.email,
      password: Joi.string().required(),
    }),
  },

  register: {
    body: Joi.object({
      email: ValidationSchemas.email,
      password: ValidationSchemas.password,
      name: ValidationSchemas.name,
    }),
  },

  // Tenant management
  createTenant: {
    body: Joi.object({
      name: ValidationSchemas.name,
      description: ValidationSchemas.safeText,
    }),
  },

  getTenant: {
    params: Joi.object({
      tenantId: ValidationSchemas.tenantId,
    }),
  },

  // Pagination
  pagination: {
    query: Joi.object({
      page: ValidationSchemas.page,
      limit: ValidationSchemas.limit,
      sortBy: ValidationSchemas.sortBy,
      sortOrder: ValidationSchemas.sortOrder,
      search: ValidationSchemas.safeText,
    }),
  },

  // Generic ID parameter
  idParam: {
    params: Joi.object({
      id: ValidationSchemas.id,
    }),
  },
};