/**
 * Platform Analytics Controller
 * Handles revenue analytics and business intelligence endpoints for system users
 * Implements Story 9.3: Platform Revenue & Analytics Dashboard
 */

import { NextFunction, Request, Response } from 'express';
import { PlatformAnalyticsService } from '../services/platform-analytics';
import { AuditLogger } from '../utils/audit';
import { JWTPayload } from '../utils/jwt';

export interface SystemAuthRequest extends Request {
  user?: JWTPayload & {
    isSystemUser: boolean;
  };
}

export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export class PlatformAnalyticsController {
  private static analyticsService = new PlatformAnalyticsService();

  /**
   * GET /api/platform/analytics/revenue
   * Get comprehensive revenue analytics
   */
  static async getRevenueAnalytics(
    req: SystemAuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      // Verify system user access
      if (!req.user?.isSystemUser || !['SUPER_ADMIN', 'PLATFORM_ADMIN', 'FINANCE_ADMIN'].includes(req.user.role)) {
        const response: ApiResponse = {
          success: false,
          error: 'Platform admin access required',
        };
        res.status(403).json(response);
        return;
      }

      const { startDate, endDate } = req.query;
      
      let dateRange: { start: Date; end: Date } | undefined;
      if (startDate && endDate) {
        dateRange = {
          start: new Date(startDate as string),
          end: new Date(endDate as string),
        };
        
        // Validate date range
        if (isNaN(dateRange.start.getTime()) || isNaN(dateRange.end.getTime())) {
          const response: ApiResponse = {
            success: false,
            error: 'Invalid date format. Use ISO 8601 format (YYYY-MM-DD)',
          };
          res.status(400).json(response);
          return;
        }
        
        if (dateRange.start >= dateRange.end) {
          const response: ApiResponse = {
            success: false,
            error: 'Start date must be before end date',
          };
          res.status(400).json(response);
          return;
        }
      }

      const analytics = await this.analyticsService.getRevenueAnalytics(dateRange);

      // Log access for audit
      await AuditLogger.logSystem({
        userId: req.user.userId,
        action: 'ANALYTICS_REVENUE_VIEW',
        entityType: 'Analytics',
        entityId: 'platform_analytics',
        metadata: { dateRange },
        ipAddress: req.ip || '',
        userAgent: req.get('User-Agent') || '',
      });

      const response: ApiResponse = {
        success: true,
        data: analytics,
      };
      res.json(response);

    } catch (error) {
      next(error);
    }
  }

  /**
   * GET /api/platform/analytics/metrics
   * Get platform-wide metrics
   */
  static async getPlatformMetrics(
    req: SystemAuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      // Verify system user access
      if (!req.user?.isSystemUser || !['SUPER_ADMIN', 'PLATFORM_ADMIN', 'FINANCE_ADMIN', 'ANALYTICS_VIEWER'].includes(req.user.role)) {
        const response: ApiResponse = {
          success: false,
          error: 'Platform access required',
        };
        res.status(403).json(response);
        return;
      }

      const { startDate, endDate } = req.query;
      
      let dateRange: { start: Date; end: Date } | undefined;
      if (startDate && endDate) {
        dateRange = {
          start: new Date(startDate as string),
          end: new Date(endDate as string),
        };
      }

      const metrics = await this.analyticsService.getPlatformMetrics(dateRange);

      // Log access for audit
      await AuditLogger.logSystem({
        userId: req.user.userId,
        action: 'ANALYTICS_METRICS_VIEW',
        entityType: 'Analytics',
        entityId: 'platform_metrics',
        metadata: { dateRange },
        ipAddress: req.ip || '',
        userAgent: req.get('User-Agent') || '',
      });

      const response: ApiResponse = {
        success: true,
        data: metrics,
      };
      res.json(response);

    } catch (error) {
      next(error);
    }
  }

  /**
   * GET /api/platform/analytics/churn-prediction
   * Get churn risk predictions for tenants
   */
  static async getChurnPrediction(
    req: SystemAuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      // Verify system user access
      if (!req.user?.isSystemUser || !['SUPER_ADMIN', 'PLATFORM_ADMIN', 'ANALYTICS_VIEWER'].includes(req.user.role)) {
        const response: ApiResponse = {
          success: false,
          error: 'Platform access required',
        };
        res.status(403).json(response);
        return;
      }

      const { tenantIds, limit = '50' } = req.query;
      
      let targetTenantIds: string[] | undefined;
      if (tenantIds) {
        targetTenantIds = Array.isArray(tenantIds) ? tenantIds as string[] : [tenantIds as string];
      }

      const predictions = await this.analyticsService.predictChurnRisk(targetTenantIds);
      
      // Apply limit
      const limitedPredictions = predictions.slice(0, parseInt(limit as string, 10));

      // Log access for audit
      await AuditLogger.logSystem({
        userId: req.user.userId,
        action: 'ANALYTICS_CHURN_PREDICTION_VIEW',
        entityType: 'Analytics',
        entityId: 'churn_prediction',
        metadata: { tenantIds: targetTenantIds, limit },
        ipAddress: req.ip || '',
        userAgent: req.get('User-Agent') || '',
      });

      const response: ApiResponse = {
        success: true,
        data: {
          predictions: limitedPredictions,
          totalCount: predictions.length,
          highRiskCount: predictions.filter(p => p.riskScore > 70).length,
          mediumRiskCount: predictions.filter(p => p.riskScore > 40 && p.riskScore <= 70).length,
          lowRiskCount: predictions.filter(p => p.riskScore <= 40).length,
        },
      };
      res.json(response);

    } catch (error) {
      next(error);
    }
  }

  /**
   * GET /api/platform/analytics/segmentation
   * Get tenant segmentation analysis
   */
  static async getTenantSegmentation(
    req: SystemAuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      // Verify system user access
      if (!req.user?.isSystemUser || !['SUPER_ADMIN', 'PLATFORM_ADMIN', 'ANALYTICS_VIEWER'].includes(req.user.role)) {
        const response: ApiResponse = {
          success: false,
          error: 'Platform access required',
        };
        res.status(403).json(response);
        return;
      }

      const segmentation = await this.analyticsService.getTenantSegmentation();

      // Log access for audit
      await AuditLogger.logSystem({
        userId: req.user.userId,
        action: 'ANALYTICS_SEGMENTATION_VIEW',
        entityType: 'Analytics',
        entityId: 'tenant_segmentation',
        metadata: {},
        ipAddress: req.ip || '',
        userAgent: req.get('User-Agent') || '',
      });

      const response: ApiResponse = {
        success: true,
        data: {
          segments: segmentation,
          totalSegments: segmentation.length,
          summary: {
            totalTenants: segmentation.reduce((sum, s) => sum + s.tenantCount, 0),
            totalRevenue: segmentation.reduce((sum, s) => sum + s.totalRevenue, 0),
            averageChurnRate: segmentation.reduce((sum, s) => sum + s.churnRate, 0) / segmentation.length,
          },
        },
      };
      res.json(response);

    } catch (error) {
      next(error);
    }
  }

  /**
   * POST /api/platform/analytics/executive-report
   * Generate executive report
   */
  static async generateExecutiveReport(
    req: SystemAuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      // Verify system user access (only high-level roles)
      if (!req.user?.isSystemUser || !['SUPER_ADMIN', 'PLATFORM_ADMIN'].includes(req.user.role)) {
        const response: ApiResponse = {
          success: false,
          error: 'Platform admin access required',
        };
        res.status(403).json(response);
        return;
      }

      const { 
        startDate, 
        endDate, 
        includeForecasts = true, 
        includeSegmentation = true, 
        includeRecommendations = true 
      } = req.body;

      // Validate required fields
      if (!startDate || !endDate) {
        const response: ApiResponse = {
          success: false,
          error: 'Start date and end date are required',
        };
        res.status(400).json(response);
        return;
      }

      const period = {
        start: new Date(startDate),
        end: new Date(endDate),
      };

      // Validate dates
      if (isNaN(period.start.getTime()) || isNaN(period.end.getTime())) {
        const response: ApiResponse = {
          success: false,
          error: 'Invalid date format',
        };
        res.status(400).json(response);
        return;
      }

      const options = {
        includeForecasts: Boolean(includeForecasts),
        includeSegmentation: Boolean(includeSegmentation),
        includeRecommendations: Boolean(includeRecommendations),
      };

      const report = await this.analyticsService.generateExecutiveReport(period, options);

      // Log report generation for audit
      await AuditLogger.logSystem({
        userId: req.user.userId,
        action: 'ANALYTICS_EXECUTIVE_REPORT_GENERATED',
        entityType: 'Analytics',
        entityId: 'executive_report',
        metadata: { period, options, reportId: report.id },
        ipAddress: req.ip || '',
        userAgent: req.get('User-Agent') || '',
      });

      const response: ApiResponse = {
        success: true,
        data: report,
      };
      res.json(response);

    } catch (error) {
      next(error);
    }
  }

  /**
   * GET /api/platform/analytics/export
   * Export analytics data in various formats
   */
  static async exportAnalyticsData(
    req: SystemAuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      // Verify system user access
      if (!req.user?.isSystemUser || !['SUPER_ADMIN', 'PLATFORM_ADMIN', 'FINANCE_ADMIN'].includes(req.user.role)) {
        const response: ApiResponse = {
          success: false,
          error: 'Platform admin access required',
        };
        res.status(403).json(response);
        return;
      }

      const {
        type = 'revenue',
        format = 'json',
        startDate,
        endDate,
        filename
      } = req.query;

      // Validate format
      if (!['json', 'csv', 'pdf'].includes(format as string)) {
        const response: ApiResponse = {
          success: false,
          error: 'Invalid format. Supported formats: json, csv, pdf',
        };
        res.status(400).json(response);
        return;
      }

      let data: unknown;
      let defaultFilename: string;

      // Get data based on type
      switch (type) {
        case 'revenue': {
          const dateRange = startDate && endDate ? {
            start: new Date(startDate as string),
            end: new Date(endDate as string),
          } : undefined;
          data = await this.analyticsService.getRevenueAnalytics(dateRange);
          defaultFilename = `revenue-analytics-${new Date().toISOString().split('T')[0]}`;
          break;
        }

        case 'metrics': {
          data = await this.analyticsService.getPlatformMetrics();
          defaultFilename = `platform-metrics-${new Date().toISOString().split('T')[0]}`;
          break;
        }

        case 'segmentation': {
          data = await this.analyticsService.getTenantSegmentation();
          defaultFilename = `tenant-segmentation-${new Date().toISOString().split('T')[0]}`;
          break;
        }

        case 'churn': {
          data = await this.analyticsService.predictChurnRisk();
          defaultFilename = `churn-prediction-${new Date().toISOString().split('T')[0]}`;
          break;
        }

        default: {
          const response: ApiResponse = {
            success: false,
            error: 'Invalid type. Supported types: revenue, metrics, segmentation, churn',
          };
          res.status(400).json(response);
          return;
        }
      }

      const exportFilename = (filename as string) || defaultFilename;
      const exportedData = await this.analyticsService.exportAnalyticsData(
        format as 'json' | 'csv' | 'pdf',
        data,
        exportFilename
      );

      // Log export for audit
      await AuditLogger.logSystem({
        userId: req.user.userId,
        action: 'ANALYTICS_DATA_EXPORTED',
        entityType: 'Analytics',
        entityId: 'analytics_export',
        metadata: { type, format, filename: exportFilename },
        ipAddress: req.ip || '',
        userAgent: req.get('User-Agent') || '',
      });

      // Set appropriate headers based on format
      switch (format) {
        case 'json':
          res.setHeader('Content-Type', 'application/json');
          res.setHeader('Content-Disposition', `attachment; filename="${exportFilename}.json"`);
          res.send(exportedData);
          break;

        case 'csv':
          res.setHeader('Content-Type', 'text/csv');
          res.setHeader('Content-Disposition', `attachment; filename="${exportFilename}.csv"`);
          res.send(exportedData);
          break;

        case 'pdf':
          res.setHeader('Content-Type', 'application/pdf');
          res.setHeader('Content-Disposition', `attachment; filename="${exportFilename}.pdf"`);
          res.send(exportedData);
          break;
      }

    } catch (error) {
      next(error);
    }
  }

  /**
   * GET /api/platform/analytics/dashboard
   * Get dashboard summary data
   */
  static async getDashboardSummary(
    req: SystemAuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      // Verify system user access
      if (!req.user?.isSystemUser || !['SUPER_ADMIN', 'PLATFORM_ADMIN', 'FINANCE_ADMIN', 'ANALYTICS_VIEWER'].includes(req.user.role)) {
        const response: ApiResponse = {
          success: false,
          error: 'Platform access required',
        };
        res.status(403).json(response);
        return;
      }

      // Get current month data
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

      const currentPeriod = { start: startOfMonth, end: endOfMonth };

      // Get previous month for comparison
      const prevMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const prevMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
      const previousPeriod = { start: prevMonth, end: prevMonthEnd };

      // Fetch all required data in parallel
      const [
        currentRevenue,
        previousRevenue,
        currentMetrics,
        previousMetrics,
        churnPredictions,
        segmentation
      ] = await Promise.all([
        this.analyticsService.getRevenueAnalytics(currentPeriod),
        this.analyticsService.getRevenueAnalytics(previousPeriod),
        this.analyticsService.getPlatformMetrics(currentPeriod),
        this.analyticsService.getPlatformMetrics(previousPeriod),
        this.analyticsService.predictChurnRisk(),
        this.analyticsService.getTenantSegmentation()
      ]);

      // Calculate month-over-month changes
      const revenueChange = previousRevenue.mrr > 0
        ? ((currentRevenue.mrr - previousRevenue.mrr) / previousRevenue.mrr) * 100
        : 0;

      const tenantChange = previousMetrics.totalTenants > 0
        ? ((currentMetrics.totalTenants - previousMetrics.totalTenants) / previousMetrics.totalTenants) * 100
        : 0;

      // Get high-risk tenants
      const highRiskTenants = churnPredictions.filter(p => p.riskScore > 70);

      // Create dashboard summary
      const dashboardData = {
        overview: {
          mrr: currentRevenue.mrr,
          arr: currentRevenue.arr,
          totalTenants: currentMetrics.totalTenants,
          activeTenants: currentMetrics.activeTenants,
          churnRate: currentRevenue.churnRate,
          growthRate: currentRevenue.growthRate,
        },
        changes: {
          revenueChange: Math.round(revenueChange * 100) / 100,
          tenantChange: Math.round(tenantChange * 100) / 100,
          churnChange: Math.round((currentRevenue.churnRate - previousRevenue.churnRate) * 100) / 100,
        },
        alerts: {
          highRiskTenants: highRiskTenants.length,
          highChurnRate: currentRevenue.churnRate > 5,
          negativeGrowth: currentRevenue.growthRate < 0,
          lowLtvCacRatio: (currentRevenue.ltv / currentRevenue.cac) < 3,
        },
        topSegments: segmentation
          .sort((a, b) => b.totalRevenue - a.totalRevenue)
          .slice(0, 3)
          .map(s => ({
            segment: s.segment,
            revenue: s.totalRevenue,
            tenants: s.tenantCount,
            churnRate: s.churnRate,
          })),
        recentTrends: {
          cohorts: currentRevenue.cohortData.slice(-6), // Last 6 months
          forecasts: currentRevenue.forecasts.filter(f => f.scenario === 'realistic').slice(0, 6), // Next 6 months
        },
      };

      // Log dashboard access for audit
      await AuditLogger.logSystem({
        userId: req.user.userId,
        action: 'ANALYTICS_DASHBOARD_VIEW',
        entityType: 'Analytics',
        entityId: 'analytics_dashboard',
        metadata: { period: currentPeriod },
        ipAddress: req.ip || '',
        userAgent: req.get('User-Agent') || '',
      });

      const response: ApiResponse = {
        success: true,
        data: dashboardData,
      };
      res.json(response);

    } catch (error) {
      next(error);
    }
  }
}
