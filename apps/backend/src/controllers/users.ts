import { Request, Response } from 'express';
import { UserRole } from '@prisma/client';
import { AuthService, CreateUserData } from '../services/auth';
import { AuditLogger } from '../utils/audit';
import { ApiResponse } from '@underwriting/shared';
import { prisma } from '../prisma/client';

import { JWTPayload } from '../utils/jwt';

export interface AuthRequest extends Request {
  user?: JWTPayload;
}

export class UsersController {
  /**
   * POST /api/users/register
   * Register new tenant user
   */
  static async register(req: Request, res: Response): Promise<void> {
    try {
      const { email, password, name, tenantId, role }: CreateUserData =
        req.body as CreateUserData;

      // Input validation
      if (!email || !tenantId) {
        const response: ApiResponse = {
          success: false,
          error: 'Email and tenant ID are required',
        };
        res.status(400).json(response);
        return;
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        const response: ApiResponse = {
          success: false,
          error: 'Invalid email format',
        };
        res.status(400).json(response);
        return;
      }

      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        const response: ApiResponse = {
          success: false,
          error: 'User with this email already exists',
        };
        res.status(409).json(response);
        return;
      }

      // Verify tenant exists and is active
      const tenant = await prisma.tenant.findUnique({
        where: { id: tenantId },
      });

      if (!tenant || tenant.status !== 'ACTIVE') {
        const response: ApiResponse = {
          success: false,
          error: 'Invalid or inactive tenant',
        };
        res.status(400).json(response);
        return;
      }

      // Create user
      const userData: CreateUserData = {
        email,
        password,
        name,
        tenantId,
        role: role || 'USER',
      };

      const user = await AuthService.createUser(userData);

      // Log user creation
      await AuditLogger.logUserManagement(
        'USER_CREATED',
        user.id,
        'system', // Use system for self-registration
        req.ip || '0.0.0.0',
        req.get('User-Agent') || 'unknown',
        tenantId,
        undefined,
        {
          email: user.email,
          name: user.name,
          role: user.role,
        }
      );

      const response: ApiResponse = {
        success: true,
        data: {
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role,
            tenantId: user.tenantId,
            emailVerified: user.emailVerified,
            createdAt: user.createdAt,
          },
        },
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('User registration error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'User registration failed',
      };
      res.status(500).json(response);
    }
  }

  /**
   * GET /api/users/profile
   * Get user profile with detailed information
   */
  static async getProfile(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      if (!user || user.isSystemUser === true) {
        const response: ApiResponse = {
          success: false,
          error: 'User not authenticated or invalid user type',
        };
        res.status(401).json(response);
        return;
      }

      // Get detailed user information
      const userDetails = await prisma.user.findUnique({
        where: { id: user.userId },
        include: {
          tenant: {
            select: {
              id: true,
              name: true,
              status: true,
              subscriptionTier: true,
            },
          },
        },
      });

      if (!userDetails) {
        const response: ApiResponse = {
          success: false,
          error: 'User not found',
        };
        res.status(404).json(response);
        return;
      }

      const response: ApiResponse = {
        success: true,
        data: {
          user: {
            id: userDetails.id,
            email: userDetails.email,
            name: userDetails.name,
            role: userDetails.role,
            tenantId: userDetails.tenantId,
            emailVerified: userDetails.emailVerified,
            avatar: userDetails.avatar,
            timezone: userDetails.timezone,
            locale: userDetails.locale,
            lastLoginAt: userDetails.lastLoginAt,
            createdAt: userDetails.createdAt,
            updatedAt: userDetails.updatedAt,
            tenant: userDetails.tenant,
          },
        },
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('Get user profile error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to get user profile',
      };
      res.status(500).json(response);
    }
  }

  /**
   * PUT /api/users/profile
   * Update user profile
   */
  static async updateProfile(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      if (!user || user.isSystemUser === true) {
        const response: ApiResponse = {
          success: false,
          error: 'User not authenticated or invalid user type',
        };
        res.status(401).json(response);
        return;
      }

      const { name, avatar, timezone, locale } = req.body as {
        name?: string;
        avatar?: string;
        timezone?: string;
        locale?: string;
      };

      // Get current user data for audit logging
      const currentUser = await prisma.user.findUnique({
        where: { id: user.userId },
      });

      if (!currentUser) {
        const response: ApiResponse = {
          success: false,
          error: 'User not found',
        };
        res.status(404).json(response);
        return;
      }

      // Update user profile
      const updatedUser = await prisma.user.update({
        where: { id: user.userId },
        data: {
          ...(name !== undefined && { name }),
          ...(avatar !== undefined && { avatar }),
          ...(timezone !== undefined && { timezone }),
          ...(locale !== undefined && { locale }),
          updatedAt: new Date(),
        },
      });

      // Log profile update
      await AuditLogger.logUserManagement(
        'USER_UPDATED',
        user.userId,
        user.userId,
        req.ip || '0.0.0.0',
        req.get('User-Agent') || 'unknown',
        user.tenantId,
        {
          name: currentUser.name,
          avatar: currentUser.avatar,
          timezone: currentUser.timezone,
          locale: currentUser.locale,
        },
        {
          name: updatedUser.name,
          avatar: updatedUser.avatar,
          timezone: updatedUser.timezone,
          locale: updatedUser.locale,
        }
      );

      const response: ApiResponse = {
        success: true,
        data: {
          user: {
            id: updatedUser.id,
            email: updatedUser.email,
            name: updatedUser.name,
            role: updatedUser.role,
            tenantId: updatedUser.tenantId,
            emailVerified: updatedUser.emailVerified,
            avatar: updatedUser.avatar,
            timezone: updatedUser.timezone,
            locale: updatedUser.locale,
            updatedAt: updatedUser.updatedAt,
          },
        },
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('Update user profile error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to update user profile',
      };
      res.status(500).json(response);
    }
  }

  /**
   * GET /api/users
   * List users in tenant (admin only)
   */
  static async listUsers(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      if (!user || user.isSystemUser === true) {
        const response: ApiResponse = {
          success: false,
          error: 'User not authenticated or invalid user type',
        };
        res.status(401).json(response);
        return;
      }

      // Check if user has admin permissions
      if (!['ADMIN', 'OWNER'].includes(user.role)) {
        const response: ApiResponse = {
          success: false,
          error: 'Insufficient permissions',
        };
        res.status(403).json(response);
        return;
      }

      const { page = 1, limit = 20, search, role } = req.query;
      const offset = (Number(page) - 1) * Number(limit);

      // Build search filters
      const where: Record<string, unknown> = {
        tenantId: user.tenantId,
      };

      if (search) {
        where.OR = [
          { email: { contains: search as string, mode: 'insensitive' } },
          { name: { contains: search as string, mode: 'insensitive' } },
        ];
      }

      if (role && role !== 'all') {
        where.role = role as UserRole;
      }

      // Get users with pagination
      const [users, totalCount] = await Promise.all([
        prisma.user.findMany({
          where,
          select: {
            id: true,
            email: true,
            name: true,
            role: true,
            emailVerified: true,
            avatar: true,
            lastLoginAt: true,
            createdAt: true,
            updatedAt: true,
          },
          orderBy: { createdAt: 'desc' },
          take: Number(limit),
          skip: offset,
        }),
        prisma.user.count({ where }),
      ]);

      const response: ApiResponse = {
        success: true,
        data: {
          users,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total: totalCount,
            pages: Math.ceil(totalCount / Number(limit)),
          },
        },
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('List users error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to list users',
      };
      res.status(500).json(response);
    }
  }

  /**
   * PUT /api/users/:userId/role
   * Update user role (admin only)
   */
  static async updateUserRole(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      if (!user || user.isSystemUser === true) {
        const response: ApiResponse = {
          success: false,
          error: 'User not authenticated or invalid user type',
        };
        res.status(401).json(response);
        return;
      }

      // Check if user has admin permissions
      if (!['ADMIN', 'OWNER'].includes(user.role)) {
        const response: ApiResponse = {
          success: false,
          error: 'Insufficient permissions',
        };
        res.status(403).json(response);
        return;
      }

      const { userId } = req.params;
      const { role } = req.body as { role: string };

      if (!userId) {
        const response: ApiResponse = {
          success: false,
          error: 'User ID is required',
        };
        res.status(400).json(response);
        return;
      }

      if (!role || !['USER', 'ADMIN', 'OWNER'].includes(role)) {
        const response: ApiResponse = {
          success: false,
          error: 'Valid role is required (USER, ADMIN, OWNER)',
        };
        res.status(400).json(response);
        return;
      }

      // Prevent users from changing their own role to a lower level
      if (userId === user.userId && role !== user.role) {
        const response: ApiResponse = {
          success: false,
          error: 'Cannot change your own role',
        };
        res.status(400).json(response);
        return;
      }

      // Get target user
      const targetUser = await prisma.user.findUnique({
        where: { id: userId, tenantId: user.tenantId },
      });

      if (!targetUser) {
        const response: ApiResponse = {
          success: false,
          error: 'User not found',
        };
        res.status(404).json(response);
        return;
      }

      // Update user role
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: { role: role as UserRole },
      });

      // Log role change
      await AuditLogger.logUserManagement(
        'USER_UPDATED',
        userId,
        user.userId,
        req.ip || '0.0.0.0',
        req.get('User-Agent') || 'unknown',
        user.tenantId,
        { role: targetUser.role },
        { role: role as string }
      );

      const response: ApiResponse = {
        success: true,
        data: {
          user: {
            id: updatedUser.id,
            email: updatedUser.email,
            name: updatedUser.name,
            role: updatedUser.role,
            updatedAt: updatedUser.updatedAt,
          },
        },
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('Update user role error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to update user role',
      };
      res.status(500).json(response);
    }
  }
}
