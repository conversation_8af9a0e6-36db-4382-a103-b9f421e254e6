import { Request, Response } from 'express';
import { SystemUserRole } from '@prisma/client';
import { AuthService, CreateSystemUserData } from '../services/auth';
import { AuditLogger } from '../utils/audit';
import { ApiResponse } from '@underwriting/shared';
import { prisma } from '../prisma/client';

import { JWTPayload } from '../utils/jwt';

export interface SystemAuthRequest extends Request {
  user?: JWTPayload;
}

export class SystemUsersController {
  /**
   * POST /api/system/users
   * Create new system user (super admin only)
   */
  static async createSystemUser(
    req: SystemAuthRequest,
    res: Response
  ): Promise<void> {
    try {
      const user = req.user;
      if (!user || user.isSystemUser !== true || user.role !== 'SUPER_ADMIN') {
        const response: ApiResponse = {
          success: false,
          error: 'Super admin access required',
        };
        res.status(403).json(response);
        return;
      }

      const { email, password, name, role }: CreateSystemUserData =
        req.body as CreateSystemUserData;

      // Input validation
      if (!email || !password || !name || !role) {
        const response: ApiResponse = {
          success: false,
          error: 'Email, password, name, and role are required',
        };
        res.status(400).json(response);
        return;
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        const response: ApiResponse = {
          success: false,
          error: 'Invalid email format',
        };
        res.status(400).json(response);
        return;
      }

      // Validate role
      if (!['SUPER_ADMIN', 'PLATFORM_ADMIN', 'SUPPORT'].includes(role)) {
        const response: ApiResponse = {
          success: false,
          error:
            'Invalid role. Must be SUPER_ADMIN, PLATFORM_ADMIN, or SUPPORT',
        };
        res.status(400).json(response);
        return;
      }

      // Check if system user already exists
      const existingUser = await prisma.systemUser.findUnique({
        where: { email },
      });

      if (existingUser) {
        const response: ApiResponse = {
          success: false,
          error: 'System user with this email already exists',
        };
        res.status(409).json(response);
        return;
      }

      // Create system user
      const systemUser = await AuthService.createSystemUser({
        email,
        password,
        name,
        role: role as SystemUserRole,
      });

      // Log system user creation
      await AuditLogger.logTenantManagement(
        'TENANT_CREATED', // Using tenant management for system operations
        'SYSTEM',
        user.userId,
        req.ip || '0.0.0.0',
        req.get('User-Agent') || 'unknown',
        undefined,
        {
          email: systemUser.email,
          name: systemUser.name,
          role: systemUser.role,
        },
        true
      );

      const response: ApiResponse = {
        success: true,
        data: {
          systemUser: {
            id: systemUser.id,
            email: systemUser.email,
            name: systemUser.name,
            role: systemUser.role,
            emailVerified: systemUser.emailVerified,
            isActive: systemUser.isActive,
            createdAt: systemUser.createdAt,
          },
        },
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('Create system user error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'System user creation failed',
      };
      res.status(500).json(response);
    }
  }

  /**
   * GET /api/system/users
   * List system users (admin only)
   */
  static async listSystemUsers(
    req: SystemAuthRequest,
    res: Response
  ): Promise<void> {
    try {
      const user = req.user;
      if (
        !user ||
        user.isSystemUser !== true ||
        !['SUPER_ADMIN', 'PLATFORM_ADMIN'].includes(user.role)
      ) {
        const response: ApiResponse = {
          success: false,
          error: 'System admin access required',
        };
        res.status(403).json(response);
        return;
      }

      const { page = 1, limit = 20, search, role, isActive } = req.query;
      const offset = (Number(page) - 1) * Number(limit);

      // Build search filters
      const where: Record<string, unknown> = {};

      if (search) {
        where.OR = [
          { email: { contains: search as string, mode: 'insensitive' } },
          { name: { contains: search as string, mode: 'insensitive' } },
        ];
      }

      if (role && role !== 'all') {
        where.role = role as SystemUserRole;
      }

      if (isActive !== undefined) {
        where.isActive = isActive === 'true';
      }

      // Get system users with pagination
      const [systemUsers, totalCount] = await Promise.all([
        prisma.systemUser.findMany({
          where,
          select: {
            id: true,
            email: true,
            name: true,
            role: true,
            emailVerified: true,
            isActive: true,
            lastLoginAt: true,
            createdAt: true,
            updatedAt: true,
          },
          orderBy: { createdAt: 'desc' },
          take: Number(limit),
          skip: offset,
        }),
        prisma.systemUser.count({ where }),
      ]);

      const response: ApiResponse = {
        success: true,
        data: {
          systemUsers,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total: totalCount,
            pages: Math.ceil(totalCount / Number(limit)),
          },
        },
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('List system users error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to list system users',
      };
      res.status(500).json(response);
    }
  }

  /**
   * GET /api/system/users/:userId
   * Get system user details
   */
  static async getSystemUser(
    req: SystemAuthRequest,
    res: Response
  ): Promise<void> {
    try {
      const user = req.user;
      if (
        !user ||
        user.isSystemUser !== true ||
        !['SUPER_ADMIN', 'PLATFORM_ADMIN'].includes(user.role)
      ) {
        const response: ApiResponse = {
          success: false,
          error: 'System admin access required',
        };
        res.status(403).json(response);
        return;
      }

      const { userId } = req.params;

      const systemUser = await prisma.systemUser.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          emailVerified: true,
          isActive: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!systemUser) {
        const response: ApiResponse = {
          success: false,
          error: 'System user not found',
        };
        res.status(404).json(response);
        return;
      }

      const response: ApiResponse = {
        success: true,
        data: { systemUser },
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('Get system user error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to get system user',
      };
      res.status(500).json(response);
    }
  }

  /**
   * PUT /api/system/users/:userId
   * Update system user (super admin only)
   */
  static async updateSystemUser(
    req: SystemAuthRequest,
    res: Response
  ): Promise<void> {
    try {
      const user = req.user;
      if (!user || user.isSystemUser !== true || user.role !== 'SUPER_ADMIN') {
        const response: ApiResponse = {
          success: false,
          error: 'Super admin access required',
        };
        res.status(403).json(response);
        return;
      }

      const { userId } = req.params;
      const { name, role, isActive } = req.body as {
        name?: string;
        role?: string;
        isActive?: boolean;
      };

      // Prevent users from deactivating themselves
      if (userId === user.userId && isActive === false) {
        const response: ApiResponse = {
          success: false,
          error: 'Cannot deactivate your own account',
        };
        res.status(400).json(response);
        return;
      }

      // Get current system user
      const currentSystemUser = await prisma.systemUser.findUnique({
        where: { id: userId },
      });

      if (!currentSystemUser) {
        const response: ApiResponse = {
          success: false,
          error: 'System user not found',
        };
        res.status(404).json(response);
        return;
      }

      // Validate role if provided
      if (
        role &&
        !['SUPER_ADMIN', 'PLATFORM_ADMIN', 'SUPPORT'].includes(role)
      ) {
        const response: ApiResponse = {
          success: false,
          error:
            'Invalid role. Must be SUPER_ADMIN, PLATFORM_ADMIN, or SUPPORT',
        };
        res.status(400).json(response);
        return;
      }

      // Update system user
      const updatedSystemUser = await prisma.systemUser.update({
        where: { id: userId },
        data: {
          ...(name !== undefined && { name: name as string }),
          ...(role !== undefined && { role: role as SystemUserRole }),
          ...(isActive !== undefined && { isActive: isActive as boolean }),
          updatedAt: new Date(),
        },
      });

      // Log system user update
      await AuditLogger.logTenantManagement(
        'TENANT_UPDATED',
        'SYSTEM',
        user.userId,
        req.ip || '0.0.0.0',
        req.get('User-Agent') || 'unknown',
        {
          name: currentSystemUser.name,
          role: currentSystemUser.role,
          isActive: currentSystemUser.isActive,
        },
        {
          name: updatedSystemUser.name,
          role: updatedSystemUser.role,
          isActive: updatedSystemUser.isActive,
        },
        true
      );

      const response: ApiResponse = {
        success: true,
        data: {
          systemUser: {
            id: updatedSystemUser.id,
            email: updatedSystemUser.email,
            name: updatedSystemUser.name,
            role: updatedSystemUser.role,
            isActive: updatedSystemUser.isActive,
            updatedAt: updatedSystemUser.updatedAt,
          },
        },
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('Update system user error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to update system user',
      };
      res.status(500).json(response);
    }
  }

  /**
   * DELETE /api/system/users/:userId
   * Delete system user (super admin only)
   */
  static async deleteSystemUser(
    req: SystemAuthRequest,
    res: Response
  ): Promise<void> {
    try {
      const user = req.user;
      if (!user || user.isSystemUser !== true || user.role !== 'SUPER_ADMIN') {
        const response: ApiResponse = {
          success: false,
          error: 'Super admin access required',
        };
        res.status(403).json(response);
        return;
      }

      const { userId } = req.params;

      // Prevent users from deleting themselves
      if (userId === user.userId) {
        const response: ApiResponse = {
          success: false,
          error: 'Cannot delete your own account',
        };
        res.status(400).json(response);
        return;
      }

      // Get system user to delete
      const systemUserToDelete = await prisma.systemUser.findUnique({
        where: { id: userId },
      });

      if (!systemUserToDelete) {
        const response: ApiResponse = {
          success: false,
          error: 'System user not found',
        };
        res.status(404).json(response);
        return;
      }

      // Delete system user
      await prisma.systemUser.delete({
        where: { id: userId },
      });

      // Log system user deletion
      await AuditLogger.logTenantManagement(
        'TENANT_DELETED',
        'SYSTEM',
        user.userId,
        req.ip || '0.0.0.0',
        req.get('User-Agent') || 'unknown',
        {
          email: systemUserToDelete.email,
          name: systemUserToDelete.name,
          role: systemUserToDelete.role,
        },
        undefined,
        true
      );

      const response: ApiResponse = {
        success: true,
        data: { message: 'System user deleted successfully' },
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('Delete system user error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to delete system user',
      };
      res.status(500).json(response);
    }
  }

  /**
   * GET /api/system/audit-logs
   * Get system audit logs (admin only)
   */
  static async getAuditLogs(
    req: SystemAuthRequest,
    res: Response
  ): Promise<void> {
    try {
      const user = req.user;
      if (
        !user ||
        user.isSystemUser !== true ||
        !['SUPER_ADMIN', 'PLATFORM_ADMIN'].includes(user.role)
      ) {
        const response: ApiResponse = {
          success: false,
          error: 'System admin access required',
        };
        res.status(403).json(response);
        return;
      }

      const { limit = 50, userId } = req.query;

      const auditLogs = await AuditLogger.getSystemAuditLogs(
        userId as string,
        Number(limit)
      );

      const response: ApiResponse = {
        success: true,
        data: { auditLogs },
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('Get audit logs error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to get audit logs',
      };
      res.status(500).json(response);
    }
  }
}
