import { Request, Response } from 'express';
import { AuthService, LoginCredentials } from '../services/auth';
import { AuditLogger } from '../utils/audit';
import { ApiResponse } from '@underwriting/shared';

import { JWTPayload } from '../utils/jwt';

export interface AuthRequest extends Request {
  user?: JWTPayload;
}

export class AuthController {
  /**
   * POST /api/auth/login
   * Authenticate tenant user
   */
  static async login(req: Request, res: Response): Promise<void> {
    try {
      const { email, password }: LoginCredentials =
        req.body as LoginCredentials;

      // Input validation
      if (!email || !password) {
        const response: ApiResponse = {
          success: false,
          error: 'Email and password are required',
        };
        res.status(400).json(response);
        return;
      }

      // Authenticate user
      const result = await AuthService.login({ email, password });

      if (!result.success) {
        // Log failed login attempt
        await AuditLogger.logAuth(
          'LOGIN_FAILED',
          'anonymous-user', // Use placeholder for failed login
          req.ip || '0.0.0.0',
          req.get('User-Agent') || 'unknown',
          email,
          false,
          { reason: result.error }
        );

        const response: ApiResponse = {
          success: false,
          error: result.error,
        };
        res.status(401).json(response);
        return;
      }

      // Log successful login
      await AuditLogger.logAuth(
        'LOGIN',
        result.user?.id || 'unknown-user',
        req.ip || '0.0.0.0',
        req.get('User-Agent') || 'unknown',
        result.user?.email,
        false
      );

      const response: ApiResponse = {
        success: true,
        data: {
          tokens: result.tokens,
          user: result.user,
        },
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('Login error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Authentication failed',
      };
      res.status(500).json(response);
    }
  }

  /**
   * POST /api/auth/system/login
   * Authenticate system user (admin)
   */
  static async systemLogin(req: Request, res: Response): Promise<void> {
    try {
      const { email, password }: LoginCredentials =
        req.body as LoginCredentials;

      // Input validation
      if (!email || !password) {
        const response: ApiResponse = {
          success: false,
          error: 'Email and password are required',
        };
        res.status(400).json(response);
        return;
      }

      // Authenticate system user
      const result = await AuthService.systemLogin({ email, password });

      if (!result.success) {
        // Log failed system login attempt
        await AuditLogger.logAuth(
          'LOGIN_FAILED',
          'system-user-unknown',
          req.ip || '0.0.0.0',
          req.get('User-Agent') || 'unknown',
          email,
          true,
          { reason: result.error }
        );

        const response: ApiResponse = {
          success: false,
          error: result.error,
        };
        res.status(401).json(response);
        return;
      }

      // Log successful system login
      await AuditLogger.logAuth(
        'LOGIN',
        result.user?.id || 'system-user-unknown',
        req.ip || '0.0.0.0',
        req.get('User-Agent') || 'unknown',
        result.user?.email,
        true
      );

      const response: ApiResponse = {
        success: true,
        data: {
          tokens: result.tokens,
          user: result.user,
        },
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('System login error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Authentication failed',
      };
      res.status(500).json(response);
    }
  }

  /**
   * POST /api/auth/logout
   * Logout user and invalidate session
   */
  static async logout(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      if (!user) {
        const response: ApiResponse = {
          success: false,
          error: 'User not authenticated',
        };
        res.status(401).json(response);
        return;
      }

      // Logout user
      const success = await AuthService.logout(
        user.sessionId,
        user.isSystemUser || false
      );

      if (success) {
        // Log logout
        await AuditLogger.logAuth(
          'LOGOUT',
          user.userId,
          user.email,
          req.ip || '',
          req.get('User-Agent'),
          user.isSystemUser || false || false
        );

        const response: ApiResponse = {
          success: true,
          data: { message: 'Logged out successfully' },
        };
        res.status(200).json(response);
      } else {
        const response: ApiResponse = {
          success: false,
          error: 'Logout failed',
        };
        res.status(500).json(response);
      }
    } catch (error) {
      console.error('Logout error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Logout failed',
      };
      res.status(500).json(response);
    }
  }

  /**
   * POST /api/auth/refresh
   * Refresh access token using refresh token
   */
  static async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const { refreshToken } = req.body as { refreshToken: string };

      if (!refreshToken) {
        const response: ApiResponse = {
          success: false,
          error: 'Refresh token is required',
        };
        res.status(400).json(response);
        return;
      }

      // Refresh token
      const result = await AuthService.refreshToken(refreshToken);

      if (!result.success) {
        const response: ApiResponse = {
          success: false,
          error: result.error,
        };
        res.status(401).json(response);
        return;
      }

      const response: ApiResponse = {
        success: true,
        data: {
          tokens: result.tokens,
          user: result.user,
        },
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('Token refresh error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Token refresh failed',
      };
      res.status(500).json(response);
    }
  }

  /**
   * GET /api/auth/me
   * Get current user profile
   */
  static async getProfile(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      if (!user) {
        const response: ApiResponse = {
          success: false,
          error: 'User not authenticated',
        };
        res.status(401).json(response);
        return;
      }

      const response: ApiResponse = {
        success: true,
        data: {
          user: {
            id: user.userId,
            email: user.email,
            role: user.role,
            tenantId: user.tenantId,
            isSystemUser: user.isSystemUser,
          },
        },
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('Get profile error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to get user profile',
      };
      res.status(500).json(response);
    }
  }

  /**
   * POST /api/auth/change-password
   * Change user password
   */
  static async changePassword(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      if (!user) {
        const response: ApiResponse = {
          success: false,
          error: 'User not authenticated',
        };
        res.status(401).json(response);
        return;
      }

      const { currentPassword, newPassword } = req.body as {
        currentPassword: string;
        newPassword: string;
      };

      if (!currentPassword || !newPassword) {
        const response: ApiResponse = {
          success: false,
          error: 'Current password and new password are required',
        };
        res.status(400).json(response);
        return;
      }

      // Change password
      const result = await AuthService.changePassword(
        user.userId,
        currentPassword,
        newPassword,
        user.isSystemUser || false
      );

      if (!result.success) {
        const response: ApiResponse = {
          success: false,
          error: result.error,
        };
        res.status(400).json(response);
        return;
      }

      // Log password change
      await AuditLogger.logAuth(
        'PASSWORD_CHANGED',
        user.userId,
        user.email,
        req.ip || '',
        req.get('User-Agent'),
        user.isSystemUser || false
      );

      const response: ApiResponse = {
        success: true,
        data: { message: 'Password changed successfully' },
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('Change password error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Password change failed',
      };
      res.status(500).json(response);
    }
  }
}
