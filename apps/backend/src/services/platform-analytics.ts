/**
 * Platform Analytics Service
 * Provides comprehensive business intelligence and revenue analytics for platform owners
 * Implements Story 9.3: Platform Revenue & Analytics Dashboard
 */

import { BillingCycle, SubscriptionStatus } from '@prisma/client';
import { prisma } from '../prisma/client';

export interface RevenueAnalytics {
  mrr: number; // Monthly Recurring Revenue
  arr: number; // Annual Recurring Revenue
  growthRate: number; // Month-over-month growth rate
  churnRate: number; // Monthly churn rate
  ltv: number; // Customer Lifetime Value
  cac: number; // Customer Acquisition Cost (estimated)
  cohortData: CohortAnalysis[];
  forecasts: RevenueForecast[];
}

export interface CohortAnalysis {
  cohortMonth: string; // YYYY-MM format
  customersAcquired: number;
  retentionRates: number[]; // Retention rates for months 1, 2, 3, etc.
  revenueRetention: number[]; // Revenue retention for months 1, 2, 3, etc.
  totalRevenue: number;
  averageRevenue: number;
}

export interface RevenueForecast {
  month: string; // YYYY-MM format
  predictedMrr: number;
  confidence: number; // 0-100
  scenario: 'conservative' | 'realistic' | 'optimistic';
  factors: string[];
}

export interface ChurnPrediction {
  tenantId: string;
  tenantName: string;
  riskScore: number; // 0-100
  predictedChurnDate: Date;
  confidence: number; // 0-100
  riskFactors: string[];
  recommendedActions: string[];
}

export interface PlatformMetrics {
  totalTenants: number;
  activeTenants: number;
  churnedTenants: number;
  totalRevenue: number;
  averageRevenuePerTenant: number;
  tenantGrowthRate: number;
  revenueGrowthRate: number;
}

export interface TenantSegmentation {
  segment: string;
  tenantCount: number;
  totalRevenue: number;
  averageRevenue: number;
  churnRate: number;
  growthRate: number;
  characteristics: string[];
}

export interface UsagePatternAnalysis {
  period: string;
  totalApiCalls: number;
  averageCallsPerTenant: number;
  peakUsageHours: number[];
  topFeatures: Array<{
    feature: string;
    usageCount: number;
    tenantCount: number;
  }>;
  usageTrends: Array<{
    date: string;
    totalUsage: number;
    uniqueTenants: number;
  }>;
}

export interface FinancialForecast {
  period: string;
  scenario: 'conservative' | 'realistic' | 'optimistic';
  projectedRevenue: number;
  projectedCosts: number;
  projectedProfit: number;
  assumptions: string[];
  riskFactors: string[];
}

export interface ExecutiveReport {
  id: string;
  title: string;
  generatedAt: Date;
  period: {
    start: Date;
    end: Date;
  };
  summary: {
    totalRevenue: number;
    revenueGrowth: number;
    tenantGrowth: number;
    churnRate: number;
    keyInsights: string[];
  };
  metrics: RevenueAnalytics;
  segments: TenantSegmentation[];
  forecasts: FinancialForecast[];
  recommendations: string[];
}

export class PlatformAnalyticsService {
  /**
   * Get comprehensive revenue analytics
   */
  async getRevenueAnalytics(
    dateRange?: { start: Date; end: Date }
  ): Promise<RevenueAnalytics> {
    const endDate = dateRange?.end || new Date();
    const startDate = dateRange?.start || new Date(endDate.getFullYear(), endDate.getMonth() - 12, 1);

    // Calculate MRR (Monthly Recurring Revenue)
    const mrr = await this.calculateMRR(endDate);
    
    // Calculate ARR (Annual Recurring Revenue)
    const arr = mrr * 12;
    
    // Calculate growth rate
    const previousMonthMrr = await this.calculateMRR(
      new Date(endDate.getFullYear(), endDate.getMonth() - 1, 1)
    );
    const growthRate = previousMonthMrr > 0 ? ((mrr - previousMonthMrr) / previousMonthMrr) * 100 : 0;
    
    // Calculate churn rate
    const churnRate = await this.calculateChurnRate(endDate);
    
    // Calculate LTV and CAC
    const ltv = await this.calculateLTV();
    const cac = await this.calculateCAC();
    
    // Generate cohort analysis
    const cohortData = await this.generateCohortAnalysis(startDate, endDate);
    
    // Generate forecasts
    const forecasts = await this.generateRevenueForecasts(mrr, growthRate);

    return {
      mrr,
      arr,
      growthRate,
      churnRate,
      ltv,
      cac,
      cohortData,
      forecasts,
    };
  }

  /**
   * Calculate Monthly Recurring Revenue for a specific date
   */
  private async calculateMRR(date: Date): Promise<number> {
    const subscriptions = await prisma.subscription.findMany({
      where: {
        status: SubscriptionStatus.ACTIVE,
        startDate: { lte: date },
        OR: [
          { endDate: null },
          { endDate: { gte: date } }
        ]
      },
      select: {
        monthlyFee: true,
        billingCycle: true,
      },
    });

    let totalMrr = 0;
    for (const subscription of subscriptions) {
      const monthlyFee = Number(subscription.monthlyFee);
      if (subscription.billingCycle === BillingCycle.YEARLY) {
        // Convert yearly to monthly
        totalMrr += monthlyFee / 12;
      } else {
        totalMrr += monthlyFee;
      }
    }

    return Math.round(totalMrr * 100) / 100;
  }

  /**
   * Calculate monthly churn rate
   */
  private async calculateChurnRate(date: Date): Promise<number> {
    const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
    const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0);

    // Get active subscriptions at start of month
    const activeAtStart = await prisma.subscription.count({
      where: {
        status: SubscriptionStatus.ACTIVE,
        startDate: { lt: startOfMonth },
        OR: [
          { endDate: null },
          { endDate: { gte: startOfMonth } }
        ]
      },
    });

    // Get churned subscriptions during the month
    const churned = await prisma.subscription.count({
      where: {
        status: { in: [SubscriptionStatus.CANCELLED, SubscriptionStatus.EXPIRED] },
        endDate: {
          gte: startOfMonth,
          lte: endOfMonth,
        },
      },
    });

    return activeAtStart > 0 ? (churned / activeAtStart) * 100 : 0;
  }

  /**
   * Calculate Customer Lifetime Value
   */
  private async calculateLTV(): Promise<number> {
    // Get average monthly revenue per customer
    const avgMonthlyRevenue = await this.getAverageMonthlyRevenuePerCustomer();
    
    // Get average customer lifespan in months
    const avgLifespan = await this.getAverageCustomerLifespan();
    
    return Math.round(avgMonthlyRevenue * avgLifespan * 100) / 100;
  }

  /**
   * Calculate Customer Acquisition Cost (estimated)
   */
  private async calculateCAC(): Promise<number> {
    // This is a simplified calculation
    // In a real implementation, this would include marketing and sales costs
    const totalTenants = await prisma.tenant.count();
    const estimatedAcquisitionCosts = totalTenants * 150; // Estimated $150 per customer
    
    return totalTenants > 0 ? estimatedAcquisitionCosts / totalTenants : 0;
  }

  /**
   * Get average monthly revenue per customer
   */
  private async getAverageMonthlyRevenuePerCustomer(): Promise<number> {
    const activeSubscriptions = await prisma.subscription.findMany({
      where: { status: SubscriptionStatus.ACTIVE },
      select: { monthlyFee: true, billingCycle: true },
    });

    if (activeSubscriptions.length === 0) return 0;

    let totalMonthlyRevenue = 0;
    for (const subscription of activeSubscriptions) {
      const monthlyFee = Number(subscription.monthlyFee);
      if (subscription.billingCycle === BillingCycle.YEARLY) {
        totalMonthlyRevenue += monthlyFee / 12;
      } else {
        totalMonthlyRevenue += monthlyFee;
      }
    }

    return totalMonthlyRevenue / activeSubscriptions.length;
  }

  /**
   * Get average customer lifespan in months
   */
  private async getAverageCustomerLifespan(): Promise<number> {
    const completedSubscriptions = await prisma.subscription.findMany({
      where: {
        status: { in: [SubscriptionStatus.CANCELLED, SubscriptionStatus.EXPIRED] },
        endDate: { not: null },
      },
      select: {
        startDate: true,
        endDate: true,
      },
    });

    if (completedSubscriptions.length === 0) {
      // If no completed subscriptions, estimate based on current active ones
      return 24; // Default 24 months
    }

    let totalLifespanMonths = 0;
    for (const subscription of completedSubscriptions) {
      if (subscription.endDate) {
        const lifespanMs = subscription.endDate.getTime() - subscription.startDate.getTime();
        const lifespanMonths = lifespanMs / (1000 * 60 * 60 * 24 * 30.44); // Average days per month
        totalLifespanMonths += lifespanMonths;
      }
    }

    return totalLifespanMonths / completedSubscriptions.length;
  }

  /**
   * Generate cohort analysis
   */
  private async generateCohortAnalysis(
    startDate: Date,
    endDate: Date
  ): Promise<CohortAnalysis[]> {
    const cohorts: CohortAnalysis[] = [];
    
    // Generate monthly cohorts for the date range
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      const cohortMonth = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
      
      // Get tenants acquired in this month
      const cohortStart = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
      const cohortEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
      
      const cohortTenants = await prisma.tenant.findMany({
        where: {
          createdAt: {
            gte: cohortStart,
            lte: cohortEnd,
          },
        },
        include: {
          subscriptions: true,
        },
      });

      if (cohortTenants.length > 0) {
        const cohortAnalysis = await this.analyzeCohort(cohortTenants, cohortStart);
        cohorts.push({
          cohortMonth,
          customersAcquired: cohortTenants.length,
          ...cohortAnalysis,
        });
      }

      currentDate.setMonth(currentDate.getMonth() + 1);
    }

    return cohorts;
  }

  /**
   * Analyze a specific cohort
   */
  private async analyzeCohort(
    cohortTenants: any[],
    cohortStart: Date
  ): Promise<Omit<CohortAnalysis, 'cohortMonth' | 'customersAcquired'>> {
    const retentionRates: number[] = [];
    const revenueRetention: number[] = [];
    let totalRevenue = 0;

    // Calculate retention for up to 12 months
    for (let month = 1; month <= 12; month++) {
      const checkDate = new Date(cohortStart);
      checkDate.setMonth(checkDate.getMonth() + month);

      if (checkDate > new Date()) break; // Don't calculate future retention

      let retainedCustomers = 0;
      let retainedRevenue = 0;

      for (const tenant of cohortTenants) {
        const activeSubscription = tenant.subscriptions.find((sub: any) =>
          sub.status === SubscriptionStatus.ACTIVE &&
          sub.startDate <= checkDate &&
          (!sub.endDate || sub.endDate >= checkDate)
        );

        if (activeSubscription) {
          retainedCustomers++;
          const monthlyFee = Number(activeSubscription.monthlyFee);
          retainedRevenue += activeSubscription.billingCycle === BillingCycle.YEARLY
            ? monthlyFee / 12
            : monthlyFee;
        }
      }

      const retentionRate = (retainedCustomers / cohortTenants.length) * 100;
      retentionRates.push(Math.round(retentionRate * 100) / 100);
      revenueRetention.push(Math.round(retainedRevenue * 100) / 100);
      totalRevenue += retainedRevenue;
    }

    const averageRevenue = cohortTenants.length > 0 ? totalRevenue / cohortTenants.length : 0;

    return {
      retentionRates,
      revenueRetention,
      totalRevenue: Math.round(totalRevenue * 100) / 100,
      averageRevenue: Math.round(averageRevenue * 100) / 100,
    };
  }

  /**
   * Generate revenue forecasts
   */
  private async generateRevenueForecasts(
    currentMrr: number,
    growthRate: number
  ): Promise<RevenueForecast[]> {
    const forecasts: RevenueForecast[] = [];
    const scenarios = [
      { name: 'conservative' as const, multiplier: 0.5 },
      { name: 'realistic' as const, multiplier: 1.0 },
      { name: 'optimistic' as const, multiplier: 1.5 },
    ];

    // Generate 12-month forecasts
    for (let month = 1; month <= 12; month++) {
      const date = new Date();
      date.setMonth(date.getMonth() + month);
      const monthStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

      for (const scenario of scenarios) {
        const adjustedGrowthRate = (growthRate / 100) * scenario.multiplier;
        const predictedMrr = currentMrr * Math.pow(1 + adjustedGrowthRate, month);

        const confidence = this.calculateForecastConfidence(month, scenario.name);
        const factors = this.getForecastFactors(scenario.name);

        forecasts.push({
          month: monthStr,
          predictedMrr: Math.round(predictedMrr * 100) / 100,
          confidence,
          scenario: scenario.name,
          factors,
        });
      }
    }

    return forecasts;
  }

  /**
   * Calculate forecast confidence based on time horizon and scenario
   */
  private calculateForecastConfidence(month: number, scenario: string): number {
    let baseConfidence = 90;

    // Confidence decreases over time
    baseConfidence -= month * 5;

    // Adjust based on scenario
    switch (scenario) {
      case 'conservative':
        baseConfidence += 10;
        break;
      case 'optimistic':
        baseConfidence -= 15;
        break;
    }

    return Math.max(30, Math.min(95, baseConfidence));
  }

  /**
   * Get factors affecting forecast
   */
  private getForecastFactors(scenario: string): string[] {
    const commonFactors = [
      'Historical growth trends',
      'Market conditions',
      'Seasonal variations',
    ];

    switch (scenario) {
      case 'conservative':
        return [...commonFactors, 'Economic uncertainty', 'Increased competition'];
      case 'optimistic':
        return [...commonFactors, 'Market expansion', 'Product improvements'];
      default:
        return commonFactors;
    }
  }

  /**
   * Predict tenant churn risk
   */
  async predictChurnRisk(
    tenantIds?: string[]
  ): Promise<ChurnPrediction[]> {
    const whereClause = tenantIds ? { id: { in: tenantIds } } : {};

    const tenants = await prisma.tenant.findMany({
      where: whereClause,
      include: {
        subscriptions: {
          where: { status: SubscriptionStatus.ACTIVE },
        },
        users: {
          select: { lastLoginAt: true },
        },
      },
    });

    const predictions: ChurnPrediction[] = [];

    for (const tenant of tenants) {
      const riskScore = await this.calculateChurnRiskScore(tenant);
      const prediction = this.generateChurnPrediction(tenant, riskScore);
      predictions.push(prediction);
    }

    return predictions.sort((a, b) => b.riskScore - a.riskScore);
  }

  /**
   * Calculate churn risk score for a tenant
   */
  private async calculateChurnRiskScore(tenant: any): Promise<number> {
    let riskScore = 0;

    // Factor 1: User activity (40% weight)
    const lastLoginDays = this.getDaysSinceLastLogin(tenant.users);
    if (lastLoginDays > 30) riskScore += 40;
    else if (lastLoginDays > 14) riskScore += 20;
    else if (lastLoginDays > 7) riskScore += 10;

    // Factor 2: Subscription age (20% weight)
    const subscriptionAge = this.getSubscriptionAge(tenant.subscriptions[0]);
    if (subscriptionAge < 90) riskScore += 20; // New customers are at higher risk

    // Factor 3: Payment issues (30% weight)
    const hasPaymentIssues = await this.hasRecentPaymentIssues(tenant.id);
    if (hasPaymentIssues) riskScore += 30;

    // Factor 4: Support tickets (10% weight)
    const supportTicketCount = await this.getRecentSupportTicketCount(tenant.id);
    if (supportTicketCount > 5) riskScore += 10;

    return Math.min(100, riskScore);
  }

  /**
   * Generate churn prediction details
   */
  private generateChurnPrediction(tenant: any, riskScore: number): ChurnPrediction {
    const riskFactors: string[] = [];
    const recommendedActions: string[] = [];

    if (riskScore > 70) {
      riskFactors.push('High risk of churn');
      recommendedActions.push('Immediate customer success intervention');
    }
    if (riskScore > 50) {
      riskFactors.push('Moderate risk factors detected');
      recommendedActions.push('Proactive outreach recommended');
    }

    // Predict churn date based on risk score
    const daysToChurn = Math.max(30, 180 - (riskScore * 1.5));
    const predictedChurnDate = new Date();
    predictedChurnDate.setDate(predictedChurnDate.getDate() + daysToChurn);

    return {
      tenantId: tenant.id,
      tenantName: tenant.name || 'Unknown',
      riskScore,
      predictedChurnDate,
      confidence: this.calculateChurnConfidence(riskScore),
      riskFactors,
      recommendedActions,
    };
  }

  /**
   * Helper methods for churn prediction
   */
  private getDaysSinceLastLogin(users: any[]): number {
    if (!users.length) return 999;

    const lastLogin = users
      .map(u => u.lastLoginAt)
      .filter(Boolean)
      .sort((a, b) => b.getTime() - a.getTime())[0];

    if (!lastLogin) return 999;

    return Math.floor((Date.now() - lastLogin.getTime()) / (1000 * 60 * 60 * 24));
  }

  private getSubscriptionAge(subscription: any): number {
    if (!subscription) return 0;
    return Math.floor((Date.now() - subscription.startDate.getTime()) / (1000 * 60 * 60 * 24));
  }

  private async hasRecentPaymentIssues(tenantId: string): Promise<boolean> {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const failedPayments = await prisma.payment.count({
      where: {
        invoice: { tenantId },
        status: 'FAILED',
        createdAt: { gte: thirtyDaysAgo },
      },
    });

    return failedPayments > 0;
  }

  private async getRecentSupportTicketCount(_tenantId: string): Promise<number> {
    // This would integrate with a support ticket system
    // For now, return a mock value
    return Math.floor(Math.random() * 10);
  }

  private calculateChurnConfidence(riskScore: number): number {
    // Higher risk scores have higher confidence in prediction
    return Math.min(95, 60 + (riskScore * 0.4));
  }

  /**
   * Get platform-wide metrics
   */
  async getPlatformMetrics(
    dateRange?: { start: Date; end: Date }
  ): Promise<PlatformMetrics> {
    const endDate = dateRange?.end || new Date();
    const startDate = dateRange?.start || new Date(endDate.getFullYear(), endDate.getMonth() - 1, 1);

    const totalTenants = await prisma.tenant.count();

    const activeTenants = await prisma.tenant.count({
      where: {
        subscriptions: {
          some: {
            status: SubscriptionStatus.ACTIVE,
          },
        },
      },
    });

    const churnedTenants = await prisma.tenant.count({
      where: {
        subscriptions: {
          every: {
            status: { in: [SubscriptionStatus.CANCELLED, SubscriptionStatus.EXPIRED] },
          },
        },
      },
    });

    const currentMrr = await this.calculateMRR(endDate);
    const previousMrr = await this.calculateMRR(startDate);

    const totalRevenue = currentMrr * 12; // ARR
    const averageRevenuePerTenant = activeTenants > 0 ? totalRevenue / activeTenants : 0;

    const tenantGrowthRate = await this.calculateTenantGrowthRate(startDate, endDate);
    const revenueGrowthRate = previousMrr > 0 ? ((currentMrr - previousMrr) / previousMrr) * 100 : 0;

    return {
      totalTenants,
      activeTenants,
      churnedTenants,
      totalRevenue: Math.round(totalRevenue * 100) / 100,
      averageRevenuePerTenant: Math.round(averageRevenuePerTenant * 100) / 100,
      tenantGrowthRate: Math.round(tenantGrowthRate * 100) / 100,
      revenueGrowthRate: Math.round(revenueGrowthRate * 100) / 100,
    };
  }

  /**
   * Calculate tenant growth rate
   */
  private async calculateTenantGrowthRate(startDate: Date, endDate: Date): Promise<number> {
    const tenantsAtStart = await prisma.tenant.count({
      where: { createdAt: { lt: startDate } },
    });

    const tenantsAtEnd = await prisma.tenant.count({
      where: { createdAt: { lt: endDate } },
    });

    return tenantsAtStart > 0 ? ((tenantsAtEnd - tenantsAtStart) / tenantsAtStart) * 100 : 0;
  }

  /**
   * Generate tenant segmentation analysis
   */
  async getTenantSegmentation(): Promise<TenantSegmentation[]> {
    const segments: TenantSegmentation[] = [];

    // Segment by subscription tier
    const tiers = ['STARTER', 'PROFESSIONAL', 'ENTERPRISE'];

    for (const tier of tiers) {
      const tenants = await prisma.tenant.findMany({
        where: { subscriptionTier: tier as any },
        include: {
          subscriptions: {
            where: { status: SubscriptionStatus.ACTIVE },
          },
        },
      });

      if (tenants.length > 0) {
        const totalRevenue = tenants.reduce((sum, tenant) => {
          const revenue = tenant.subscriptions.reduce((subSum, sub) => {
            const monthlyFee = Number(sub.monthlyFee);
            return subSum + (sub.billingCycle === BillingCycle.YEARLY ? monthlyFee / 12 : monthlyFee);
          }, 0);
          return sum + revenue;
        }, 0);

        const averageRevenue = totalRevenue / tenants.length;
        const churnRate = await this.calculateSegmentChurnRate(tier);
        const growthRate = await this.calculateSegmentGrowthRate(tier);

        segments.push({
          segment: tier,
          tenantCount: tenants.length,
          totalRevenue: Math.round(totalRevenue * 100) / 100,
          averageRevenue: Math.round(averageRevenue * 100) / 100,
          churnRate: Math.round(churnRate * 100) / 100,
          growthRate: Math.round(growthRate * 100) / 100,
          characteristics: this.getSegmentCharacteristics(tier),
        });
      }
    }

    return segments;
  }

  /**
   * Calculate churn rate for a specific segment
   */
  private async calculateSegmentChurnRate(tier: string): Promise<number> {
    const startOfMonth = new Date();
    startOfMonth.setDate(1);

    const activeAtStart = await prisma.tenant.count({
      where: {
        subscriptionTier: tier as any,
        subscriptions: {
          some: {
            status: SubscriptionStatus.ACTIVE,
            startDate: { lt: startOfMonth },
          },
        },
      },
    });

    const churned = await prisma.tenant.count({
      where: {
        subscriptionTier: tier as any,
        subscriptions: {
          some: {
            status: { in: [SubscriptionStatus.CANCELLED, SubscriptionStatus.EXPIRED] },
            endDate: { gte: startOfMonth },
          },
        },
      },
    });

    return activeAtStart > 0 ? (churned / activeAtStart) * 100 : 0;
  }

  /**
   * Calculate growth rate for a specific segment
   */
  private async calculateSegmentGrowthRate(tier: string): Promise<number> {
    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);

    const currentCount = await prisma.tenant.count({
      where: { subscriptionTier: tier as any },
    });

    const previousCount = await prisma.tenant.count({
      where: {
        subscriptionTier: tier as any,
        createdAt: { lt: lastMonth },
      },
    });

    return previousCount > 0 ? ((currentCount - previousCount) / previousCount) * 100 : 0;
  }

  /**
   * Get characteristics for a segment
   */
  private getSegmentCharacteristics(tier: string): string[] {
    switch (tier) {
      case 'STARTER':
        return ['Small businesses', 'Basic features', 'Price-sensitive'];
      case 'PROFESSIONAL':
        return ['Growing companies', 'Advanced features', 'Value-focused'];
      case 'ENTERPRISE':
        return ['Large organizations', 'Full feature set', 'Custom requirements'];
      default:
        return [];
    }
  }

  /**
   * Generate executive report
   */
  async generateExecutiveReport(
    period: { start: Date; end: Date },
    options: {
      includeForecasts?: boolean;
      includeSegmentation?: boolean;
      includeRecommendations?: boolean;
    } = {}
  ): Promise<ExecutiveReport> {
    const reportId = `exec-report-${Date.now()}`;
    const generatedAt = new Date();

    // Get core metrics
    const metrics = await this.getRevenueAnalytics(period);
    const platformMetrics = await this.getPlatformMetrics(period);

    // Generate summary
    const summary = {
      totalRevenue: metrics.arr,
      revenueGrowth: metrics.growthRate,
      tenantGrowth: platformMetrics.tenantGrowthRate,
      churnRate: metrics.churnRate,
      keyInsights: await this.generateKeyInsights(metrics, platformMetrics),
    };

    // Get segmentation if requested
    const segments = options.includeSegmentation ? await this.getTenantSegmentation() : [];

    // Generate forecasts if requested
    const forecasts = options.includeForecasts ? await this.generateFinancialForecasts(metrics) : [];

    // Generate recommendations if requested
    const recommendations = options.includeRecommendations ?
      await this.generateRecommendations(metrics, platformMetrics) : [];

    return {
      id: reportId,
      title: `Executive Report - ${period.start.toISOString().split('T')[0]} to ${period.end.toISOString().split('T')[0]}`,
      generatedAt,
      period,
      summary,
      metrics,
      segments,
      forecasts,
      recommendations,
    };
  }

  /**
   * Generate key insights
   */
  private async generateKeyInsights(
    metrics: RevenueAnalytics,
    platformMetrics: PlatformMetrics
  ): Promise<string[]> {
    const insights: string[] = [];

    if (metrics.growthRate > 10) {
      insights.push(`Strong revenue growth of ${metrics.growthRate.toFixed(1)}% month-over-month`);
    } else if (metrics.growthRate < 0) {
      insights.push(`Revenue decline of ${Math.abs(metrics.growthRate).toFixed(1)}% requires attention`);
    }

    if (metrics.churnRate > 5) {
      insights.push(`High churn rate of ${metrics.churnRate.toFixed(1)}% indicates customer retention issues`);
    }

    if (platformMetrics.averageRevenuePerTenant > 500) {
      insights.push(`Strong ARPU of $${platformMetrics.averageRevenuePerTenant.toFixed(0)} indicates healthy unit economics`);
    }

    if (metrics.ltv / metrics.cac > 3) {
      insights.push(`Healthy LTV:CAC ratio of ${(metrics.ltv / metrics.cac).toFixed(1)}:1 supports sustainable growth`);
    }

    return insights;
  }

  /**
   * Generate financial forecasts
   */
  private async generateFinancialForecasts(metrics: RevenueAnalytics): Promise<FinancialForecast[]> {
    const forecasts: FinancialForecast[] = [];
    const scenarios = ['conservative', 'realistic', 'optimistic'] as const;

    for (let quarter = 1; quarter <= 4; quarter++) {
      const period = `Q${quarter} ${new Date().getFullYear() + 1}`;

      for (const scenario of scenarios) {
        const multiplier = scenario === 'conservative' ? 0.8 : scenario === 'optimistic' ? 1.3 : 1.0;
        const projectedRevenue = metrics.arr * Math.pow(1 + (metrics.growthRate / 100) * multiplier, quarter);
        const projectedCosts = projectedRevenue * 0.7; // Assume 70% cost ratio
        const projectedProfit = projectedRevenue - projectedCosts;

        forecasts.push({
          period,
          scenario,
          projectedRevenue: Math.round(projectedRevenue * 100) / 100,
          projectedCosts: Math.round(projectedCosts * 100) / 100,
          projectedProfit: Math.round(projectedProfit * 100) / 100,
          assumptions: this.getForecastAssumptions(scenario),
          riskFactors: this.getForecastRiskFactors(scenario),
        });
      }
    }

    return forecasts;
  }

  /**
   * Get forecast assumptions
   */
  private getForecastAssumptions(scenario: string): string[] {
    const common = ['Current growth trends continue', 'Market conditions remain stable'];

    switch (scenario) {
      case 'conservative':
        return [...common, 'Economic headwinds impact growth', 'Increased competition'];
      case 'optimistic':
        return [...common, 'Market expansion opportunities', 'Product innovation drives growth'];
      default:
        return common;
    }
  }

  /**
   * Get forecast risk factors
   */
  private getForecastRiskFactors(scenario: string): string[] {
    const common = ['Market volatility', 'Competitive pressure'];

    switch (scenario) {
      case 'conservative':
        return [...common, 'Economic recession', 'Customer budget cuts'];
      case 'optimistic':
        return [...common, 'Execution risks', 'Market saturation'];
      default:
        return common;
    }
  }

  /**
   * Generate recommendations
   */
  private async generateRecommendations(
    metrics: RevenueAnalytics,
    platformMetrics: PlatformMetrics
  ): Promise<string[]> {
    const recommendations: string[] = [];

    if (metrics.churnRate > 5) {
      recommendations.push('Implement customer success program to reduce churn');
      recommendations.push('Analyze churn reasons and address top pain points');
    }

    if (metrics.growthRate < 5) {
      recommendations.push('Invest in marketing and sales to accelerate growth');
      recommendations.push('Consider product enhancements to increase value proposition');
    }

    if (platformMetrics.averageRevenuePerTenant < 200) {
      recommendations.push('Explore upselling opportunities to increase ARPU');
      recommendations.push('Consider premium tier pricing optimization');
    }

    if (metrics.ltv / metrics.cac < 3) {
      recommendations.push('Optimize customer acquisition costs');
      recommendations.push('Focus on higher-value customer segments');
    }

    return recommendations;
  }

  /**
   * Export analytics data
   */
  async exportAnalyticsData(
    format: 'csv' | 'json' | 'pdf',
    data: any,
    filename: string
  ): Promise<Buffer | string> {
    switch (format) {
      case 'json':
        return JSON.stringify(data, null, 2);
      case 'csv':
        return this.convertToCSV(data);
      case 'pdf':
        return this.generatePDF(data, filename);
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  /**
   * Convert data to CSV format
   */
  private convertToCSV(data: any): string {
    if (Array.isArray(data) && data.length > 0) {
      const headers = Object.keys(data[0]);
      const csvRows = [headers.join(',')];

      for (const row of data) {
        const values = headers.map(header => {
          const value = row[header];
          return typeof value === 'string' ? `"${value}"` : value;
        });
        csvRows.push(values.join(','));
      }

      return csvRows.join('\n');
    }

    return '';
  }

  /**
   * Generate PDF report
   */
  private generatePDF(data: any, filename: string): Buffer {
    // This would integrate with a PDF generation library like puppeteer or jsPDF
    // For now, return a placeholder
    const content = `PDF Report: ${filename}\n\nData: ${JSON.stringify(data, null, 2)}`;
    return Buffer.from(content, 'utf-8');
  }
}
