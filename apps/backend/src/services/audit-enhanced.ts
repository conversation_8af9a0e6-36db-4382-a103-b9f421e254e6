import { Request } from 'express';
import { prisma } from '../prisma/client';
import { SecurityContext } from './security-policy';
import { GeoLocationService } from './geolocation';
import { Prisma } from '@prisma/client';

export interface AuditEventData {
  action: 'AUTH_LOGIN' | 'AUTH_LOGIN_FAILED' | 'AUTH_MFA_REQUIRED' | 'AUTH_MFA_SUCCESS' | 'AUTH_MFA_FAILED' | 'AUTH_LOGOUT' | 'PASSWORD_CHANGE' | 'MFA_SETUP' | 'MFA_ENABLED' | 'MFA_DISABLED' | 'BACKUP_CODE_USED' | 'SESSION_EXPIRED' | 'SECURITY_VIOLATION' | 'IP_BLOCKED' | 'SUSPICIOUS_ACTIVITY' | 'BULK_OPERATION_STARTED' | 'BULK_OPERATION_COMPLETED' | 'BULK_OPERATION_CANCELLED' | 'TENANT_SUSPENDED' | 'TENANT_ACTIVATED' | 'TENANT_TIER_UPDATED' | 'TENANT_NOTIFICATION_SENT' | 'TENANT_DATA_EXPORTED';
  entityType: 'SystemUser' | 'Session' | 'MFA' | 'Security' | 'Tenant' | 'BulkOperation';
  entityId?: string;
  userId: string;
  tenantId?: string;
  details: Record<string, unknown>;
  riskLevel?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  securityContext?: SecurityContext;
  metadata?: {
    userAgent?: string;
    ipAddress?: string;
    timestamp: Date;
    correlationId?: string;
  };
}

export interface SecurityViolation {
  type: 'IP_RESTRICTION' | 'GEO_RESTRICTION' | 'VPN_BLOCKED' | 'TOR_BLOCKED' | 'THREAT_DETECTED' | 'RATE_LIMIT_EXCEEDED' | 'SUSPICIOUS_LOGIN_PATTERN';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  mitigationAction?: string;
}

export interface EnhancedAuditLog {
  id: string;
  userId: string | null;
  action: string;
  entityType: string;
  entityId: string | null;
  ipAddress: string | null;
  userAgent: string | null;
  riskLevel: string;
  metadata: Record<string, unknown>;
  timestamp: Date;
}

/**
 * Enhanced Audit Logging Service
 * Provides comprehensive audit logging with security context enrichment
 */
export class EnhancedAuditService {
  private static readonly MAX_DETAILS_SIZE = 1024 * 10; // 10KB max for details
  private static readonly RETENTION_DAYS = 365; // 1 year retention

  /**
   * Log authentication event with security context
   */
  static async logAuthEvent(
    eventData: AuditEventData,
    req?: Request
  ): Promise<void> {
    try {
      // Build security context if not provided
      let securityContext = eventData.securityContext;
      if (!securityContext && req) {
        securityContext = await this.buildSecurityContext(req);
      }

      // Prepare audit log data
      const auditData = {
        tenantId: eventData.tenantId || 'system', // Default to system tenant for system users
        userId: eventData.userId,
        action: eventData.action,
        entityType: eventData.entityType,
        entityId: eventData.entityId || null,
        ipAddress: securityContext?.ipAddress || this.getIPFromRequest(req) || null,
        userAgent: securityContext?.userAgent || req?.headers['user-agent'] || null,
        oldValues: undefined,
        newValues: this.sanitizeDetails(eventData.details) as Prisma.InputJsonValue,
        metadata: JSON.parse(JSON.stringify({
          riskLevel: eventData.riskLevel || 'LOW',
          securityContext: securityContext ? {
            riskScore: securityContext.riskScore,
            threatIndicators: securityContext.threatIndicators,
            geolocation: securityContext.geolocation,
          } : null,
          correlationId: eventData.metadata?.correlationId || this.generateCorrelationId(),
          timestamp: new Date().toISOString(),
          ...eventData.metadata,
        })) as Prisma.InputJsonValue,
        reason: `Security audit for ${eventData.action}`,
        riskLevel: eventData.riskLevel || 'LOW',
      };

      // Store in audit log table
      await prisma.auditLog.create({
        data: auditData,
      });

      // Log high-risk events for immediate attention
      const riskScore = securityContext?.riskScore || 0;
      if (riskScore > 70) {
        await this.logHighRiskEvent({ ...auditData, riskScore }, securityContext);
      }

      // Check for suspicious patterns
      await this.analyzeSuspiciousPatterns(eventData.userId, auditData.ipAddress || 'unknown');

    } catch (error) {
      console.error('Enhanced audit logging failed:', error);
      // Fallback to simple logging to ensure we don't lose audit trail
      await this.fallbackAuditLog(eventData, req);
    }
  }

  /**
   * Log security violation with immediate escalation
   */
  static async logSecurityViolation(
    userId: string,
    violation: SecurityViolation,
    securityContext: SecurityContext,
    req?: Request
  ): Promise<void> {
    try {
      const auditData: AuditEventData = {
        action: 'SECURITY_VIOLATION',
        entityType: 'Security',
        entityId: userId,
        userId,
        details: {
          violationType: violation.type,
          severity: violation.severity,
          description: violation.description,
          mitigationAction: violation.mitigationAction,
          threatIndicators: securityContext.threatIndicators,
        },
        riskLevel: violation.severity,
        securityContext,
        metadata: {
          timestamp: new Date(),
          correlationId: this.generateCorrelationId(),
        },
      };

      await this.logAuthEvent(auditData, req);

      // Immediate escalation for critical violations
      if (violation.severity === 'CRITICAL') {
        await this.escalateCriticalViolation(userId, violation, securityContext);
      }

    } catch (error) {
      console.error('Security violation logging failed:', error);
    }
  }

  /**
   * Get audit logs for a user with filtering
   */
  static async getUserAuditLogs(
    userId: string,
    options: {
      actions?: string[];
      startDate?: Date;
      endDate?: Date;
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<EnhancedAuditLog[]> {
    try {
      const {
        actions,
        startDate,
        endDate,
        limit = 100,
        offset = 0
      } = options;

      const whereClause: Record<string, unknown> = {
        userId,
      };

      if (actions && actions.length > 0) {
        whereClause.action = { in: actions };
      }

      if (startDate || endDate) {
        whereClause.timestamp = {};
        if (startDate) {
          (whereClause.timestamp as Record<string, unknown>).gte = startDate;
        }
        if (endDate) {
          (whereClause.timestamp as Record<string, unknown>).lte = endDate;
        }
      }

      const auditLogs = await prisma.auditLog.findMany({
        where: whereClause,
        orderBy: { timestamp: 'desc' },
        take: limit,
        skip: offset,
      });

      return auditLogs.map(log => ({
        id: log.id,
        userId: log.userId,
        action: log.action,
        entityType: log.entityType,
        entityId: log.entityId,
        ipAddress: log.ipAddress,
        userAgent: log.userAgent,
        riskLevel: log.riskLevel,
        metadata: log.metadata as Record<string, unknown>,
        timestamp: log.timestamp,
      }));

    } catch (error) {
      console.error('Failed to get user audit logs:', error);
      return [];
    }
  }

  /**
   * Get security dashboard metrics
   */
  static async getSecurityMetrics(
    timeRange: { startDate: Date; endDate: Date }
  ): Promise<{
    totalEvents: number;
    failedLogins: number;
    securityViolations: number;
    highRiskEvents: number;
    topRiskCountries: Array<{ country: string; count: number; avgRiskScore: number }>;
    threatIndicators: Array<{ indicator: string; count: number }>;
  }> {
    try {
      const { startDate, endDate } = timeRange;

      // Get total events
      const totalEvents = await prisma.auditLog.count({
        where: {
          timestamp: { gte: startDate, lte: endDate },
        },
      });

      // Get failed login attempts
      const failedLogins = await prisma.auditLog.count({
        where: {
          action: 'AUTH_LOGIN_FAILED',
          timestamp: { gte: startDate, lte: endDate },
        },
      });

      // Get security violations
      const securityViolations = await prisma.auditLog.count({
        where: {
          action: 'SECURITY_VIOLATION',
          timestamp: { gte: startDate, lte: endDate },
        },
      });

      // Get high-risk events
      const highRiskEvents = await prisma.auditLog.count({
        where: {
          riskLevel: { in: ['HIGH', 'CRITICAL'] },
          timestamp: { gte: startDate, lte: endDate },
        },
      });

      // For production, implement proper aggregations
      const topRiskCountries = [
        { country: 'Unknown', count: 0, avgRiskScore: 0 }
      ];

      const threatIndicators = [
        { indicator: 'suspicious_user_agent', count: 0 }
      ];

      return {
        totalEvents,
        failedLogins,
        securityViolations,
        highRiskEvents,
        topRiskCountries,
        threatIndicators,
      };

    } catch (error) {
      console.error('Failed to get security metrics:', error);
      return {
        totalEvents: 0,
        failedLogins: 0,
        securityViolations: 0,
        highRiskEvents: 0,
        topRiskCountries: [],
        threatIndicators: [],
      };
    }
  }

  /**
   * Cleanup old audit logs based on retention policy
   */
  static async cleanupOldLogs(): Promise<{ deletedCount: number }> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.RETENTION_DAYS);

      const result = await prisma.auditLog.deleteMany({
        where: {
          timestamp: { lt: cutoffDate },
        },
      });

      console.log(`Cleaned up ${result.count} old audit log entries`);
      return { deletedCount: result.count };

    } catch (error) {
      console.error('Failed to cleanup old audit logs:', error);
      return { deletedCount: 0 };
    }
  }

  /**
   * Export audit logs for compliance reporting
   */
  static async exportAuditLogs(
    filters: {
      userId?: string;
      startDate: Date;
      endDate: Date;
      eventTypes?: string[];
    }
  ): Promise<Buffer> {
    try {
      const logs = await prisma.auditLog.findMany({
        where: {
          ...(filters.userId && { userId: filters.userId }),
          timestamp: {
            gte: filters.startDate,
            lte: filters.endDate,
          },
          ...(filters.eventTypes && { action: { in: filters.eventTypes } }),
        },
        orderBy: { timestamp: 'desc' },
      });

      // Convert to CSV format for compliance export
      const csvHeader = 'Timestamp,User ID,Action,Entity Type,IP Address,Risk Level,Details\n';
      const csvRows = logs.map(log => {
        const details = JSON.stringify(log.metadata).replace(/"/g, '""');
        return `${log.timestamp.toISOString()},${log.userId || 'system'},${log.action},${log.entityType},${log.ipAddress || 'unknown'},${log.riskLevel},"${details}"`;
      }).join('\n');

      return Buffer.from(csvHeader + csvRows, 'utf-8');

    } catch (error) {
      console.error('Failed to export audit logs:', error);
      throw new Error('Failed to export audit logs');
    }
  }

  /**
   * Build security context from request
   */
  private static async buildSecurityContext(req: Request): Promise<SecurityContext> {
    const ipAddress = this.getIPFromRequest(req);
    const userAgent = req.headers['user-agent'] || 'unknown';

    let geolocation;
    try {
      geolocation = await GeoLocationService.getLocation(ipAddress);
    } catch (error) {
      console.warn('Geolocation lookup failed for audit:', error);
    }

    const riskScore = geolocation ? await GeoLocationService.getThreatScore(ipAddress) : 50;
    const threatIndicators: string[] = [];

    if (geolocation?.isVPN) threatIndicators.push('vpn_detected');
    if (geolocation?.isTor) threatIndicators.push('tor_detected');
    if (userAgent.toLowerCase().includes('bot')) threatIndicators.push('bot_user_agent');

    return {
      ipAddress,
      userAgent,
      geolocation,
      riskScore,
      threatIndicators,
    };
  }

  /**
   * Extract IP address from request
   */
  private static getIPFromRequest(req?: Request): string {
    if (!req) return 'unknown';

    const forwarded = req.headers['x-forwarded-for'] as string;
    const real = req.headers['x-real-ip'] as string;
    return forwarded?.split(',')?.[0] || real || req.socket.remoteAddress || 'unknown';
  }

  /**
   * Sanitize details object to prevent injection and limit size
   */
  private static sanitizeDetails(details: Record<string, unknown>): Record<string, unknown> {
    const sanitized = { ...details };
    
    // Remove sensitive fields
    delete sanitized.password;
    delete sanitized.passwordHash;
    delete sanitized.secret;
    delete sanitized.token;

    // Limit size
    const jsonString = JSON.stringify(sanitized);
    if (jsonString.length > this.MAX_DETAILS_SIZE) {
      return { 
        ...sanitized, 
        _truncated: true,
        _originalSize: jsonString.length 
      };
    }

    return sanitized;
  }

  /**
   * Generate correlation ID for event tracking
   */
  private static generateCorrelationId(): string {
    return `aud_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }


  /**
   * Log high-risk events for immediate attention
   */
  private static async logHighRiskEvent(
    auditData: Record<string, unknown>,
    securityContext?: SecurityContext
  ): Promise<void> {
    try {
      // In production, this would integrate with alerting systems
      console.warn('HIGH-RISK EVENT DETECTED:', {
        userId: auditData.userId,
        eventType: auditData.eventType,
        riskScore: auditData.riskScore,
        ipAddress: auditData.ipAddress,
        threatIndicators: securityContext?.threatIndicators,
      });

      // Could trigger alerts to security team
      // await AlertingService.sendSecurityAlert(auditData);

    } catch (error) {
      console.error('Failed to log high-risk event:', error);
    }
  }

  /**
   * Analyze suspicious patterns in user activity
   */
  private static async analyzeSuspiciousPatterns(
    userId: string,
    ipAddress: string
  ): Promise<void> {
    try {
      const recentLogs = await prisma.auditLog.findMany({
        where: {
          userId,
          timestamp: {
            gte: new Date(Date.now() - 60 * 60 * 1000), // Last hour
          },
        },
        orderBy: { timestamp: 'desc' },
        take: 10,
      });

      // Check for rapid login attempts from different IPs
      const uniqueIPs = new Set(recentLogs.map(log => log.ipAddress));
      if (uniqueIPs.size > 3) {
        await this.logSecurityViolation(
          userId,
          {
            type: 'SUSPICIOUS_LOGIN_PATTERN',
            severity: 'HIGH',
            description: `Multiple IP addresses used within 1 hour: ${Array.from(uniqueIPs).join(', ')}`,
            mitigationAction: 'Account flagged for review',
          },
          {
            ipAddress,
            userAgent: 'pattern_analyzer',
            riskScore: 85,
            threatIndicators: ['multiple_ips_rapid_succession'],
          }
        );
      }

    } catch (error) {
      console.error('Failed to analyze suspicious patterns:', error);
    }
  }

  /**
   * Escalate critical security violations
   */
  private static async escalateCriticalViolation(
    userId: string,
    violation: SecurityViolation,
    securityContext: SecurityContext
  ): Promise<void> {
    try {
      // In production, this would:
      // 1. Send immediate alerts to security team
      // 2. Potentially block the user account temporarily
      // 3. Trigger automated incident response

      console.error('CRITICAL SECURITY VIOLATION:', {
        userId,
        violation,
        securityContext,
        timestamp: new Date().toISOString(),
      });

      // Could implement automatic user suspension for critical threats
      // await this.suspendUserForSecurity(userId, violation);

    } catch (error) {
      console.error('Failed to escalate critical violation:', error);
    }
  }

  /**
   * Fallback audit logging when enhanced logging fails
   */
  private static async fallbackAuditLog(
    eventData: AuditEventData,
    req?: Request
  ): Promise<void> {
    try {
      console.log('AUDIT EVENT (FALLBACK):', {
        action: eventData.action,
        userId: eventData.userId,
        timestamp: new Date().toISOString(),
        ipAddress: this.getIPFromRequest(req),
        details: this.sanitizeDetails(eventData.details),
      });
    } catch (error) {
      console.error('Even fallback audit logging failed:', error);
    }
  }
}