// Email Service
// BMad Agent: email-service-specialist
// Implements email delivery for invoice and notification systems

import * as nodemailer from 'nodemailer';
import { logger } from '../utils';

export interface EmailAttachment {
  filename: string;
  path?: string;
  content?: Buffer | string;
  contentType?: string;
}

export interface EmailOptions {
  to: string | string[];
  cc?: string | string[];
  bcc?: string | string[];
  subject: string;
  text?: string;
  html?: string;
  attachments?: EmailAttachment[];
  template?: string;
  templateData?: Record<string, unknown>;
}

/**
 * Email send result interface
 */
export interface EmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

/**
 * Invoice data interface
 */
export interface InvoiceData {
  invoiceNumber: string;
  customerName: string;
  amount: number;
  dueDate: Date;
  pdfPath?: string;
}

/**
 * Payment data interface
 */
export interface PaymentData {
  invoiceNumber: string;
  customerName: string;
  amount: number;
  paymentDate: Date;
  paymentMethod: string;
}

/**
 * Overdue invoice data interface
 */
export interface OverdueInvoiceData {
  invoiceNumber: string;
  customerName: string;
  amount: number;
  dueDate: Date;
  daysPastDue: number;
}

/**
 * Nodemailer result interface
 */
interface NodemailerResult {
  messageId: string;
  envelope: unknown;
  accepted: string[];
  rejected: string[];
  pending: string[];
  response: string;
}

export class EmailService {
  private transporter!: nodemailer.Transporter;
  private fromAddress: string;
  private fromName: string;

  constructor() {
    this.fromAddress =
      process.env.SMTP_FROM_ADDRESS || '<EMAIL>';
    this.fromName = process.env.SMTP_FROM_NAME || 'AI Underwriting Platform';

    this.initializeTransporter();
  }

  private initializeTransporter(): void {
    // Production SMTP configuration
    if (process.env.SMTP_HOST) {
      this.transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASSWORD,
        },
      });
    }
    // Development/testing configuration
    else if (process.env.NODE_ENV === 'development') {
      // Use Ethereal for development testing
      this.transporter = nodemailer.createTransport({
        host: 'smtp.ethereal.email',
        port: 587,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: 'ethereal.pass',
        },
      });
    }
    // Fallback to console logging
    else {
      this.transporter = nodemailer.createTransport({
        streamTransport: true,
        newline: 'unix',
        buffer: true,
      });
    }

    logger.info('Email service initialized', {
      transport: process.env.SMTP_HOST ? 'smtp' : 'development',
      fromAddress: this.fromAddress,
    });
  }

  /**
   * Send email
   */
  async sendEmail(options: EmailOptions): Promise<EmailResult> {
    try {
      const recipients = Array.isArray(options.to) ? options.to : [options.to];

      logger.info('Sending email', {
        to: recipients.length,
        subject: options.subject,
        hasAttachments: !!options.attachments?.length,
      });

      const mailOptions = {
        from: `${this.fromName} <${this.fromAddress}>`,
        to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
        cc: options.cc
          ? Array.isArray(options.cc)
            ? options.cc.join(', ')
            : options.cc
          : undefined,
        bcc: options.bcc
          ? Array.isArray(options.bcc)
            ? options.bcc.join(', ')
            : options.bcc
          : undefined,
        subject: options.subject,
        text: options.text,
        html: options.html,
        attachments: options.attachments,
      };

      const result = await this.transporter.sendMail(mailOptions) as NodemailerResult;

      logger.info('Email sent successfully', {
        messageId: result.messageId,
        recipients: recipients.length,
        subject: options.subject,
      });

      return {
        success: true,
        messageId: result.messageId,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error('Failed to send email', {
        error: errorMessage,
        to: options.to,
        subject: options.subject,
      });

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Send invoice email
   */
  async sendInvoiceEmail(
    to: string | string[],
    invoiceData: InvoiceData
  ): Promise<EmailResult> {
    const subject = `Invoice ${invoiceData.invoiceNumber} from AI Underwriting Platform`;

    const html = this.generateInvoiceEmailHTML(invoiceData);

    const attachments: EmailAttachment[] = [];
    if (invoiceData.pdfPath) {
      attachments.push({
        filename: `Invoice_${invoiceData.invoiceNumber}.pdf`,
        path: invoiceData.pdfPath,
        contentType: 'application/pdf',
      });
    }

    return await this.sendEmail({
      to,
      subject,
      html,
      attachments,
    });
  }

  /**
   * Send payment confirmation email
   */
  async sendPaymentConfirmationEmail(
    to: string | string[],
    paymentData: PaymentData
  ): Promise<EmailResult> {
    const subject = `Payment Confirmation - Invoice ${paymentData.invoiceNumber}`;

    const html = this.generatePaymentConfirmationHTML(paymentData);

    return await this.sendEmail({
      to,
      subject,
      html,
    });
  }

  /**
   * Send overdue invoice reminder
   */
  async sendOverdueReminderEmail(
    to: string | string[],
    invoiceData: OverdueInvoiceData
  ): Promise<EmailResult> {
    const subject = `Payment Reminder - Invoice ${invoiceData.invoiceNumber} is ${invoiceData.daysPastDue} days overdue`;

    const html = this.generateOverdueReminderHTML(invoiceData);

    return await this.sendEmail({
      to,
      subject,
      html,
    });
  }

  /**
   * Test email configuration
   */
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      await this.transporter.verify();

      logger.info('Email connection test successful');

      return { success: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error('Email connection test failed', {
        error: errorMessage,
      });

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  // Private helper methods for generating email templates

  private generateInvoiceEmailHTML(invoiceData: InvoiceData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #1f2937; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9fafb; }
          .invoice-details { background-color: white; padding: 20px; margin: 20px 0; border-radius: 8px; }
          .amount { font-size: 24px; font-weight: bold; color: #1f2937; }
          .footer { text-align: center; padding: 20px; color: #6b7280; font-size: 14px; }
          .button { display: inline-block; background-color: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Invoice ${invoiceData.invoiceNumber}</h1>
            <p>AI Underwriting Platform</p>
          </div>
          
          <div class="content">
            <p>Dear ${invoiceData.customerName},</p>
            
            <p>We hope this email finds you well. Please find your invoice attached for your AI Underwriting Platform subscription.</p>
            
            <div class="invoice-details">
              <h3>Invoice Details</h3>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;"><strong>Invoice Number:</strong></td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;">${invoiceData.invoiceNumber}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;"><strong>Due Date:</strong></td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;">${invoiceData.dueDate.toLocaleDateString()}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0;"><strong>Amount Due:</strong></td>
                  <td style="padding: 8px 0;" class="amount">$${invoiceData.amount.toFixed(2)}</td>
                </tr>
              </table>
            </div>
            
            <p>You can view and pay this invoice online through your account portal:</p>
            <div style="text-align: center;">
              <a href="https://app.ai-underwriting.com/billing" class="button">View Invoice & Pay Online</a>
            </div>
            
            <p><strong>Payment Options:</strong></p>
            <ul>
              <li>Online payment via your account portal</li>
              <li>Automatic payment if you have enabled auto-pay</li>
              <li>Contact our billing team for alternative payment methods</li>
            </ul>
            
            <p>If you have any questions about this invoice or need assistance, please don't hesitate to contact our support team.</p>
            
            <p>Thank you for choosing AI Underwriting Platform!</p>
          </div>
          
          <div class="footer">
            <p>AI Underwriting Platform | <EMAIL> | 1-800-AI-UNDERWRITE</p>
            <p>This is an automated message. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generatePaymentConfirmationHTML(paymentData: PaymentData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #059669; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f0fdf4; }
          .payment-details { background-color: white; padding: 20px; margin: 20px 0; border-radius: 8px; }
          .amount { font-size: 24px; font-weight: bold; color: #059669; }
          .footer { text-align: center; padding: 20px; color: #6b7280; font-size: 14px; }
          .checkmark { font-size: 48px; color: #059669; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="checkmark">✓</div>
            <h1>Payment Confirmed</h1>
            <p>Thank you for your payment!</p>
          </div>
          
          <div class="content">
            <p>Dear ${paymentData.customerName},</p>
            
            <p>We have successfully received your payment. Thank you for your prompt payment!</p>
            
            <div class="payment-details">
              <h3>Payment Details</h3>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;"><strong>Invoice Number:</strong></td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;">${paymentData.invoiceNumber}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;"><strong>Payment Date:</strong></td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;">${paymentData.paymentDate.toLocaleDateString()}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;"><strong>Payment Method:</strong></td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;">${paymentData.paymentMethod}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0;"><strong>Amount Paid:</strong></td>
                  <td style="padding: 8px 0;" class="amount">$${paymentData.amount.toFixed(2)}</td>
                </tr>
              </table>
            </div>
            
            <p>Your payment has been processed and applied to your account. Your services will continue without interruption.</p>
            
            <p>You can view your payment history and download receipts from your account portal at any time.</p>
            
            <p>Thank you for your business!</p>
          </div>
          
          <div class="footer">
            <p>AI Underwriting Platform | <EMAIL> | 1-800-AI-UNDERWRITE</p>
            <p>This is an automated message. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generateOverdueReminderHTML(invoiceData: OverdueInvoiceData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #dc2626; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #fef2f2; }
          .invoice-details { background-color: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #dc2626; }
          .amount { font-size: 24px; font-weight: bold; color: #dc2626; }
          .footer { text-align: center; padding: 20px; color: #6b7280; font-size: 14px; }
          .button { display: inline-block; background-color: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .urgent { background-color: #fee2e2; padding: 15px; border-radius: 6px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Payment Reminder</h1>
            <p>Invoice ${invoiceData.invoiceNumber} is ${invoiceData.daysPastDue} days overdue</p>
          </div>
          
          <div class="content">
            <p>Dear ${invoiceData.customerName},</p>
            
            <div class="urgent">
              <strong>⚠️ URGENT:</strong> Your payment is now ${invoiceData.daysPastDue} days overdue. 
              Please make payment immediately to avoid service interruption.
            </div>
            
            <p>Our records show that the following invoice remains unpaid:</p>
            
            <div class="invoice-details">
              <h3>Overdue Invoice</h3>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;"><strong>Invoice Number:</strong></td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;">${invoiceData.invoiceNumber}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;"><strong>Original Due Date:</strong></td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;">${invoiceData.dueDate.toLocaleDateString()}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;"><strong>Days Past Due:</strong></td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;">${invoiceData.daysPastDue} days</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0;"><strong>Amount Due:</strong></td>
                  <td style="padding: 8px 0;" class="amount">$${invoiceData.amount.toFixed(2)}</td>
                </tr>
              </table>
            </div>
            
            <p><strong>Please take immediate action:</strong></p>
            <div style="text-align: center;">
              <a href="https://app.ai-underwriting.com/billing" class="button">Pay Now</a>
            </div>
            
            <p><strong>Important:</strong> Continued non-payment may result in:</p>
            <ul>
              <li>Service suspension or termination</li>
              <li>Late payment fees</li>
              <li>Collection activities</li>
              <li>Negative impact on your credit rating</li>
            </ul>
            
            <p>If you have already made this payment, please disregard this notice. If you're experiencing financial difficulties or have questions about this invoice, please contact our billing team immediately.</p>
            
            <p>We value your business and want to work with you to resolve this matter promptly.</p>
          </div>
          
          <div class="footer">
            <p>AI Underwriting Platform | <EMAIL> | 1-800-AI-UNDERWRITE</p>
            <p>This is an automated message. Please contact us if you need assistance.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}
