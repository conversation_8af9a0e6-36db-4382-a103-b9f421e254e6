import { Prisma, Tenant, TenantStatus, SubscriptionTier } from '@prisma/client';
import { prisma } from '../prisma/client';

interface TenantWithRelations extends Tenant {
  users?: Array<{
    id: string;
    lastLoginAt?: Date | null;
  }>;
  applications?: Array<{
    id: string;
    createdAt?: Date;
    status?: string;
  }>;
  auditLogs?: Array<{
    action: string;
    timestamp: Date;
    userId: string | null;
  }>;
  _count?: {
    users?: number;
    applications?: number;
  };
}

export interface TenantManagementView {
  id: string;
  name: string;
  slug: string;
  status: TenantStatus;
  subscriptionTier: SubscriptionTier;
  healthScore: number; // 0-100
  monthlyUsage: UsageMetrics;
  lastActivity: Date;
  supportTickets: number;
  revenue: MonthlyRevenue;
  userCount: number;
  applicationCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface UsageMetrics {
  aiTokensUsed: number;
  aiTokensQuota: number;
  applicationsProcessed: number;
  activeUsers: number;
  apiCalls: number;
  storageUsed: number; // in MB
}

export interface MonthlyRevenue {
  current: number;
  previous: number;
  growth: number; // percentage
  forecasted: number;
}

export interface TenantSearchFilters {
  status?: TenantStatus[];
  subscriptionTier?: SubscriptionTier[];
  healthScoreMin?: number;
  healthScoreMax?: number;
  createdAfter?: Date;
  createdBefore?: Date;
  region?: string;
  revenueMin?: number;
  revenueMax?: number;
  search?: string; // Free text search across name, slug, domain
}

export interface TenantListOptions {
  filters?: TenantSearchFilters;
  sortBy?: 'name' | 'createdAt' | 'healthScore' | 'revenue' | 'lastActivity';
  sortDirection?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface TenantListResponse {
  tenants: TenantManagementView[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
  filters: TenantSearchFilters;
}

export interface BulkOperation {
  id: string;
  operation: BulkOperationType;
  tenantIds: string[];
  status: OperationStatus;
  progress: number; // 0-100
  results: OperationResult[];
  createdAt: Date;
  updatedAt: Date;
  createdBy: string; // System user ID
  metadata?: Record<string, unknown>;
}

export enum BulkOperationType {
  SUSPEND = 'suspend',
  ACTIVATE = 'activate',
  UPDATE_TIER = 'update_tier',
  SEND_NOTIFICATION = 'send_notification',
  EXPORT_DATA = 'export_data',
  DELETE = 'delete',
  MIGRATE_DATA = 'migrate_data',
}

export enum OperationStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export interface OperationResult {
  tenantId: string;
  tenantName: string;
  success: boolean;
  error?: string;
  details?: Record<string, unknown>;
  processedAt: Date;
}

export interface TenantHealthMetrics {
  healthScore: number;
  factors: HealthFactor[];
  lastCalculated: Date;
  trend: 'improving' | 'stable' | 'declining';
}

export interface HealthFactor {
  name: string;
  score: number; // 0-100
  weight: number; // 0-1
  description: string;
  impact: 'positive' | 'negative' | 'neutral';
}

/**
 * Cross-Tenant Management Service
 * Provides comprehensive tenant management capabilities for platform administrators
 */
export class TenantManagementService {
  private readonly DEFAULT_PAGE_SIZE = 25;
  private readonly MAX_PAGE_SIZE = 100;

  /**
   * Get paginated list of tenants with advanced filtering and search
   */
  async getTenantList(options: TenantListOptions = {}): Promise<TenantListResponse> {
    try {
      const {
        filters = {},
        sortBy = 'createdAt',
        sortDirection = 'desc',
        page = 1,
        limit = this.DEFAULT_PAGE_SIZE,
      } = options;

      const pageSize = Math.min(limit, this.MAX_PAGE_SIZE);
      const offset = (page - 1) * pageSize;

      // Build where clause from filters
      const whereClause = this.buildWhereClause(filters);

      // Build order by clause
      const orderBy = this.buildOrderByClause(sortBy, sortDirection);

      // Get total count
      const total = await prisma.tenant.count({ where: whereClause });

      // Get tenants with related data
      const tenants = await prisma.tenant.findMany({
        where: whereClause,
        orderBy,
        take: pageSize,
        skip: offset,
        include: {
          users: {
            select: { id: true },
          },
          applications: {
            select: { id: true },
            where: { status: { not: 'WITHDRAWN' } },
          },
          _count: {
            select: {
              users: true,
              applications: { where: { status: { not: 'WITHDRAWN' } } },
            },
          },
        },
      });

      // Transform to management view with enhanced metrics
      const tenantViews = await Promise.all(
        tenants.map(tenant => this.transformToManagementView(tenant))
      );

      return {
        tenants: tenantViews,
        total,
        page,
        limit: pageSize,
        hasMore: offset + pageSize < total,
        filters,
      };
    } catch (error) {
      console.error('Failed to get tenant list:', error);
      throw new Error('Failed to retrieve tenant list');
    }
  }

  /**
   * Get detailed tenant information including health metrics
   */
  async getTenantDetails(tenantId: string): Promise<TenantManagementView & {
    healthMetrics: TenantHealthMetrics;
    recentActivity: RecentActivity[];
  }> {
    try {
      const tenant = await prisma.tenant.findUnique({
        where: { id: tenantId },
        include: {
          users: {
            select: { id: true, lastLoginAt: true },
          },
          applications: {
            select: { id: true, createdAt: true, status: true },
            where: { status: { not: 'WITHDRAWN' } },
          },
          auditLogs: {
            select: { action: true, timestamp: true, userId: true },
            orderBy: { timestamp: 'desc' },
            take: 10,
          },
          _count: {
            select: {
              users: true,
              applications: { where: { status: { not: 'WITHDRAWN' } } },
            },
          },
        },
      });

      if (!tenant) {
        throw new Error('Tenant not found');
      }

      const managementView = await this.transformToManagementView(tenant);
      const healthMetrics = await this.calculateHealthMetrics(tenant);
      const recentActivity = this.extractRecentActivity(tenant);

      return {
        ...managementView,
        healthMetrics,
        recentActivity,
      };
    } catch (error) {
      console.error('Failed to get tenant details:', error);
      throw new Error('Failed to retrieve tenant details');
    }
  }

  /**
   * Search tenants using full-text search capabilities
   */
  async searchTenants(
    query: string,
    options: TenantListOptions = {}
  ): Promise<TenantListResponse> {
    try {
      const searchFilters: TenantSearchFilters = {
        ...options.filters,
        search: query,
      };

      return await this.getTenantList({
        ...options,
        filters: searchFilters,
      });
    } catch (error) {
      console.error('Failed to search tenants:', error);
      throw new Error('Failed to search tenants');
    }
  }

  /**
   * Update tenant status (activate, suspend, etc.)
   */
  async updateTenantStatus(
    tenantId: string,
    status: TenantStatus,
    reason: string,
    systemUserId: string
  ): Promise<{ id: string; status: TenantStatus }> {
    try {
      const tenant = await prisma.tenant.findUnique({
        where: { id: tenantId },
        select: { id: true, name: true, status: true },
      });

      if (!tenant) {
        throw new Error('Tenant not found');
      }

      // Update tenant status
      const updatedTenant = await prisma.tenant.update({
        where: { id: tenantId },
        data: { status, updatedAt: new Date() },
        select: { id: true, status: true },
      });

      // Log the status change
      await this.logTenantAction(
        tenantId,
        'STATUS_CHANGE',
        {
          previousStatus: tenant.status,
          newStatus: status,
          reason,
        },
        systemUserId
      );

      console.log(`Tenant ${tenant.name} status changed from ${tenant.status} to ${status}`);
      return updatedTenant;
    } catch (error) {
      console.error('Failed to update tenant status:', error);
      throw new Error('Failed to update tenant status');
    }
  }

  /**
   * Update tenant subscription tier
   */
  async updateTenantTier(
    tenantId: string,
    subscriptionTier: SubscriptionTier,
    systemUserId: string
  ): Promise<void> {
    try {
      const tenant = await prisma.tenant.findUnique({
        where: { id: tenantId },
        select: { id: true, name: true, subscriptionTier: true },
      });

      if (!tenant) {
        throw new Error('Tenant not found');
      }

      // Update subscription tier
      await prisma.tenant.update({
        where: { id: tenantId },
        data: { subscriptionTier, updatedAt: new Date() },
      });

      // Log the tier change
      await this.logTenantAction(
        tenantId,
        'TIER_CHANGE',
        {
          previousTier: tenant.subscriptionTier,
          newTier: subscriptionTier,
        },
        systemUserId
      );

      console.log(`Tenant ${tenant.name} tier changed from ${tenant.subscriptionTier} to ${subscriptionTier}`);
    } catch (error) {
      console.error('Failed to update tenant tier:', error);
      throw new Error('Failed to update tenant tier');
    }
  }

  /**
   * Get tenant analytics summary
   */
  async getTenantAnalytics(tenantId: string): Promise<{
    usage: UsageMetrics;
    revenue: MonthlyRevenue;
    growth: GrowthMetrics;
    performance: PerformanceMetrics;
  }> {
    try {
      const tenant = await prisma.tenant.findUnique({
        where: { id: tenantId },
        include: {
          users: {
            select: { id: true, lastLoginAt: true },
          },
          applications: {
            select: { id: true, createdAt: true, status: true },
            where: { 
              createdAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } 
            },
          },
        },
      });

      if (!tenant) {
        throw new Error('Tenant not found');
      }

      const usage = await this.calculateUsageMetrics(tenant);
      const revenue = await this.calculateRevenueMetrics(tenantId);
      const growth = await this.calculateGrowthMetrics(tenantId);
      const performance = await this.calculatePerformanceMetrics(tenantId);

      return { usage, revenue, growth, performance };
    } catch (error) {
      console.error('Failed to get tenant analytics:', error);
      throw new Error('Failed to retrieve tenant analytics');
    }
  }

  /**
   * Build where clause for tenant filtering
   */
  private buildWhereClause(filters: TenantSearchFilters): Prisma.TenantWhereInput {
    const where: Prisma.TenantWhereInput = {};

    if (filters.status && filters.status.length > 0) {
      where.status = { in: filters.status };
    }

    if (filters.subscriptionTier && filters.subscriptionTier.length > 0) {
      where.subscriptionTier = { in: filters.subscriptionTier };
    }

    if (filters.createdAfter || filters.createdBefore) {
      where.createdAt = {};
      if (filters.createdAfter) {
        where.createdAt.gte = filters.createdAfter;
      }
      if (filters.createdBefore) {
        where.createdAt.lte = filters.createdBefore;
      }
    }

    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { slug: { contains: filters.search, mode: 'insensitive' } },
        { domain: { contains: filters.search, mode: 'insensitive' } },
      ];
    }

    return where;
  }

  /**
   * Build order by clause for tenant sorting
   */
  private buildOrderByClause(
    sortBy: string,
    sortDirection: 'asc' | 'desc'
  ): Prisma.TenantOrderByWithRelationInput {
    switch (sortBy) {
      case 'name':
        return { name: sortDirection };
      case 'createdAt':
        return { createdAt: sortDirection };
      case 'healthScore':
        // For now, sort by updated date as proxy for health
        return { updatedAt: sortDirection };
      case 'revenue':
        // For now, sort by subscription tier as proxy for revenue
        return { subscriptionTier: sortDirection };
      case 'lastActivity':
        return { updatedAt: sortDirection };
      default:
        return { createdAt: sortDirection };
    }
  }

  /**
   * Transform tenant data to management view
   */
  private async transformToManagementView(tenant: TenantWithRelations): Promise<TenantManagementView> {
    const usage = await this.calculateUsageMetrics(tenant);
    const revenue = await this.calculateRevenueMetrics(tenant.id);
    const healthScore = await this.calculateHealthScore(tenant);
    const lastActivity = this.getLastActivity(tenant);

    return {
      id: tenant.id,
      name: tenant.name,
      slug: tenant.slug,
      status: tenant.status,
      subscriptionTier: tenant.subscriptionTier,
      healthScore,
      monthlyUsage: usage,
      lastActivity,
      supportTickets: 0, // TODO: Implement support ticket counting
      revenue,
      userCount: tenant._count?.users || tenant.users?.length || 0,
      applicationCount: tenant._count?.applications || tenant.applications?.length || 0,
      createdAt: tenant.createdAt,
      updatedAt: tenant.updatedAt,
    };
  }

  /**
   * Calculate usage metrics for a tenant
   */
  private async calculateUsageMetrics(tenant: TenantWithRelations): Promise<UsageMetrics> {
    return {
      aiTokensUsed: tenant.aiUsageCurrentMonth || 0,
      aiTokensQuota: tenant.aiQuotaMonthly || 10000,
      applicationsProcessed: tenant.applications?.length || 0,
      activeUsers: tenant.users?.length || 0,
      apiCalls: 0, // TODO: Implement API call tracking
      storageUsed: 0, // TODO: Implement storage tracking
    };
  }

  /**
   * Calculate revenue metrics for a tenant
   */
  private async calculateRevenueMetrics(tenantId: string): Promise<MonthlyRevenue> {
    // TODO: Implement actual revenue calculation from billing system
    // For now, return mock data based on subscription tier
    const tenant = await prisma.tenant.findUnique({
      where: { id: tenantId },
      select: { subscriptionTier: true },
    });

    const tierRevenue = {
      STARTER: 99,
      PROFESSIONAL: 299,
      ENTERPRISE: 999,
    };

    const current = tierRevenue[tenant?.subscriptionTier as keyof typeof tierRevenue] || 0;
    const previous = current * 0.95; // Mock 5% growth
    const growth = ((current - previous) / previous) * 100;
    const forecasted = current * 1.1; // Mock 10% growth forecast

    return {
      current,
      previous,
      growth,
      forecasted,
    };
  }

  /**
   * Calculate health score for a tenant
   */
  private async calculateHealthScore(tenant: TenantWithRelations): Promise<number> {
    let score = 100;

    // Deduct points for inactive status
    if (tenant.status === 'SUSPENDED') score -= 50;
    if (tenant.status === 'CHURNED') score -= 100;

    // Deduct points for low activity
    const daysSinceUpdate = Math.floor(
      (Date.now() - new Date(tenant.updatedAt).getTime()) / (1000 * 60 * 60 * 24)
    );
    if (daysSinceUpdate > 30) score -= 20;
    if (daysSinceUpdate > 60) score -= 30;

    // Deduct points for low usage
    const usageRatio = (tenant.aiUsageCurrentMonth || 0) / (tenant.aiQuotaMonthly || 1);
    if (usageRatio < 0.1) score -= 15; // Very low usage
    if (usageRatio < 0.05) score -= 25; // Extremely low usage

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Get last activity date for a tenant
   */
  private getLastActivity(tenant: TenantWithRelations): Date {
    const dates = [
      tenant.updatedAt,
      ...(tenant.users || []).map(u => u.lastLoginAt).filter(Boolean),
      ...(tenant.applications || []).map(a => a.createdAt).filter(Boolean),
    ].filter(Boolean);

    return dates.length > 0 ? new Date(Math.max(...dates.map(d => new Date(d as Date).getTime()))) : tenant.updatedAt;
  }

  /**
   * Calculate detailed health metrics
   */
  private async calculateHealthMetrics(tenant: TenantWithRelations): Promise<TenantHealthMetrics> {
    const factors: HealthFactor[] = [
      {
        name: 'Account Status',
        score: tenant.status === 'ACTIVE' ? 100 : tenant.status === 'TRIAL' ? 80 : 0,
        weight: 0.3,
        description: 'Current account status and standing',
        impact: tenant.status === 'ACTIVE' ? 'positive' : 'negative',
      },
      {
        name: 'Usage Activity',
        score: Math.min(100, ((tenant.aiUsageCurrentMonth || 0) / (tenant.aiQuotaMonthly || 1)) * 100),
        weight: 0.25,
        description: 'AI service usage relative to quota',
        impact: 'positive',
      },
      {
        name: 'User Engagement',
        score: Math.min(100, (tenant.users?.length || 0) * 20),
        weight: 0.2,
        description: 'Number of active users in the tenant',
        impact: 'positive',
      },
      {
        name: 'Recent Activity',
        score: this.calculateActivityScore(tenant),
        weight: 0.15,
        description: 'Recent platform activity and engagement',
        impact: 'positive',
      },
      {
        name: 'Application Portfolio',
        score: Math.min(100, (tenant.applications?.length || 0) * 10),
        weight: 0.1,
        description: 'Number of applications being processed',
        impact: 'positive',
      },
    ];

    const healthScore = factors.reduce((sum, factor) => sum + factor.score * factor.weight, 0);
    const trend = this.calculateHealthTrend(tenant);

    return {
      healthScore: Math.round(healthScore),
      factors,
      lastCalculated: new Date(),
      trend,
    };
  }

  /**
   * Calculate activity score based on recent activity
   */
  private calculateActivityScore(tenant: TenantWithRelations): number {
    const daysSinceUpdate = Math.floor(
      (Date.now() - new Date(tenant.updatedAt).getTime()) / (1000 * 60 * 60 * 24)
    );

    if (daysSinceUpdate <= 1) return 100;
    if (daysSinceUpdate <= 7) return 80;
    if (daysSinceUpdate <= 30) return 60;
    if (daysSinceUpdate <= 60) return 40;
    return 20;
  }

  /**
   * Calculate health trend
   */
  private calculateHealthTrend(tenant: TenantWithRelations): 'improving' | 'stable' | 'declining' {
    // Simplified trend calculation
    // In a real implementation, this would compare historical health scores
    const recentActivity = this.calculateActivityScore(tenant);
    if (recentActivity >= 80) return 'improving';
    if (recentActivity >= 60) return 'stable';
    return 'declining';
  }

  /**
   * Extract recent activity from tenant data
   */
  private extractRecentActivity(tenant: TenantWithRelations): RecentActivity[] {
    const activities: RecentActivity[] = [];

    // Add audit log activities
    if (tenant.auditLogs) {
      activities.push(
        ...tenant.auditLogs.map(log => ({
          type: 'audit',
          description: `${log.action} action performed`,
          timestamp: log.timestamp,
          userId: log.userId,
        }))
      );
    }

    // Add user login activities
    if (tenant.users) {
      tenant.users
        .filter(u => u.lastLoginAt)
        .forEach(user => {
          activities.push({
            type: 'user_login',
            description: 'User logged in',
            timestamp: user.lastLoginAt as Date,
            userId: user.id,
          });
        });
    }

    // Sort by timestamp descending and limit to 20 most recent
    return activities
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 20);
  }

  /**
   * Log tenant management action
   */
  private async logTenantAction(
    tenantId: string,
    action: string,
    details: Record<string, unknown>,
    systemUserId: string
  ): Promise<void> {
    try {
      await prisma.auditLog.create({
        data: {
          tenantId,
          action,
          entityType: 'Tenant',
          entityId: tenantId,
          userId: systemUserId,
          newValues: details as Prisma.InputJsonValue,
          metadata: {
            source: 'tenant_management',
            timestamp: new Date(),
          } as Prisma.InputJsonValue,
          reason: `Tenant management action: ${action}`,
          riskLevel: 'LOW',
        },
      });
    } catch (error) {
      console.error('Failed to log tenant action:', error);
      // Don't throw error for logging failures
    }
  }

  /**
   * Calculate growth metrics (placeholder implementation)
   */
  private async calculateGrowthMetrics(_tenantId: string): Promise<GrowthMetrics> {
    // TODO: Implement actual growth metrics calculation
    return {
      userGrowth: 5.2,
      applicationGrowth: 12.8,
      revenueGrowth: 8.1,
      engagementGrowth: 3.4,
    };
  }

  /**
   * Calculate performance metrics (placeholder implementation)
   */
  private async calculatePerformanceMetrics(_tenantId: string): Promise<PerformanceMetrics> {
    // TODO: Implement actual performance metrics calculation
    return {
      averageProcessingTime: 2.3,
      successRate: 98.7,
      apiResponseTime: 145,
      errorRate: 1.3,
    };
  }
}

// Additional interfaces for completeness
export interface RecentActivity {
  type: string;
  description: string;
  timestamp: Date;
  userId?: string | null;
}

export interface GrowthMetrics {
  userGrowth: number;
  applicationGrowth: number;
  revenueGrowth: number;
  engagementGrowth: number;
}

export interface PerformanceMetrics {
  averageProcessingTime: number;
  successRate: number;
  apiResponseTime: number;
  errorRate: number;
}

// Export service instance
export const tenantManagementService = new TenantManagementService();