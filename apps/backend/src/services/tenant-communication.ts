/**
 * Tenant Communication Service
 * Provides tenant communication and notification tools with template management
 */

import { prisma } from '../prisma/client';
import nodemailer from 'nodemailer';
// import { auditService } from './audit-enhanced';

export interface CommunicationTemplate {
  id: string;
  name: string;
  type: TemplateType;
  subject: string;
  bodyText: string;
  bodyHtml?: string;
  variables: TemplateVariable[];
  category: TemplateCategory;
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  usage: TemplateUsage;
}

export enum TemplateType {
  EMAIL = 'email',
  SMS = 'sms',
  IN_APP = 'in_app',
  PUSH = 'push'
}

export enum TemplateCategory {
  ONBOARDING = 'onboarding',
  BILLING = 'billing',
  SUPPORT = 'support',
  MARKETING = 'marketing',
  SYSTEM = 'system',
  COMPLIANCE = 'compliance'
}

export interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'date' | 'boolean';
  description: string;
  required: boolean;
  defaultValue?: string;
  validation?: string;
}

export interface TemplateUsage {
  totalSent: number;
  lastUsed?: Date;
  successRate: number;
  averageOpenRate?: number;
  averageClickRate?: number;
}

export interface CommunicationCampaign {
  id: string;
  name: string;
  description: string;
  templateId: string;
  targetCriteria: TargetCriteria;
  schedule: CampaignSchedule;
  status: CampaignStatus;
  metrics: CampaignMetrics;
  createdBy: string;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
}

export enum CampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  RUNNING = 'running',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export interface TargetCriteria {
  tenantIds?: string[];
  subscriptionTiers?: string[];
  regions?: string[];
  healthScoreRange?: { min: number; max: number };
  activityDateRange?: { start: Date; end: Date };
  customFilters?: Record<string, unknown>;
}

export interface CampaignSchedule {
  type: 'immediate' | 'scheduled' | 'recurring';
  scheduledAt?: Date;
  timezone?: string;
  recurringPattern?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    interval: number;
    endDate?: Date;
  };
}

export interface CampaignMetrics {
  targetCount: number;
  sentCount: number;
  deliveredCount: number;
  openedCount: number;
  clickedCount: number;
  failedCount: number;
  unsubscribedCount: number;
  deliveryRate: number;
  openRate: number;
  clickRate: number;
}

export interface CommunicationLog {
  id: string;
  tenantId: string;
  userId?: string;
  templateId: string;
  campaignId?: string;
  type: TemplateType;
  subject: string;
  recipient: string;
  status: MessageStatus;
  sentAt: Date;
  deliveredAt?: Date;
  openedAt?: Date;
  clickedAt?: Date;
  errorMessage?: string;
  metadata: Record<string, unknown>;
}

export enum MessageStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  OPENED = 'opened',
  CLICKED = 'clicked',
  FAILED = 'failed',
  BOUNCED = 'bounced',
  UNSUBSCRIBED = 'unsubscribed'
}

export interface BulkCommunicationJob {
  id: string;
  name: string;
  templateId: string;
  targetTenants: string[];
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  totalCount: number;
  successCount: number;
  failureCount: number;
  startedAt?: Date;
  completedAt?: Date;
  createdBy: string;
  results: BulkCommunicationResult[];
}

export interface BulkCommunicationResult {
  tenantId: string;
  status: 'success' | 'failed';
  messageId?: string;
  errorMessage?: string;
  sentAt?: Date;
}

class TenantCommunicationService {
  private transporter: nodemailer.Transporter;

  constructor() {
    // Initialize email transporter
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || 'localhost',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
  }

  /**
   * Create or update communication template
   */
  async createTemplate(template: Omit<CommunicationTemplate, 'id' | 'createdAt' | 'updatedAt' | 'usage'>): Promise<CommunicationTemplate> {
    const newTemplate: CommunicationTemplate = {
      ...template,
      id: `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      usage: {
        totalSent: 0,
        successRate: 0
      }
    };

    // Log template creation (audit service temporarily disabled)
    console.log('Template created:', {
      templateName: template.name,
      templateType: template.type,
      category: template.category,
      createdBy: template.createdBy
    });

    return newTemplate;
  }

  /**
   * Get all communication templates
   */
  async getTemplates(filters: {
    type?: TemplateType;
    category?: TemplateCategory;
    isActive?: boolean;
  } = {}): Promise<CommunicationTemplate[]> {
    // Mock implementation - would fetch from database
    const templates: CommunicationTemplate[] = [
      {
        id: 'template_welcome',
        name: 'Welcome Email',
        type: TemplateType.EMAIL,
        subject: 'Welcome to {{platform_name}}!',
        bodyText: 'Dear {{tenant_name}}, welcome to our platform!',
        bodyHtml: '<h1>Welcome {{tenant_name}}!</h1><p>We\'re excited to have you on board.</p>',
        variables: [
          { name: 'tenant_name', type: 'string', description: 'Tenant company name', required: true },
          { name: 'platform_name', type: 'string', description: 'Platform name', required: true, defaultValue: 'OLA Platform' }
        ],
        category: TemplateCategory.ONBOARDING,
        isActive: true,
        createdBy: 'system',
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        usage: {
          totalSent: 150,
          lastUsed: new Date(),
          successRate: 98.5,
          averageOpenRate: 85.2,
          averageClickRate: 12.4
        }
      }
    ];

    return templates.filter(template => {
      if (filters.type && template.type !== filters.type) return false;
      if (filters.category && template.category !== filters.category) return false;
      if (filters.isActive !== undefined && template.isActive !== filters.isActive) return false;
      return true;
    });
  }

  /**
   * Send communication to single tenant
   */
  async sendToTenant(
    tenantId: string,
    templateId: string,
    variables: Record<string, unknown> = {},
    userId?: string
  ): Promise<CommunicationLog> {
    const tenant = await prisma.tenant.findUnique({
      where: { id: tenantId },
      include: { users: true }
    });

    if (!tenant) {
      throw new Error(`Tenant not found: ${tenantId}`);
    }

    const template = await this.getTemplateById(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }

    // Get primary contact for tenant
    const primaryContact = tenant.users.find(user => user.role === 'ADMIN') || tenant.users[0];
    if (!primaryContact) {
      throw new Error(`No contact found for tenant: ${tenantId}`);
    }

    const recipient = primaryContact.email;
    const processedContent = this.processTemplate(template, variables);

    const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      // Send email
      if (template.type === TemplateType.EMAIL) {
        await this.transporter.sendMail({
          from: process.env.SMTP_FROM || '<EMAIL>',
          to: recipient,
          subject: processedContent.subject,
          text: processedContent.bodyText,
          html: processedContent.bodyHtml
        });
      }

      const communicationLog: CommunicationLog = {
        id: messageId,
        tenantId,
        userId,
        templateId,
        type: template.type,
        subject: processedContent.subject,
        recipient,
        status: MessageStatus.SENT,
        sentAt: new Date(),
        metadata: { variables }
      };

      // Log communication (audit service temporarily disabled)
      console.log('Communication sent:', {
        userId: userId || 'system',
        tenantId,
        templateId,
        templateName: template.name,
        recipient,
        messageId
      });

      return communicationLog;

    } catch (error) {
      const communicationLog: CommunicationLog = {
        id: messageId,
        tenantId,
        userId,
        templateId,
        type: template.type,
        subject: processedContent.subject,
        recipient,
        status: MessageStatus.FAILED,
        sentAt: new Date(),
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        metadata: { variables }
      };

      return communicationLog;
    }
  }

  /**
   * Send bulk communication to multiple tenants
   */
  async sendBulkCommunication(
    templateId: string,
    targetCriteria: TargetCriteria,
    variables: Record<string, unknown> = {},
    userId: string,
    jobName?: string
  ): Promise<BulkCommunicationJob> {
    const targetTenants = await this.getTargetTenants(targetCriteria);
    
    const job: BulkCommunicationJob = {
      id: `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: jobName || `Bulk Communication ${new Date().toISOString()}`,
      templateId,
      targetTenants: targetTenants.map(t => t.id),
      status: 'pending',
      progress: 0,
      totalCount: targetTenants.length,
      successCount: 0,
      failureCount: 0,
      createdBy: userId,
      results: []
    };

    // Process bulk communication asynchronously
    this.processBulkCommunication(job, variables);

    // Log bulk communication start (audit service temporarily disabled)
    console.log('Bulk communication started:', {
      userId,
      jobId: job.id,
      templateId,
      targetCount: targetTenants.length,
      jobName: job.name
    });

    return job;
  }

  /**
   * Create and execute communication campaign
   */
  async createCampaign(campaign: Omit<CommunicationCampaign, 'id' | 'createdAt' | 'metrics'>): Promise<CommunicationCampaign> {
    const newCampaign: CommunicationCampaign = {
      ...campaign,
      id: `campaign_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date(),
      metrics: {
        targetCount: 0,
        sentCount: 0,
        deliveredCount: 0,
        openedCount: 0,
        clickedCount: 0,
        failedCount: 0,
        unsubscribedCount: 0,
        deliveryRate: 0,
        openRate: 0,
        clickRate: 0
      }
    };

    // Calculate target count
    const targetTenants = await this.getTargetTenants(campaign.targetCriteria);
    newCampaign.metrics.targetCount = targetTenants.length;

    // Log campaign creation (audit service temporarily disabled)
    console.log('Campaign created:', {
      userId: campaign.createdBy,
      campaignId: newCampaign.id,
      campaignName: campaign.name,
      templateId: campaign.templateId,
      targetCount: targetTenants.length
    });

    return newCampaign;
  }

  /**
   * Get communication logs for a tenant
   */
  async getTenantCommunicationHistory(
    tenantId: string,
    options: {
      limit?: number;
      offset?: number;
      type?: TemplateType;
      status?: MessageStatus;
    } = {}
  ): Promise<{ logs: CommunicationLog[]; total: number }> {
    // Mock implementation - would query database
    const mockLogs: CommunicationLog[] = [
      {
        id: 'log_1',
        tenantId,
        templateId: 'template_welcome',
        type: TemplateType.EMAIL,
        subject: 'Welcome to OLA Platform!',
        recipient: '<EMAIL>',
        status: MessageStatus.DELIVERED,
        sentAt: new Date('2024-01-15'),
        deliveredAt: new Date('2024-01-15'),
        openedAt: new Date('2024-01-15'),
        metadata: {}
      }
    ];

    const filteredLogs = mockLogs.filter(log => {
      if (options.type && log.type !== options.type) return false;
      if (options.status && log.status !== options.status) return false;
      return true;
    });

    const offset = options.offset || 0;
    const limit = options.limit || 50;
    const paginatedLogs = filteredLogs.slice(offset, offset + limit);

    return {
      logs: paginatedLogs,
      total: filteredLogs.length
    };
  }

  /**
   * Get communication analytics
   */
  async getCommunicationAnalytics(_filters: {
    dateRange?: { start: Date; end: Date };
    templateIds?: string[];
    tenantIds?: string[];
  } = {}): Promise<{
    summary: {
      totalSent: number;
      deliveryRate: number;
      openRate: number;
      clickRate: number;
    };
    byTemplate: Array<{
      templateId: string;
      templateName: string;
      sent: number;
      delivered: number;
      opened: number;
      clicked: number;
    }>;
    trends: Array<{
      date: Date;
      sent: number;
      delivered: number;
      opened: number;
    }>;
  }> {
    // Mock analytics data
    return {
      summary: {
        totalSent: 1250,
        deliveryRate: 98.2,
        openRate: 82.5,
        clickRate: 15.3
      },
      byTemplate: [
        {
          templateId: 'template_welcome',
          templateName: 'Welcome Email',
          sent: 150,
          delivered: 148,
          opened: 125,
          clicked: 18
        }
      ],
      trends: Array.from({ length: 30 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - (29 - i));
        return {
          date,
          sent: Math.floor(Math.random() * 50) + 10,
          delivered: Math.floor(Math.random() * 45) + 10,
          opened: Math.floor(Math.random() * 35) + 5
        };
      })
    };
  }

  private async getTemplateById(templateId: string): Promise<CommunicationTemplate | null> {
    const templates = await this.getTemplates();
    return templates.find(t => t.id === templateId) || null;
  }

  private processTemplate(
    template: CommunicationTemplate,
    variables: Record<string, unknown>
  ): { subject: string; bodyText: string; bodyHtml?: string } {
    let subject = template.subject;
    let bodyText = template.bodyText;
    let bodyHtml = template.bodyHtml;

    // Replace variables in template
    template.variables.forEach(variable => {
      const value = String(variables[variable.name] || variable.defaultValue || '');
      const placeholder = `{{${variable.name}}}`;
      
      subject = subject.replace(new RegExp(placeholder, 'g'), value);
      bodyText = bodyText.replace(new RegExp(placeholder, 'g'), value);
      if (bodyHtml) {
        bodyHtml = bodyHtml.replace(new RegExp(placeholder, 'g'), value);
      }
    });

    return { subject, bodyText, bodyHtml };
  }

  private async getTargetTenants(criteria: TargetCriteria): Promise<Array<{ id: string; name: string }>> {
    const whereClause: Record<string, unknown> = {};

    if (criteria.tenantIds) {
      whereClause.id = { in: criteria.tenantIds };
    }

    if (criteria.subscriptionTiers) {
      whereClause.subscriptions = {
        some: {
          tier: { in: criteria.subscriptionTiers }
        }
      };
    }

    const tenants = await prisma.tenant.findMany({
      where: whereClause,
      select: { id: true, name: true }
    });

    return tenants;
  }

  private async processBulkCommunication(
    job: BulkCommunicationJob,
    variables: Record<string, unknown>
  ): Promise<void> {
    job.status = 'running';
    job.startedAt = new Date();

    for (let i = 0; i < job.targetTenants.length; i++) {
      const tenantId = job.targetTenants[i];
      if (!tenantId) {
        console.error('Undefined tenant ID at index', i);
        continue;
      }
      
      try {
        const result = await this.sendToTenant(tenantId, job.templateId, variables, job.createdBy);
        
        job.results.push({
          tenantId: tenantId,
          status: result.status === MessageStatus.FAILED ? 'failed' : 'success',
          messageId: result.id || 'unknown',
          errorMessage: result.errorMessage,
          sentAt: result.sentAt
        });

        if (result.status !== MessageStatus.FAILED) {
          job.successCount++;
        } else {
          job.failureCount++;
        }

      } catch (error) {
        job.results.push({
          tenantId: tenantId,
          status: 'failed',
          errorMessage: error instanceof Error ? error.message : 'Unknown error'
        });
        job.failureCount++;
      }

      job.progress = Math.round(((i + 1) / job.targetTenants.length) * 100);
    }

    job.status = 'completed';
    job.completedAt = new Date();

    // Log bulk communication completion (audit service temporarily disabled)
    console.log('Bulk communication completed:', {
      userId: job.createdBy,
      jobId: job.id,
      totalCount: job.totalCount,
      successCount: job.successCount,
      failureCount: job.failureCount
    });
  }
}

export const tenantCommunicationService = new TenantCommunicationService();