import { prisma } from '../prisma/client';
import { TenantStatus, SubscriptionTier } from '@prisma/client';
import { BulkOperation, BulkOperationType, OperationStatus, OperationResult } from './tenant-management';
export { BulkOperationType, OperationStatus };
import { EnhancedAuditService } from './audit-enhanced';

export interface BulkOperationRequest {
  operation: BulkOperationType;
  tenantIds: string[];
  parameters?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
}

export interface BulkOperationProgress {
  id: string;
  operation: BulkOperationType;
  totalTenants: number;
  processedTenants: number;
  successfulTenants: number;
  failedTenants: number;
  progress: number; // 0-100
  status: OperationStatus;
  estimatedTimeRemaining?: number; // in seconds
  startedAt: Date;
  updatedAt: Date;
}

export interface BulkStatusUpdateRequest {
  newStatus: TenantStatus;
  reason: string;
}

export interface BulkTierUpdateRequest {
  newTier: SubscriptionTier;
  reason: string;
}

export interface BulkNotificationRequest {
  subject: string;
  message: string;
  templateId?: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
}

/**
 * Bulk Operations Service
 * Handles bulk operations on multiple tenants with progress tracking and error handling
 */
export class BulkOperationsService {
  private readonly MAX_BATCH_SIZE = 100;
  private readonly BATCH_PROCESSING_DELAY = 100; // ms between batches
  private operations = new Map<string, BulkOperation>();

  /**
   * Start a bulk operation on multiple tenants
   */
  async startBulkOperation(
    request: BulkOperationRequest,
    systemUserId: string
  ): Promise<string> {
    try {
      // Validate request
      this.validateBulkRequest(request);

      // Create operation record
      const operationId = this.generateOperationId();
      const operation: BulkOperation = {
        id: operationId,
        operation: request.operation,
        tenantIds: request.tenantIds,
        status: OperationStatus.PENDING,
        progress: 0,
        results: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: systemUserId,
        metadata: request.metadata,
      };

      // Store operation in memory and database
      this.operations.set(operationId, operation);
      await this.persistOperation(operation);

      // Log the operation start
      await EnhancedAuditService.logAuthEvent({
        action: 'BULK_OPERATION_STARTED',
        entityType: 'BulkOperation',
        entityId: operationId,
        userId: systemUserId,
        details: {
          operation: request.operation,
          tenantCount: request.tenantIds.length,
          parameters: request.parameters,
        },
        riskLevel: 'MEDIUM',
      });

      // Start processing asynchronously
      this.processBulkOperation(operationId, request, systemUserId).catch(error => {
        console.error(`Bulk operation ${operationId} failed:`, error);
        this.updateOperationStatus(operationId, OperationStatus.FAILED);
      });

      return operationId;
    } catch (error) {
      console.error('Failed to start bulk operation:', error);
      throw new Error('Failed to start bulk operation');
    }
  }

  /**
   * Get bulk operation progress
   */
  async getBulkOperationProgress(operationId: string): Promise<BulkOperationProgress | null> {
    try {
      const operation = this.operations.get(operationId);
      if (!operation) {
        // Try to load from database
        const dbOperation = await this.loadOperationFromDatabase(operationId);
        if (dbOperation) {
          this.operations.set(operationId, dbOperation);
          return this.transformToProgress(dbOperation);
        }
        return null;
      }

      return this.transformToProgress(operation);
    } catch (error) {
      console.error('Failed to get bulk operation progress:', error);
      return null;
    }
  }

  /**
   * Cancel a bulk operation
   */
  async cancelBulkOperation(operationId: string, systemUserId: string): Promise<boolean> {
    try {
      const operation = this.operations.get(operationId);
      if (!operation) {
        return false;
      }

      if (operation.status === OperationStatus.COMPLETED || operation.status === OperationStatus.FAILED) {
        return false; // Cannot cancel completed operations
      }

      operation.status = OperationStatus.CANCELLED;
      operation.updatedAt = new Date();

      await this.persistOperation(operation);

      // Log the cancellation
      await EnhancedAuditService.logAuthEvent({
        action: 'BULK_OPERATION_CANCELLED',
        entityType: 'BulkOperation',
        entityId: operationId,
        userId: systemUserId,
        details: {
          operation: operation.operation,
          progress: operation.progress,
          processedTenants: operation.results.length,
        },
        riskLevel: 'LOW',
      });

      return true;
    } catch (error) {
      console.error('Failed to cancel bulk operation:', error);
      return false;
    }
  }

  /**
   * Get bulk operation results
   */
  async getBulkOperationResults(operationId: string): Promise<OperationResult[]> {
    try {
      const operation = this.operations.get(operationId) || await this.loadOperationFromDatabase(operationId);
      return operation?.results || [];
    } catch (error) {
      console.error('Failed to get bulk operation results:', error);
      return [];
    }
  }

  /**
   * Get active bulk operations
   */
  async getActiveBulkOperations(): Promise<BulkOperationProgress[]> {
    try {
      const activeOperations = Array.from(this.operations.values())
        .filter(op => op.status === OperationStatus.PENDING || op.status === OperationStatus.IN_PROGRESS);

      return activeOperations.map(op => this.transformToProgress(op));
    } catch (error) {
      console.error('Failed to get active bulk operations:', error);
      return [];
    }
  }

  /**
   * Process bulk operation asynchronously
   */
  private async processBulkOperation(
    operationId: string,
    request: BulkOperationRequest,
    systemUserId: string
  ): Promise<void> {
    const operation = this.operations.get(operationId);
    if (!operation) return;

    try {
      operation.status = OperationStatus.IN_PROGRESS;
      operation.updatedAt = new Date();

      const tenantBatches = this.chunkArray(request.tenantIds, this.MAX_BATCH_SIZE);
      let processedCount = 0;

      for (const batch of tenantBatches) {
        // Re-check operation status in case it was cancelled
        const currentOperation = this.operations.get(operationId);
        if (currentOperation?.status === OperationStatus.CANCELLED) {
          operation.status = OperationStatus.CANCELLED;
          break;
        }

        // Process batch
        const batchResults = await this.processBatch(batch, request, systemUserId);
        operation.results.push(...batchResults);

        processedCount += batch.length;
        operation.progress = Math.round((processedCount / request.tenantIds.length) * 100);
        operation.updatedAt = new Date();

        // Persist progress
        await this.persistOperation(operation);

        // Small delay between batches to prevent overwhelming the system
        if (tenantBatches.length > 1) {
          await new Promise(resolve => setTimeout(resolve, this.BATCH_PROCESSING_DELAY));
        }
      }

      // Mark operation as completed
      if (operation.status !== OperationStatus.CANCELLED) {
        operation.status = OperationStatus.COMPLETED;
      }
      operation.progress = 100;
      operation.updatedAt = new Date();

      await this.persistOperation(operation);

      // Log completion
      await EnhancedAuditService.logAuthEvent({
        action: 'BULK_OPERATION_COMPLETED',
        entityType: 'BulkOperation',
        entityId: operationId,
        userId: systemUserId,
        details: {
          operation: request.operation,
          totalTenants: request.tenantIds.length,
          successfulTenants: operation.results.filter(r => r.success).length,
          failedTenants: operation.results.filter(r => !r.success).length,
          status: operation.status,
        },
        riskLevel: 'LOW',
      });

    } catch (error) {
      console.error(`Bulk operation ${operationId} processing failed:`, error);
      operation.status = OperationStatus.FAILED;
      operation.updatedAt = new Date();
      await this.persistOperation(operation);
    }
  }

  /**
   * Process a batch of tenants
   */
  private async processBatch(
    tenantIds: string[],
    request: BulkOperationRequest,
    systemUserId: string
  ): Promise<OperationResult[]> {
    const results: OperationResult[] = [];

    for (const tenantId of tenantIds) {
      try {
        const tenant = await prisma.tenant.findUnique({
          where: { id: tenantId },
          select: { id: true, name: true, status: true, subscriptionTier: true },
        });

        if (!tenant) {
          results.push({
            tenantId,
            tenantName: 'Unknown',
            success: false,
            error: 'Tenant not found',
            processedAt: new Date(),
          });
          continue;
        }

        const result = await this.processSingleTenant(tenant, request, systemUserId);
        results.push(result);

      } catch (error) {
        results.push({
          tenantId,
          tenantName: 'Unknown',
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          processedAt: new Date(),
        });
      }
    }

    return results;
  }

  /**
   * Process a single tenant operation
   */
  private async processSingleTenant(
    tenant: { id: string; name: string; status: TenantStatus; subscriptionTier: SubscriptionTier },
    request: BulkOperationRequest,
    systemUserId: string
  ): Promise<OperationResult> {
    const result: OperationResult = {
      tenantId: tenant.id,
      tenantName: tenant.name,
      success: false,
      processedAt: new Date(),
    };

    try {
      switch (request.operation) {
        case BulkOperationType.SUSPEND:
          await this.processSuspendTenant(tenant, request.parameters, systemUserId);
          break;

        case BulkOperationType.ACTIVATE:
          await this.processActivateTenant(tenant, request.parameters, systemUserId);
          break;

        case BulkOperationType.UPDATE_TIER:
          await this.processUpdateTier(tenant, request.parameters, systemUserId);
          break;

        case BulkOperationType.SEND_NOTIFICATION:
          await this.processSendNotification(tenant, request.parameters, systemUserId);
          break;

        case BulkOperationType.EXPORT_DATA:
          await this.processExportData(tenant, request.parameters, systemUserId);
          break;

        default:
          throw new Error(`Unsupported operation: ${request.operation}`);
      }

      result.success = true;
    } catch (error) {
      result.error = error instanceof Error ? error.message : 'Unknown error';
    }

    return result;
  }

  /**
   * Process suspend tenant operation
   */
  private async processSuspendTenant(
    tenant: { id: string; name: string; status: TenantStatus },
    parameters: Record<string, unknown> | undefined,
    systemUserId: string
  ): Promise<void> {
    const reason = parameters?.reason as string || 'Bulk suspension';

    if (tenant.status === TenantStatus.SUSPENDED) {
      throw new Error('Tenant is already suspended');
    }

    await prisma.tenant.update({
      where: { id: tenant.id },
      data: { 
        status: TenantStatus.SUSPENDED,
        updatedAt: new Date(),
      },
    });

    // Log the action
    await EnhancedAuditService.logAuthEvent({
      action: 'TENANT_SUSPENDED',
      entityType: 'Tenant',
      entityId: tenant.id,
      userId: systemUserId,
      details: {
        previousStatus: tenant.status,
        reason,
        bulkOperation: true,
      },
      riskLevel: 'HIGH',
    });
  }

  /**
   * Process activate tenant operation
   */
  private async processActivateTenant(
    tenant: { id: string; name: string; status: TenantStatus },
    parameters: Record<string, unknown> | undefined,
    systemUserId: string
  ): Promise<void> {
    const reason = parameters?.reason as string || 'Bulk activation';

    if (tenant.status === TenantStatus.ACTIVE) {
      throw new Error('Tenant is already active');
    }

    await prisma.tenant.update({
      where: { id: tenant.id },
      data: { 
        status: TenantStatus.ACTIVE,
        updatedAt: new Date(),
      },
    });

    // Log the action
    await EnhancedAuditService.logAuthEvent({
      action: 'TENANT_ACTIVATED',
      entityType: 'Tenant',
      entityId: tenant.id,
      userId: systemUserId,
      details: {
        previousStatus: tenant.status,
        reason,
        bulkOperation: true,
      },
      riskLevel: 'MEDIUM',
    });
  }

  /**
   * Process update tier operation
   */
  private async processUpdateTier(
    tenant: { id: string; subscriptionTier: string },
    parameters: Record<string, unknown> | undefined,
    systemUserId: string
  ): Promise<void> {
    const newTier = parameters?.newTier as SubscriptionTier;
    const reason = parameters?.reason as string || 'Bulk tier update';

    if (!newTier) {
      throw new Error('New tier not specified');
    }

    if (tenant.subscriptionTier === newTier) {
      throw new Error(`Tenant is already on ${newTier} tier`);
    }

    await prisma.tenant.update({
      where: { id: tenant.id },
      data: { 
        subscriptionTier: newTier,
        updatedAt: new Date(),
      },
    });

    // Log the action
    await EnhancedAuditService.logAuthEvent({
      action: 'TENANT_TIER_UPDATED',
      entityType: 'Tenant',
      entityId: tenant.id,
      userId: systemUserId,
      details: {
        previousTier: tenant.subscriptionTier,
        newTier,
        reason,
        bulkOperation: true,
      },
      riskLevel: 'MEDIUM',
    });
  }

  /**
   * Process send notification operation
   */
  private async processSendNotification(
    tenant: { id: string; name: string },
    parameters: Record<string, unknown> | undefined,
    systemUserId: string
  ): Promise<void> {
    const subject = parameters?.subject as string;
    const message = parameters?.message as string;

    if (!subject || !message) {
      throw new Error('Subject and message are required for notifications');
    }

    // TODO: Implement actual notification sending
    // For now, just log the action
    console.log(`Sending notification to tenant ${tenant.name}: ${subject}`);

    // Log the action
    await EnhancedAuditService.logAuthEvent({
      action: 'TENANT_NOTIFICATION_SENT',
      entityType: 'Tenant',
      entityId: tenant.id,
      userId: systemUserId,
      details: {
        subject,
        messageLength: message.length,
        bulkOperation: true,
      },
      riskLevel: 'LOW',
    });
  }

  /**
   * Process export data operation
   */
  private async processExportData(
    tenant: { id: string; name: string },
    parameters: Record<string, unknown> | undefined,
    systemUserId: string
  ): Promise<void> {
    const exportType = parameters?.exportType as string || 'basic';

    // TODO: Implement actual data export
    // For now, just log the action
    console.log(`Exporting data for tenant ${tenant.name}, type: ${exportType}`);

    // Log the action
    await EnhancedAuditService.logAuthEvent({
      action: 'TENANT_DATA_EXPORTED',
      entityType: 'Tenant',
      entityId: tenant.id,
      userId: systemUserId,
      details: {
        exportType,
        bulkOperation: true,
      },
      riskLevel: 'MEDIUM',
    });
  }

  /**
   * Validate bulk request
   */
  private validateBulkRequest(request: BulkOperationRequest): void {
    if (!request.tenantIds || request.tenantIds.length === 0) {
      throw new Error('At least one tenant ID is required');
    }

    if (request.tenantIds.length > this.MAX_BATCH_SIZE * 10) {
      throw new Error(`Maximum ${this.MAX_BATCH_SIZE * 10} tenants allowed per operation`);
    }

    if (!Object.values(BulkOperationType).includes(request.operation)) {
      throw new Error('Invalid operation type');
    }

    // Validate operation-specific parameters
    switch (request.operation) {
      case BulkOperationType.UPDATE_TIER:
        if (!request.parameters?.newTier) {
          throw new Error('New tier is required for tier update operation');
        }
        break;

      case BulkOperationType.SEND_NOTIFICATION:
        if (!request.parameters?.subject || !request.parameters?.message) {
          throw new Error('Subject and message are required for notification operation');
        }
        break;
    }
  }

  /**
   * Generate unique operation ID
   */
  private generateOperationId(): string {
    return `bulk_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * Transform operation to progress view
   */
  private transformToProgress(operation: BulkOperation): BulkOperationProgress {
    const successfulTenants = operation.results.filter(r => r.success).length;
    const failedTenants = operation.results.filter(r => !r.success).length;

    return {
      id: operation.id,
      operation: operation.operation,
      totalTenants: operation.tenantIds.length,
      processedTenants: operation.results.length,
      successfulTenants,
      failedTenants,
      progress: operation.progress,
      status: operation.status,
      startedAt: operation.createdAt,
      updatedAt: operation.updatedAt,
      estimatedTimeRemaining: this.calculateEstimatedTimeRemaining(operation),
    };
  }

  /**
   * Calculate estimated time remaining
   */
  private calculateEstimatedTimeRemaining(operation: BulkOperation): number | undefined {
    if (operation.status !== OperationStatus.IN_PROGRESS || operation.progress === 0) {
      return undefined;
    }

    const elapsedTime = Date.now() - operation.createdAt.getTime();
    const remainingProgress = 100 - operation.progress;
    const averageTimePerPercent = elapsedTime / operation.progress;

    return Math.round((remainingProgress * averageTimePerPercent) / 1000); // Return seconds
  }

  /**
   * Chunk array into smaller batches
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Update operation status
   */
  private updateOperationStatus(operationId: string, status: OperationStatus): void {
    const operation = this.operations.get(operationId);
    if (operation) {
      operation.status = status;
      operation.updatedAt = new Date();
      this.persistOperation(operation).catch(console.error);
    }
  }

  /**
   * Persist operation to database (placeholder)
   */
  private async persistOperation(operation: BulkOperation): Promise<void> {
    // TODO: Implement actual database persistence
    // For now, operations are stored in memory only
    console.log(`Persisting operation ${operation.id}, status: ${operation.status}, progress: ${operation.progress}%`);
  }

  /**
   * Load operation from database (placeholder)
   */
  private async loadOperationFromDatabase(operationId: string): Promise<BulkOperation | null> {
    // TODO: Implement actual database loading
    console.log(`Loading operation ${operationId} from database`);
    return null;
  }
}

// Export service instance
export const bulkOperationsService = new BulkOperationsService();