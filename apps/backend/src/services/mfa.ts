import * as speakeasy from 'speakeasy';
import * as QRCode from 'qrcode';
import { randomBytes, scrypt, timingSafeEqual } from 'crypto';
import { promisify } from 'util';
import { prisma } from '../prisma/client';

const scryptAsync = promisify(scrypt);

export interface MFASecret {
  secret: string;
  qrCode: string;
  backupCodes: string[];
}

export interface MFAVerification {
  isValid: boolean;
  wasBackupCode?: boolean;
}

export interface BackupCode {
  id: string;
  code: string;
  hashedCode: string;
  isUsed: boolean;
  createdAt: Date;
}

/**
 * Multi-Factor Authentication Service
 * Handles TOTP (Time-based One-Time Password) and backup codes for system users
 */
export class MFAService {
  private static readonly APP_NAME = 'Underwriting Platform';
  private static readonly BACKUP_CODE_LENGTH = 8;
  private static readonly BACKUP_CODE_COUNT = 10;

  /**
   * Generate MFA secret and setup for a system user
   */
  static async generateMFASecret(
    userId: string,
    userEmail: string
  ): Promise<MFASecret> {
    try {
      // Generate TOTP secret
      const secret = speakeasy.generateSecret({
        name: `${this.APP_NAME} (${userEmail})`,
        issuer: this.APP_NAME,
        length: 32,
      });

      if (!secret.base32) {
        throw new Error('Failed to generate TOTP secret');
      }

      // Generate QR code
      const qrCode = await QRCode.toDataURL(secret.otpauth_url || '');

      // Generate backup codes
      const backupCodes = await this.generateBackupCodes(userId);

      // Update system user with MFA secret
      await prisma.systemUser.update({
        where: { id: userId },
        data: {
          mfaSecret: secret.base32,
          mfaEnabled: false, // Will be enabled after successful verification
        },
      });

      return {
        secret: secret.base32,
        qrCode,
        backupCodes: backupCodes.map((code) => code.code),
      };
    } catch (error) {
      console.error('MFA secret generation failed:', error);
      throw new Error('Failed to generate MFA secret');
    }
  }

  /**
   * Verify TOTP token during login or setup
   */
  static async verifyMFAToken(
    userId: string,
    token: string
  ): Promise<MFAVerification> {
    try {
      // Get system user MFA secret
      const systemUser = await prisma.systemUser.findUnique({
        where: { id: userId },
        select: { mfaSecret: true, mfaEnabled: true },
      });

      if (!systemUser?.mfaSecret) {
        return { isValid: false };
      }

      // Verify TOTP token
      const verified = speakeasy.totp.verify({
        secret: systemUser.mfaSecret,
        encoding: 'base32',
        token,
        window: 2, // Allow 2 steps of tolerance (±60 seconds)
      });

      return { isValid: verified };
    } catch (error) {
      console.error('MFA token verification failed:', error);
      return { isValid: false };
    }
  }

  /**
   * Verify backup code
   */
  static async verifyBackupCode(
    userId: string,
    inputCode: string
  ): Promise<MFAVerification> {
    try {
      // Get all unused backup codes for user
      const backupCodes = await prisma.systemUserBackupCode.findMany({
        where: {
          userId,
          isUsed: false,
        },
      });

      // Check each backup code
      for (const backupCode of backupCodes) {
        const isValid = await this.verifyBackupCodeHash(
          inputCode,
          backupCode.hashedCode
        );

        if (isValid) {
          // Mark backup code as used
          await prisma.systemUserBackupCode.update({
            where: { id: backupCode.id },
            data: {
              isUsed: true,
              usedAt: new Date(),
            },
          });

          return { isValid: true, wasBackupCode: true };
        }
      }

      return { isValid: false };
    } catch (error) {
      console.error('Backup code verification failed:', error);
      return { isValid: false };
    }
  }

  /**
   * Enable MFA for a system user after successful token verification
   */
  static async enableMFA(userId: string): Promise<void> {
    try {
      await prisma.systemUser.update({
        where: { id: userId },
        data: { mfaEnabled: true },
      });
    } catch (error) {
      console.error('MFA enable failed:', error);
      throw new Error('Failed to enable MFA');
    }
  }

  /**
   * Disable MFA for a system user
   */
  static async disableMFA(userId: string): Promise<void> {
    try {
      await prisma.$transaction(async (tx) => {
        // Disable MFA and clear secret
        await tx.systemUser.update({
          where: { id: userId },
          data: {
            mfaEnabled: false,
            mfaSecret: null,
          },
        });

        // Delete all backup codes
        await tx.systemUserBackupCode.deleteMany({
          where: { userId },
        });
      });
    } catch (error) {
      console.error('MFA disable failed:', error);
      throw new Error('Failed to disable MFA');
    }
  }

  /**
   * Generate new backup codes (replaces existing ones)
   */
  static async regenerateBackupCodes(userId: string): Promise<string[]> {
    try {
      await prisma.$transaction(async (tx) => {
        // Delete existing backup codes
        await tx.systemUserBackupCode.deleteMany({
          where: { userId },
        });
      });

      // Generate new backup codes
      const backupCodes = await this.generateBackupCodes(userId);
      return backupCodes.map((code) => code.code);
    } catch (error) {
      console.error('Backup code regeneration failed:', error);
      throw new Error('Failed to regenerate backup codes');
    }
  }

  /**
   * Get backup code status for a user
   */
  static async getBackupCodeStatus(userId: string): Promise<{
    totalCodes: number;
    usedCodes: number;
    remainingCodes: number;
  }> {
    try {
      const codes = await prisma.systemUserBackupCode.findMany({
        where: { userId },
        select: { isUsed: true },
      });

      const totalCodes = codes.length;
      const usedCodes = codes.filter((code) => code.isUsed).length;
      const remainingCodes = totalCodes - usedCodes;

      return { totalCodes, usedCodes, remainingCodes };
    } catch (error) {
      console.error('Backup code status check failed:', error);
      return { totalCodes: 0, usedCodes: 0, remainingCodes: 0 };
    }
  }

  /**
   * Check if user has MFA enabled
   */
  static async isMFAEnabled(userId: string): Promise<boolean> {
    try {
      const systemUser = await prisma.systemUser.findUnique({
        where: { id: userId },
        select: { mfaEnabled: true },
      });

      return systemUser?.mfaEnabled || false;
    } catch (error) {
      console.error('MFA status check failed:', error);
      return false;
    }
  }

  /**
   * Generate backup codes for a user
   */
  private static async generateBackupCodes(
    userId: string
  ): Promise<BackupCode[]> {
    const codes: BackupCode[] = [];

    for (let i = 0; i < this.BACKUP_CODE_COUNT; i++) {
      const code = this.generateBackupCode();
      const hashedCode = await this.hashBackupCode(code);

      const backupCode = await prisma.systemUserBackupCode.create({
        data: {
          userId,
          hashedCode,
          isUsed: false,
        },
      });

      codes.push({
        id: backupCode.id,
        code,
        hashedCode,
        isUsed: false,
        createdAt: backupCode.createdAt,
      });
    }

    return codes;
  }

  /**
   * Generate a single backup code
   */
  private static generateBackupCode(): string {
    const characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';

    for (let i = 0; i < this.BACKUP_CODE_LENGTH; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }

    // Format as XXXX-XXXX for readability
    return `${result.slice(0, 4)}-${result.slice(4)}`;
  }

  /**
   * Hash backup code for secure storage
   */
  private static async hashBackupCode(code: string): Promise<string> {
    const salt = randomBytes(16);
    const hash = (await scryptAsync(code.toUpperCase(), salt, 32)) as Buffer;
    return `${salt.toString('hex')}:${hash.toString('hex')}`;
  }

  /**
   * Verify backup code against hash
   */
  private static async verifyBackupCodeHash(
    code: string,
    hashedCode: string
  ): Promise<boolean> {
    try {
      const parts = hashedCode.split(':');
      if (parts.length !== 2) {
        return false;
      }
      const [saltHex, hashHex] = parts;
      if (!saltHex || !hashHex) {
        return false;
      }
      const salt = Buffer.from(saltHex, 'hex');
      const hash = Buffer.from(hashHex, 'hex');

      const derivedHash = (await scryptAsync(
        code.toUpperCase(),
        salt,
        32
      )) as Buffer;

      return timingSafeEqual(hash, derivedHash);
    } catch {
      return false;
    }
  }
}