/**
 * Tenant Analytics and Reporting Service
 * Provides cross-tenant analytics and comparative reporting for platform administration
 */

import { prisma } from '../prisma/client';
import type { Response } from 'express';

export interface TenantAnalytics {
  tenantId: string;
  tenantName: string;
  metrics: TenantMetrics;
  trends: TrendData;
  comparisons: ComparisonData;
  healthIndicators: HealthIndicators;
  riskFactors: RiskFactor[];
}

export interface TenantMetrics {
  usageMetrics: UsageMetrics;
  performanceMetrics: PerformanceMetrics;
  businessMetrics: BusinessMetrics;
  engagementMetrics: EngagementMetrics;
}

export interface UsageMetrics {
  applicationCount: number;
  activeApplications: number;
  processedApplications: number;
  averageProcessingTime: number;
  dailyApplicationVolume: number;
  storageUsageGB: number;
  bandwidthUsageGB: number;
  apiCallsPerDay: number;
  uniqueUsersCount: number;
}

export interface PerformanceMetrics {
  avgResponseTime: number;
  successRate: number;
  errorRate: number;
  uptimePercentage: number;
  throughput: number;
  latency: {
    p50: number;
    p95: number;
    p99: number;
  };
}

export interface BusinessMetrics {
  monthlyRevenue: number;
  yearlyRevenue: number;
  customerLifetimeValue: number;
  conversionRate: number;
  churnRisk: number;
  satisfactionScore: number;
  supportTicketCount: number;
  revenueGrowthRate: number;
}

export interface EngagementMetrics {
  dailyActiveUsers: number;
  weeklyActiveUsers: number;
  monthlyActiveUsers: number;
  sessionDuration: number;
  featureAdoptionRate: number;
  retentionRate: number;
  timeToFirstValue: number;
}

export interface TrendData {
  period: string;
  data: TrendPoint[];
}

export interface TrendPoint {
  date: Date;
  value: number;
  change: number;
  changePercentage: number;
}

export interface ComparisonData {
  industryBenchmark: number;
  platformAverage: number;
  tierAverage: number;
  ranking: number;
  percentile: number;
}

export interface HealthIndicators {
  overallHealth: number; // 0-100
  categories: {
    performance: number;
    usage: number;
    business: number;
    engagement: number;
  };
  alerts: HealthAlert[];
}

export interface HealthAlert {
  type: 'warning' | 'critical' | 'info';
  category: string;
  message: string;
  value: number;
  threshold: number;
  trend: 'improving' | 'declining' | 'stable';
}

export interface RiskFactor {
  type: 'churn' | 'performance' | 'usage' | 'financial';
  severity: 'low' | 'medium' | 'high' | 'critical';
  score: number; // 0-100
  description: string;
  recommendedActions: string[];
  timeline: string;
}

export interface AnalyticsFilters {
  tenantIds?: string[];
  dateRange: {
    start: Date;
    end: Date;
  };
  metrics?: string[];
  subscriptionTiers?: string[];
  regions?: string[];
  healthScoreRange?: {
    min: number;
    max: number;
  };
}

export interface CrossTenantComparison {
  metric: string;
  tenants: Array<{
    tenantId: string;
    tenantName: string;
    value: number;
    ranking: number;
    percentile: number;
    trend: 'up' | 'down' | 'stable';
  }>;
  platformStats: {
    average: number;
    median: number;
    min: number;
    max: number;
    standardDeviation: number;
  };
}

export interface AnalyticsReport {
  id: string;
  title: string;
  description: string;
  generatedAt: Date;
  filters: AnalyticsFilters;
  data: TenantAnalytics[];
  comparisons: CrossTenantComparison[];
  insights: string[];
  recommendations: string[];
  exportFormats: string[];
}

class TenantAnalyticsService {
  /**
   * Get comprehensive analytics for a specific tenant
   */
  async getTenantAnalytics(tenantId: string, options: {
    includeComparisons?: boolean;
    includeTrends?: boolean;
    dateRange?: { start: Date; end: Date };
  } = {}): Promise<TenantAnalytics> {
    const tenant = await this.getTenantWithRelations(tenantId);
    if (!tenant) {
      throw new Error(`Tenant not found: ${tenantId}`);
    }

    const metrics = await this.calculateTenantMetrics(tenantId, options.dateRange);
    const trends = options.includeTrends ? await this.calculateTrends(tenantId, options.dateRange) : null;
    const comparisons = options.includeComparisons ? await this.calculateComparisons(tenantId, metrics) : null;
    const healthIndicators = await this.calculateHealthIndicators(tenantId, metrics);
    const riskFactors = await this.assessRiskFactors(tenantId, metrics, healthIndicators);

    return {
      tenantId,
      tenantName: (tenant as unknown as { name?: string })?.name || 'Unknown',
      metrics,
      trends: trends || { period: '', data: [] },
      comparisons: comparisons || { industryBenchmark: 0, platformAverage: 0, tierAverage: 0, ranking: 0, percentile: 0 },
      healthIndicators,
      riskFactors
    };
  }

  /**
   * Get cross-tenant analytics and comparisons
   */
  async getCrossTenantAnalytics(filters: AnalyticsFilters): Promise<{
    tenants: TenantAnalytics[];
    comparisons: CrossTenantComparison[];
    platformSummary: {
      totalTenants: number;
      totalRevenue: number;
      averageHealth: number;
      topPerformers: string[];
      riskTenants: string[];
    };
  }> {
    const tenantIds = filters.tenantIds || await this.getFilteredTenantIds(filters);
    
    const tenantAnalytics = await Promise.all(
      tenantIds.map(id => this.getTenantAnalytics(id, {
        includeComparisons: true,
        includeTrends: true,
        dateRange: filters.dateRange
      }))
    );

    const comparisons = await this.generateCrossTenantComparisons(tenantAnalytics, filters);
    const platformSummary = this.calculatePlatformSummary(tenantAnalytics);

    return {
      tenants: tenantAnalytics,
      comparisons,
      platformSummary
    };
  }

  /**
   * Generate analytics report
   */
  async generateAnalyticsReport(
    title: string,
    filters: AnalyticsFilters,
    options: {
      includeInsights?: boolean;
      includeRecommendations?: boolean;
      format?: 'json' | 'csv' | 'pdf';
    } = {}
  ): Promise<AnalyticsReport> {
    const crossTenantData = await this.getCrossTenantAnalytics(filters);
    
    const insights = options.includeInsights ? 
      await this.generateInsights(crossTenantData) : [];
    
    const recommendations = options.includeRecommendations ? 
      await this.generateRecommendations(crossTenantData) : [];

    const report: AnalyticsReport = {
      id: `report_${Date.now()}`,
      title,
      description: `Analytics report for ${crossTenantData.tenants.length} tenants`,
      generatedAt: new Date(),
      filters,
      data: crossTenantData.tenants,
      comparisons: crossTenantData.comparisons,
      insights,
      recommendations,
      exportFormats: ['json', 'csv', 'pdf']
    };

    return report;
  }

  /**
   * Export report in specified format
   */
  async exportReport(
    report: AnalyticsReport,
    format: 'json' | 'csv' | 'pdf',
    res: Response
  ): Promise<void> {
    switch (format) {
      case 'json':
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', `attachment; filename="${report.title}_${Date.now()}.json"`);
        res.json(report);
        break;

      case 'csv': {
        const csvData = this.convertToCSV(report);
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="${report.title}_${Date.now()}.csv"`);
        res.send(csvData);
        break;
      }

      case 'pdf':
        // Note: PDF generation would require additional library like puppeteer
        throw new Error('PDF export not yet implemented');

      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  /**
   * Calculate comprehensive tenant metrics
   */
  private async calculateTenantMetrics(
    tenantId: string,
    dateRange?: { start: Date; end: Date }
  ): Promise<TenantMetrics> {
    const usageMetrics = await this.calculateUsageMetrics(tenantId, dateRange);
    const performanceMetrics = await this.calculatePerformanceMetrics(tenantId, dateRange);
    const businessMetrics = await this.calculateBusinessMetrics(tenantId, dateRange);
    const engagementMetrics = await this.calculateEngagementMetrics(tenantId, dateRange);

    return {
      usageMetrics,
      performanceMetrics,
      businessMetrics,
      engagementMetrics
    };
  }

  private async calculateUsageMetrics(
    tenantId: string,
    dateRange?: { start: Date; end: Date }
  ): Promise<UsageMetrics> {
    const whereClause = dateRange ? {
      tenantId,
      createdAt: {
        gte: dateRange.start,
        lte: dateRange.end
      }
    } : { tenantId };

    const applications = await prisma.application.findMany({
      where: whereClause,
      include: {
        tenant: true
      }
    });

    const activeApplications = applications.filter(app => 
      ['IN_PROGRESS', 'UNDER_REVIEW'].includes(app.status)
    );

    const processedApplications = applications.filter(app => 
      ['APPROVED', 'REJECTED', 'WITHDRAWN'].includes(app.status)
    );

    // Calculate average processing time
    const completedApps = applications.filter(app => app.completedAt);
    const avgProcessingTime = completedApps.length > 0 ? 
      completedApps.reduce((sum, app) => {
        if (app.completedAt && app.createdAt) {
          return sum + (app.completedAt.getTime() - app.createdAt.getTime());
        }
        return sum;
      }, 0) / completedApps.length / (1000 * 60 * 60 * 24) : 0; // Convert to days

    // Mock values for metrics not directly available in schema
    const dailyApplicationVolume = applications.length / (dateRange ? 
      Math.ceil((dateRange.end.getTime() - dateRange.start.getTime()) / (1000 * 60 * 60 * 24)) : 30);

    return {
      applicationCount: applications.length,
      activeApplications: activeApplications.length,
      processedApplications: processedApplications.length,
      averageProcessingTime: Math.round(avgProcessingTime * 100) / 100,
      dailyApplicationVolume: Math.round(dailyApplicationVolume * 100) / 100,
      storageUsageGB: Math.random() * 100, // Mock value
      bandwidthUsageGB: Math.random() * 50,
      apiCallsPerDay: Math.floor(Math.random() * 10000),
      uniqueUsersCount: Math.floor(Math.random() * 100) + 1
    };
  }

  private async calculatePerformanceMetrics(
    _tenantId: string,
    _dateRange?: { start: Date; end: Date }
  ): Promise<PerformanceMetrics> {
    // Mock performance metrics - in real implementation, these would come from monitoring systems
    return {
      avgResponseTime: Math.random() * 500 + 100,
      successRate: 95 + Math.random() * 4,
      errorRate: Math.random() * 2,
      uptimePercentage: 99 + Math.random() * 0.9,
      throughput: Math.random() * 1000 + 500,
      latency: {
        p50: Math.random() * 100 + 50,
        p95: Math.random() * 200 + 150,
        p99: Math.random() * 500 + 300
      }
    };
  }

  private async calculateBusinessMetrics(
    tenantId: string,
    _dateRange?: { start: Date; end: Date }
  ): Promise<BusinessMetrics> {
    const tenant = await prisma.tenant.findUnique({
      where: { id: tenantId },
      include: {
        subscriptions: true
      }
    });

    if (!tenant) {
      throw new Error(`Tenant not found: ${tenantId}`);
    }

    // Calculate revenue from subscriptions
    const monthlyRevenue = tenant.subscriptions?.reduce((sum, sub) => {
      const amount = Number(sub.monthlyFee) || 0;
      return sum + (sub.status === 'ACTIVE' ? amount : 0);
    }, 0) || 0;

    return {
      monthlyRevenue,
      yearlyRevenue: monthlyRevenue * 12,
      customerLifetimeValue: monthlyRevenue * 24, // Estimated 2-year LTV
      conversionRate: 75 + Math.random() * 20, // Mock conversion rate
      churnRisk: Math.random() * 30, // Risk percentage
      satisfactionScore: 4 + Math.random() * 1, // 4-5 star rating
      supportTicketCount: Math.floor(Math.random() * 20),
      revenueGrowthRate: (Math.random() - 0.5) * 50 // -25% to +25%
    };
  }

  private async calculateEngagementMetrics(
    tenantId: string,
    _dateRange?: { start: Date; end: Date }
  ): Promise<EngagementMetrics> {
    const users = await prisma.user.findMany({
      where: { tenantId }
    });

    const activeUsers = users.filter(user => 
      user.lastLoginAt && user.lastLoginAt > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    );

    return {
      dailyActiveUsers: Math.floor(activeUsers.length * (0.3 + Math.random() * 0.4)),
      weeklyActiveUsers: Math.floor(activeUsers.length * (0.6 + Math.random() * 0.3)),
      monthlyActiveUsers: activeUsers.length,
      sessionDuration: Math.random() * 60 + 15, // 15-75 minutes
      featureAdoptionRate: Math.random() * 100,
      retentionRate: 70 + Math.random() * 25,
      timeToFirstValue: Math.random() * 7 + 1 // 1-8 days
    };
  }

  private async calculateTrends(
    _tenantId: string,
    _dateRange?: { start: Date; end: Date }
  ): Promise<TrendData> {
    // Mock trend data - in real implementation, this would aggregate historical data
    const days = 30;
    const data: TrendPoint[] = [];
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      
      const value = 50 + Math.random() * 50 + Math.sin(i / 7) * 10;
      const previousValue = i === days - 1 ? value : data[data.length - 1]?.value || value;
      const change = value - previousValue;
      const changePercentage = previousValue > 0 ? (change / previousValue) * 100 : 0;
      
      data.push({
        date,
        value: Math.round(value * 100) / 100,
        change: Math.round(change * 100) / 100,
        changePercentage: Math.round(changePercentage * 100) / 100
      });
    }

    return {
      period: `${days} days`,
      data
    };
  }

  private async calculateComparisons(
    _tenantId: string,
    _metrics: TenantMetrics
  ): Promise<ComparisonData> {
    // Mock comparison data - real implementation would compare against platform averages
    const platformAverage = 50 + Math.random() * 30;
    const tierAverage = platformAverage + (Math.random() - 0.5) * 10;
    const industryBenchmark = platformAverage + (Math.random() - 0.5) * 20;
    
    return {
      industryBenchmark: Math.round(industryBenchmark * 100) / 100,
      platformAverage: Math.round(platformAverage * 100) / 100,
      tierAverage: Math.round(tierAverage * 100) / 100,
      ranking: Math.floor(Math.random() * 100) + 1,
      percentile: Math.floor(Math.random() * 100) + 1
    };
  }

  private async calculateHealthIndicators(
    _tenantId: string,
    metrics: TenantMetrics
  ): Promise<HealthIndicators> {
    const performance = Math.min(100, (metrics.performanceMetrics.successRate + 
      (100 - metrics.performanceMetrics.errorRate)) / 2);
    
    const usage = Math.min(100, (metrics.usageMetrics.applicationCount / 10) * 100);
    
    const business = Math.min(100, (metrics.businessMetrics.satisfactionScore / 5) * 100);
    
    const engagement = Math.min(100, metrics.engagementMetrics.retentionRate);
    
    const overallHealth = Math.round((performance + usage + business + engagement) / 4);

    const alerts: HealthAlert[] = [];
    
    if (performance < 80) {
      alerts.push({
        type: 'warning',
        category: 'performance',
        message: 'Performance metrics below threshold',
        value: performance,
        threshold: 80,
        trend: 'declining'
      });
    }

    if (engagement < 70) {
      alerts.push({
        type: 'critical',
        category: 'engagement',
        message: 'Low user engagement detected',
        value: engagement,
        threshold: 70,
        trend: 'declining'
      });
    }

    return {
      overallHealth,
      categories: {
        performance: Math.round(performance),
        usage: Math.round(usage),
        business: Math.round(business),
        engagement: Math.round(engagement)
      },
      alerts
    };
  }

  private async assessRiskFactors(
    _tenantId: string,
    metrics: TenantMetrics,
    healthIndicators: HealthIndicators
  ): Promise<RiskFactor[]> {
    const riskFactors: RiskFactor[] = [];

    // Churn risk assessment
    if (metrics.businessMetrics.churnRisk > 20) {
      riskFactors.push({
        type: 'churn',
        severity: metrics.businessMetrics.churnRisk > 50 ? 'critical' : 'high',
        score: Math.round(metrics.businessMetrics.churnRisk),
        description: 'High churn risk based on engagement and satisfaction metrics',
        recommendedActions: [
          'Implement customer success outreach',
          'Review onboarding process',
          'Analyze feature usage patterns'
        ],
        timeline: '30 days'
      });
    }

    // Performance risk assessment
    if (healthIndicators.categories.performance < 80) {
      riskFactors.push({
        type: 'performance',
        severity: healthIndicators.categories.performance < 60 ? 'critical' : 'medium',
        score: 100 - healthIndicators.categories.performance,
        description: 'Performance metrics indicate potential system issues',
        recommendedActions: [
          'Investigate system performance',
          'Review infrastructure capacity',
          'Optimize application queries'
        ],
        timeline: '7 days'
      });
    }

    return riskFactors;
  }

  private async getTenantWithRelations(tenantId: string): Promise<{
    id: string;
    users: unknown[];
    applications: unknown[];
    subscriptions: unknown[];
  } | null> {
    return prisma.tenant.findUnique({
      where: { id: tenantId },
      include: {
        users: true,
        applications: true,
        subscriptions: true
      }
    });
  }

  private async getFilteredTenantIds(filters: AnalyticsFilters): Promise<string[]> {
    const tenants = await prisma.tenant.findMany({
      where: filters.subscriptionTiers ? {
        subscriptionTier: { in: filters.subscriptionTiers as unknown as Array<'STARTER' | 'PROFESSIONAL' | 'ENTERPRISE'> }
      } : {},
      select: { id: true }
    });

    return tenants.map(t => t.id);
  }

  private async generateCrossTenantComparisons(
    tenantAnalytics: TenantAnalytics[],
    _filters: AnalyticsFilters
  ): Promise<CrossTenantComparison[]> {
    const comparisons: CrossTenantComparison[] = [];

    // Revenue comparison
    const revenueData = tenantAnalytics.map(t => ({
      tenantId: t.tenantId,
      tenantName: t.tenantName,
      value: t.metrics.businessMetrics.monthlyRevenue,
      ranking: 0,
      percentile: 0,
      trend: Math.random() > 0.5 ? 'up' : 'down' as 'up' | 'down' | 'stable'
    })).sort((a, b) => b.value - a.value);

    // Calculate rankings and percentiles
    revenueData.forEach((item, index) => {
      item.ranking = index + 1;
      item.percentile = Math.round(((revenueData.length - index) / revenueData.length) * 100);
    });

    const revenueValues = revenueData.map(d => d.value);
    comparisons.push({
      metric: 'Monthly Revenue',
      tenants: revenueData,
      platformStats: {
        average: revenueValues.reduce((a, b) => a + b, 0) / revenueValues.length,
        median: revenueValues[Math.floor(revenueValues.length / 2)] || 0,
        min: Math.min(...revenueValues),
        max: Math.max(...revenueValues),
        standardDeviation: this.calculateStandardDeviation(revenueValues)
      }
    });

    return comparisons;
  }

  private calculatePlatformSummary(tenantAnalytics: TenantAnalytics[]): {
    totalTenants: number;
    totalRevenue: number;
    averageHealth: number;
    topPerformers: string[];
    riskTenants: string[];
  } {
    const totalRevenue = tenantAnalytics.reduce((sum, t) => 
      sum + t.metrics.businessMetrics.monthlyRevenue, 0);
    
    const averageHealth = tenantAnalytics.reduce((sum, t) => 
      sum + t.healthIndicators.overallHealth, 0) / tenantAnalytics.length;

    const topPerformers = tenantAnalytics
      .sort((a, b) => b.healthIndicators.overallHealth - a.healthIndicators.overallHealth)
      .slice(0, 5)
      .map(t => t.tenantId);

    const riskTenants = tenantAnalytics
      .filter(t => t.riskFactors.some(rf => rf.severity === 'critical'))
      .map(t => t.tenantId);

    return {
      totalTenants: tenantAnalytics.length,
      totalRevenue: Math.round(totalRevenue * 100) / 100,
      averageHealth: Math.round(averageHealth * 100) / 100,
      topPerformers,
      riskTenants
    };
  }

  private async generateInsights(crossTenantData: {
    platformSummary: {
      averageHealth: number;
      riskTenants: string[];
      totalTenants: number;
    };
  }): Promise<string[]> {
    const insights = [];
    
    if (crossTenantData.platformSummary.averageHealth < 75) {
      insights.push('Platform health is below optimal threshold. Consider implementing proactive support measures.');
    }

    if (crossTenantData.platformSummary.riskTenants.length > crossTenantData.platformSummary.totalTenants * 0.1) {
      insights.push('High percentage of tenants at risk. Review customer success processes.');
    }

    return insights;
  }

  private async generateRecommendations(_crossTenantData: unknown): Promise<string[]> {
    const recommendations = [];
    
    recommendations.push('Implement automated health monitoring alerts for tenants below 70% health score');
    recommendations.push('Create customer success playbooks for high-risk tenant segments');
    recommendations.push('Develop performance optimization guidelines for underperforming tenants');

    return recommendations;
  }

  private convertToCSV(report: AnalyticsReport): string {
    const headers = [
      'Tenant ID', 'Tenant Name', 'Health Score', 'Monthly Revenue', 
      'Application Count', 'Success Rate', 'Churn Risk'
    ];

    const rows = report.data.map(tenant => [
      tenant.tenantId,
      tenant.tenantName,
      tenant.healthIndicators.overallHealth,
      tenant.metrics.businessMetrics.monthlyRevenue,
      tenant.metrics.usageMetrics.applicationCount,
      tenant.metrics.performanceMetrics.successRate,
      tenant.metrics.businessMetrics.churnRisk
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  private calculateStandardDeviation(values: number[]): number {
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((sum, value) => sum + Math.pow(value - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }
}

export const tenantAnalyticsService = new TenantAnalyticsService();