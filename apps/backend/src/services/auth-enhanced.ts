import { SystemUser, SystemUserRole } from '@prisma/client';
import { prisma } from '../prisma/client';
import { JWTService, TokenPair } from '../utils/jwt';
import { PasswordService } from '../utils/password';
import { SessionManager } from './redis';
import { MFAService } from './mfa';

export interface SystemLoginCredentials {
  email: string;
  password: string;
  mfaToken?: string;
  backupCode?: string;
}

export interface SystemLoginResult {
  success: boolean;
  requiresMFA?: boolean;
  tempToken?: string; // For MFA verification step
  tokens?: TokenPair;
  user?: {
    id: string;
    email: string;
    name: string;
    role: SystemUserRole;
    isSystemUser: true;
    mfaEnabled: boolean;
  };
  error?: string;
}

export interface CreateSystemUserData {
  email: string;
  password: string;
  name: string;
  role: SystemUserRole;
}

export interface MFASetupData {
  secret: string;
  qrCode: string;
  backupCodes: string[];
}

/**
 * Enhanced Authentication Service with MFA support
 * Handles system user authentication with multi-factor authentication
 */
export class EnhancedAuthService {
  /**
   * System user login with MFA support
   */
  static async systemLogin(
    credentials: SystemLoginCredentials
  ): Promise<SystemLoginResult> {
    try {
      const { email, password, mfaToken, backupCode } = credentials;

      // Find system user by email
      const systemUser = await prisma.systemUser.findUnique({
        where: { email },
      });

      if (!systemUser) {
        return {
          success: false,
          error: 'Invalid email or password',
        };
      }

      // Check if system user is active
      if (!systemUser.isActive) {
        return {
          success: false,
          error: 'Account is suspended',
        };
      }

      // Verify password
      const isPasswordValid = await PasswordService.verifyPassword(
        password,
        systemUser.passwordHash
      );
      if (!isPasswordValid) {
        return {
          success: false,
          error: 'Invalid email or password',
        };
      }

      // Check if MFA is enabled
      if (systemUser.mfaEnabled) {
        // If MFA is enabled but no MFA credentials provided, return requiresMFA
        if (!mfaToken && !backupCode) {
          // Generate temporary token for MFA verification
          const tempToken = JWTService.generateTempToken({
            userId: systemUser.id,
            email: systemUser.email,
            purpose: 'mfa_verification',
          });

          return {
            success: false,
            requiresMFA: true,
            tempToken,
          };
        }

        // Verify MFA token or backup code
        let mfaVerification;
        if (mfaToken) {
          mfaVerification = await MFAService.verifyMFAToken(
            systemUser.id,
            mfaToken
          );
        } else if (backupCode) {
          mfaVerification = await MFAService.verifyBackupCode(
            systemUser.id,
            backupCode
          );
        } else {
          return {
            success: false,
            error: 'MFA token or backup code required',
          };
        }

        if (!mfaVerification.isValid) {
          return {
            success: false,
            error: mfaVerification.wasBackupCode
              ? 'Invalid backup code'
              : 'Invalid MFA token',
          };
        }
      }

      // Create successful login session
      return await this.createSystemUserSession(systemUser);
    } catch (error) {
      console.error('Enhanced system login failed:', error);
      return {
        success: false,
        error: 'Login failed',
      };
    }
  }

  /**
   * Verify MFA token with temporary token
   */
  static async verifyMFAToken(
    tempToken: string,
    mfaToken: string
  ): Promise<SystemLoginResult> {
    try {
      // Verify temporary token
      const tempPayload = JWTService.verifyTempToken(tempToken);
      if (!tempPayload || tempPayload.purpose !== 'mfa_verification') {
        return {
          success: false,
          error: 'Invalid or expired temporary token',
        };
      }

      // Get system user
      const systemUser = await prisma.systemUser.findUnique({
        where: { id: tempPayload.userId },
      });

      if (!systemUser || !systemUser.isActive) {
        return {
          success: false,
          error: 'User not found or inactive',
        };
      }

      // Verify MFA token
      const mfaVerification = await MFAService.verifyMFAToken(
        systemUser.id,
        mfaToken
      );

      if (!mfaVerification.isValid) {
        return {
          success: false,
          error: 'Invalid MFA token',
        };
      }

      // Create successful login session
      return await this.createSystemUserSession(systemUser);
    } catch (error) {
      console.error('MFA token verification failed:', error);
      return {
        success: false,
        error: 'MFA verification failed',
      };
    }
  }

  /**
   * Setup MFA for a system user
   */
  static async setupMFA(userId: string): Promise<MFASetupData> {
    try {
      // Check if user exists and is active
      const systemUser = await prisma.systemUser.findUnique({
        where: { id: userId },
      });

      if (!systemUser || !systemUser.isActive) {
        throw new Error('User not found or inactive');
      }

      // Generate MFA secret and QR code
      const mfaData = await MFAService.generateMFASecret(
        userId,
        systemUser.email
      );

      return {
        secret: mfaData.secret,
        qrCode: mfaData.qrCode,
        backupCodes: mfaData.backupCodes,
      };
    } catch (error) {
      console.error('MFA setup failed:', error);
      throw new Error('Failed to setup MFA');
    }
  }

  /**
   * Enable MFA after successful token verification
   */
  static async enableMFA(
    userId: string,
    mfaToken: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Verify the MFA token
      const verification = await MFAService.verifyMFAToken(userId, mfaToken);
      if (!verification.isValid) {
        return {
          success: false,
          error: 'Invalid MFA token',
        };
      }

      // Enable MFA for the user
      await MFAService.enableMFA(userId);

      return { success: true };
    } catch (error) {
      console.error('MFA enable failed:', error);
      return {
        success: false,
        error: 'Failed to enable MFA',
      };
    }
  }

  /**
   * Disable MFA for a system user
   */
  static async disableMFA(userId: string): Promise<void> {
    await MFAService.disableMFA(userId);
  }

  /**
   * Regenerate backup codes
   */
  static async regenerateBackupCodes(userId: string): Promise<string[]> {
    return await MFAService.regenerateBackupCodes(userId);
  }

  /**
   * Get backup code status
   */
  static async getBackupCodeStatus(userId: string): Promise<{
    totalCodes: number;
    usedCodes: number;
    remainingCodes: number;
  }> {
    return await MFAService.getBackupCodeStatus(userId);
  }

  /**
   * Create new system user with enhanced security
   */
  static async createSystemUser(
    userData: CreateSystemUserData
  ): Promise<SystemUser> {
    const { email, password, name, role } = userData;

    // Validate password strength
    const passwordValidation =
      PasswordService.validatePasswordStrength(password);
    if (!passwordValidation.isValid) {
      throw new Error(
        `Password validation failed: ${passwordValidation.errors.join(', ')}`
      );
    }

    // Hash password
    const passwordHash = await PasswordService.hashPassword(password);

    // Create system user
    const systemUser = await prisma.systemUser.create({
      data: {
        email,
        passwordHash,
        name,
        role,
        emailVerified: false,
        isActive: true,
        mfaEnabled: false, // MFA will be setup separately
      },
    });

    return systemUser;
  }

  /**
   * Change system user password with enhanced security
   */
  static async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Validate new password
      const passwordValidation =
        PasswordService.validatePasswordStrength(newPassword);
      if (!passwordValidation.isValid) {
        return {
          success: false,
          error: `Password validation failed: ${passwordValidation.errors.join(', ')}`,
        };
      }

      // Get system user
      const systemUser = await prisma.systemUser.findUnique({
        where: { id: userId },
        select: { id: true, passwordHash: true },
      });

      if (!systemUser) {
        return {
          success: false,
          error: 'User not found',
        };
      }

      // Verify current password
      const isCurrentPasswordValid = await PasswordService.verifyPassword(
        currentPassword,
        systemUser.passwordHash
      );

      if (!isCurrentPasswordValid) {
        return {
          success: false,
          error: 'Current password is incorrect',
        };
      }

      // Hash new password
      const newPasswordHash = await PasswordService.hashPassword(newPassword);

      // Update password
      await prisma.systemUser.update({
        where: { id: userId },
        data: { passwordHash: newPasswordHash },
      });

      // Invalidate all sessions for this user
      await SessionManager.deleteAllUserSessions(userId);

      return { success: true };
    } catch (error) {
      console.error('Password change failed:', error);
      return {
        success: false,
        error: 'Password change failed',
      };
    }
  }

  /**
   * Create system user session
   */
  private static async createSystemUserSession(
    systemUser: SystemUser
  ): Promise<SystemLoginResult> {
    try {
      // Create session
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Store session in database
      const session = await prisma.systemUserSession.create({
        data: {
          userId: systemUser.id,
          sessionToken: '', // Will be updated with JWT
          expiresAt,
          device: 'web',
          ipAddress: null, // Will be set by controller
          userAgent: null, // Will be set by controller
        },
      });

      // Generate tokens
      const tokens = JWTService.generateTokenPair(
        {
          userId: systemUser.id,
          email: systemUser.email,
          role: systemUser.role,
          isSystemUser: true,
        },
        session.id
      );

      // Update session with access token
      await prisma.systemUserSession.update({
        where: { id: session.id },
        data: { sessionToken: tokens.accessToken },
      });

      // Store session data in Redis
      await SessionManager.setSession(session.id, {
        userId: systemUser.id,
        email: systemUser.email,
        role: systemUser.role,
        isSystemUser: true,
      });

      // Update last login
      await prisma.systemUser.update({
        where: { id: systemUser.id },
        data: { lastLoginAt: new Date() },
      });

      return {
        success: true,
        tokens,
        user: {
          id: systemUser.id,
          email: systemUser.email,
          name: systemUser.name,
          role: systemUser.role,
          isSystemUser: true,
          mfaEnabled: systemUser.mfaEnabled,
        },
      };
    } catch (error) {
      console.error('Session creation failed:', error);
      throw new Error('Failed to create session');
    }
  }
}