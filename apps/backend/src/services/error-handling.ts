/**
 * Centralized Error Handling Service
 * Superman-level error management with security, logging, and monitoring
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

export enum ErrorType {
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  RATE_LIMIT = 'RATE_LIMIT',
  EXTERNAL_SERVICE = 'EXTERNAL_SERVICE',
  DATABASE = 'DATABASE',
  INTERNAL = 'INTERNAL',
}

export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM', 
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

export interface ErrorContext {
  userId?: string;
  tenantId?: string;
  requestId?: string;
  ip?: string;
  userAgent?: string;
  endpoint?: string;
  method?: string;
  timestamp?: Date;
  stackTrace?: string;
  additionalData?: Record<string, unknown>;
}

export interface StandardError {
  type: ErrorType;
  severity: ErrorSeverity;
  code: string;
  message: string;
  userMessage: string;
  context: ErrorContext;
  isOperational: boolean;
  statusCode: number;
}

export class AppError extends Error implements StandardError {
  public readonly type: ErrorType;
  public readonly severity: ErrorSeverity;
  public readonly code: string;
  public readonly userMessage: string;
  public readonly context: ErrorContext;
  public readonly isOperational: boolean;
  public readonly statusCode: number;

  constructor(
    type: ErrorType,
    severity: ErrorSeverity,
    code: string,
    message: string,
    userMessage: string,
    statusCode: number,
    context: ErrorContext = {},
    isOperational: boolean = true
  ) {
    super(message);
    
    this.type = type;
    this.severity = severity;
    this.code = code;
    this.userMessage = userMessage;
    this.context = {
      ...context,
      timestamp: new Date(),
      stackTrace: this.stack,
    };
    this.isOperational = isOperational;
    this.statusCode = statusCode;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

export class ErrorHandlingService {
  /**
   * Create standardized validation error
   */
  static createValidationError(
    message: string,
    userMessage: string = 'Invalid input provided',
    context: ErrorContext = {}
  ): AppError {
    return new AppError(
      ErrorType.VALIDATION,
      ErrorSeverity.LOW,
      'ERR_VALIDATION',
      message,
      userMessage,
      400,
      context
    );
  }

  /**
   * Create authentication error
   */
  static createAuthenticationError(
    message: string = 'Authentication failed',
    userMessage: string = 'Invalid credentials',
    context: ErrorContext = {}
  ): AppError {
    return new AppError(
      ErrorType.AUTHENTICATION,
      ErrorSeverity.HIGH,
      'ERR_AUTHENTICATION',
      message,
      userMessage,
      401,
      context
    );
  }

  /**
   * Create authorization error
   */
  static createAuthorizationError(
    message: string = 'Insufficient permissions',
    userMessage: string = 'Access denied',
    context: ErrorContext = {}
  ): AppError {
    return new AppError(
      ErrorType.AUTHORIZATION,
      ErrorSeverity.MEDIUM,
      'ERR_AUTHORIZATION',
      message,
      userMessage,
      403,
      context
    );
  }

  /**
   * Create not found error
   */
  static createNotFoundError(
    resource: string,
    userMessage: string = 'Resource not found',
    context: ErrorContext = {}
  ): AppError {
    return new AppError(
      ErrorType.NOT_FOUND,
      ErrorSeverity.LOW,
      'ERR_NOT_FOUND',
      `${resource} not found`,
      userMessage,
      404,
      context
    );
  }

  /**
   * Create conflict error
   */
  static createConflictError(
    message: string,
    userMessage: string = 'Conflict detected',
    context: ErrorContext = {}
  ): AppError {
    return new AppError(
      ErrorType.CONFLICT,
      ErrorSeverity.MEDIUM,
      'ERR_CONFLICT',
      message,
      userMessage,
      409,
      context
    );
  }

  /**
   * Create rate limit error
   */
  static createRateLimitError(
    userMessage: string = 'Too many requests',
    context: ErrorContext = {}
  ): AppError {
    return new AppError(
      ErrorType.RATE_LIMIT,
      ErrorSeverity.MEDIUM,
      'ERR_RATE_LIMIT',
      'Rate limit exceeded',
      userMessage,
      429,
      context
    );
  }

  /**
   * Create external service error
   */
  static createExternalServiceError(
    service: string,
    message: string,
    userMessage: string = 'External service unavailable',
    context: ErrorContext = {}
  ): AppError {
    return new AppError(
      ErrorType.EXTERNAL_SERVICE,
      ErrorSeverity.HIGH,
      'ERR_EXTERNAL_SERVICE',
      `${service}: ${message}`,
      userMessage,
      502,
      context
    );
  }

  /**
   * Create database error
   */
  static createDatabaseError(
    operation: string,
    message: string,
    userMessage: string = 'Database operation failed',
    context: ErrorContext = {}
  ): AppError {
    return new AppError(
      ErrorType.DATABASE,
      ErrorSeverity.HIGH,
      'ERR_DATABASE',
      `Database ${operation}: ${message}`,
      userMessage,
      500,
      context
    );
  }

  /**
   * Create internal server error
   */
  static createInternalError(
    message: string,
    userMessage: string = 'Internal server error',
    context: ErrorContext = {}
  ): AppError {
    return new AppError(
      ErrorType.INTERNAL,
      ErrorSeverity.CRITICAL,
      'ERR_INTERNAL',
      message,
      userMessage,
      500,
      context
    );
  }

  /**
   * Extract context from request
   */
  static extractRequestContext(req: Request): ErrorContext {
    return {
      userId: req.user?.userId,
      tenantId: req.user?.tenantId,
      requestId: req.headers['x-request-id'] as string,
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
      method: req.method,
      timestamp: new Date(),
    };
  }

  /**
   * Log error based on severity
   */
  static logError(error: AppError): void {
    const logData = {
      type: error.type,
      severity: error.severity,
      code: error.code,
      message: error.message,
      statusCode: error.statusCode,
      context: {
        ...error.context,
        // Never log sensitive data
        stackTrace: error.severity === ErrorSeverity.CRITICAL ? error.context.stackTrace : undefined,
      },
    };

    switch (error.severity) {
      case ErrorSeverity.LOW:
        logger.info('Application error occurred', logData);
        break;
      case ErrorSeverity.MEDIUM:
        logger.warn('Application error occurred', logData);
        break;
      case ErrorSeverity.HIGH:
        logger.error('Application error occurred', logData);
        break;
      case ErrorSeverity.CRITICAL:
        logger.error('CRITICAL application error occurred', logData);
        // Could trigger alerts here
        break;
      default:
        logger.error('Unknown severity error occurred', logData);
    }
  }

  /**
   * Send error response to client
   */
  static sendErrorResponse(res: Response, error: AppError): void {
    // Never expose internal details in production
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    const errorResponse = {
      success: false,
      error: {
        type: error.type,
        code: error.code,
        message: error.userMessage,
        ...(isDevelopment && {
          details: error.message,
          context: {
            ...error.context,
            // Never expose stack traces to clients
            stackTrace: undefined,
          },
        }),
      },
      requestId: error.context.requestId,
      timestamp: error.context.timestamp,
    };

    res.status(error.statusCode).json(errorResponse);
  }

  /**
   * Check if error is operational (expected) vs programming error
   */
  static isOperationalError(error: Error): error is AppError {
    return error instanceof AppError && error.isOperational;
  }
}

/**
 * Global error handling middleware
 */
export const globalErrorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // If headers already sent, delegate to Express default handler
  if (res.headersSent) {
    return next(error);
  }

  let appError: AppError;

  if (ErrorHandlingService.isOperationalError(error)) {
    // Known operational error
    appError = error;
  } else {
    // Unknown error - treat as internal server error
    const context = ErrorHandlingService.extractRequestContext(req);
    appError = ErrorHandlingService.createInternalError(
      error.message,
      'Internal server error',
      context
    );
  }

  // Add request context if not already present
  if (!appError.context.requestId) {
    Object.assign(appError.context, ErrorHandlingService.extractRequestContext(req));
  }

  // Log the error
  ErrorHandlingService.logError(appError);

  // Send response to client
  ErrorHandlingService.sendErrorResponse(res, appError);
};

/**
 * Async wrapper for route handlers to catch async errors
 */
export const asyncHandler = (
  fn: (req: Request, res: Response, next: NextFunction) => Promise<void>
) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 404 handler for unknown routes
 */
export const notFoundHandler = (req: Request, _res: Response, next: NextFunction): void => {
  const context = ErrorHandlingService.extractRequestContext(req);
  const error = ErrorHandlingService.createNotFoundError(
    `Route ${req.method} ${req.path}`,
    'Endpoint not found',
    context
  );
  next(error);
};