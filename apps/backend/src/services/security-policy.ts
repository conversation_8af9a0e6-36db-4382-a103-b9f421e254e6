import { Request } from 'express';
import { prisma } from '../prisma/client';
import { GeoLocationService, GeoLocation } from './geolocation';

export interface SecurityPolicy {
  ipWhitelist?: string[];
  geoRestrictions?: {
    allowedCountries: string[];
    blockVPN: boolean;
    blockTor: boolean;
  };
  deviceFingerprinting: boolean;
  sessionConcurrency: number;
  requireMFA: boolean;
}

export interface SecurityContext {
  ipAddress: string;
  userAgent: string;
  geolocation?: GeoLocation;
  deviceFingerprint?: string;
  riskScore: number;
  threatIndicators: string[];
}

export interface SecurityValidationResult {
  isAllowed: boolean;
  riskScore: number;
  violations: string[];
  securityContext: SecurityContext;
}

/**
 * Security Policy Service
 * Handles IP restrictions, geolocation validation, and security policy enforcement
 */
export class SecurityPolicyService {
  private static readonly DEFAULT_POLICY: SecurityPolicy = {
    ipWhitelist: undefined, // No IP restrictions by default
    geoRestrictions: {
      allowedCountries: [], // Empty means all countries allowed
      blockVPN: false,
      blockTor: true,
    },
    deviceFingerprinting: true,
    sessionConcurrency: 3, // Max 3 concurrent sessions
    requireMFA: true,
  };

  /**
   * Validate security policy for system user login
   */
  static async validateSecurityPolicy(
    req: Request,
    userId: string
  ): Promise<SecurityValidationResult> {
    try {
      // Get user's security policy (or use default)
      const policy = await this.getUserSecurityPolicy(userId);
      
      // Build security context
      const securityContext = await this.buildSecurityContext(req);
      
      // Validate against policy
      const violations: string[] = [];
      let riskScore = securityContext.riskScore;

      // IP Whitelist validation
      if (policy.ipWhitelist && policy.ipWhitelist.length > 0) {
        if (!this.isIPWhitelisted(securityContext.ipAddress, policy.ipWhitelist)) {
          violations.push('IP address not whitelisted');
          riskScore += 30;
        }
      }

      // Geolocation validation
      if (securityContext.geolocation) {
        const geoViolations = this.validateGeolocation(
          securityContext.geolocation,
          policy.geoRestrictions
        );
        violations.push(...geoViolations);
        if (geoViolations.length > 0) {
          riskScore += 25;
        }
      }

      // Session concurrency validation
      const concurrentSessions = await this.getConcurrentSessionCount(userId);
      if (concurrentSessions >= policy.sessionConcurrency) {
        violations.push('Maximum concurrent sessions exceeded');
        riskScore += 20;
      }

      // Threat detection
      const threatViolations = this.detectThreats(securityContext);
      violations.push(...threatViolations);
      if (threatViolations.length > 0) {
        riskScore += 40;
      }

      // Cap risk score at 100
      riskScore = Math.min(riskScore, 100);

      return {
        isAllowed: violations.length === 0 && riskScore < 70, // Block if high risk
        riskScore,
        violations,
        securityContext: {
          ...securityContext,
          riskScore,
        },
      };
    } catch (error) {
      console.error('Security policy validation failed:', error);
      return {
        isAllowed: false,
        riskScore: 100,
        violations: ['Security validation failed'],
        securityContext: {
          ipAddress: this.getClientIP(req),
          userAgent: req.headers['user-agent'] || 'unknown',
          riskScore: 100,
          threatIndicators: ['validation_error'],
        },
      };
    }
  }

  /**
   * Update security policy for a system user
   */
  static async updateUserSecurityPolicy(
    userId: string,
    policy: Partial<SecurityPolicy>
  ): Promise<void> {
    try {
      // In a real implementation, this would be stored in the database
      // For now, we'll use a simple in-memory cache or default policy
      console.log(`Updating security policy for user ${userId}:`, policy);
    } catch (error) {
      console.error('Failed to update security policy:', error);
      throw new Error('Failed to update security policy');
    }
  }

  /**
   * Get user's security policy
   */
  private static async getUserSecurityPolicy(userId: string): Promise<SecurityPolicy> {
    try {
      // Get system user role to determine policy
      const systemUser = await prisma.systemUser.findUnique({
        where: { id: userId },
        select: { role: true, permissions: true },
      });

      if (!systemUser) {
        return this.DEFAULT_POLICY;
      }

      // Apply role-based security policies
      const policy = { ...this.DEFAULT_POLICY };

      // Super admins get stricter policies
      if (systemUser.role === 'SUPER_ADMIN') {
        policy.requireMFA = true;
        policy.sessionConcurrency = 2;
        policy.geoRestrictions = {
          allowedCountries: policy.geoRestrictions?.allowedCountries || [],
          blockVPN: true,
          blockTor: true,
        };
      }

      return policy;
    } catch (error) {
      console.error('Failed to get user security policy:', error);
      return this.DEFAULT_POLICY;
    }
  }

  /**
   * Build security context from request
   */
  private static async buildSecurityContext(req: Request): Promise<SecurityContext> {
    const ipAddress = this.getClientIP(req);
    const userAgent = req.headers['user-agent'] || 'unknown';
    
    // Get geolocation
    let geolocation: GeoLocation | undefined;
    try {
      geolocation = await GeoLocationService.getLocation(ipAddress);
    } catch (error) {
      console.warn('Geolocation lookup failed:', error);
    }

    // Calculate base risk score
    let riskScore = 0;
    const threatIndicators: string[] = [];

    // Check for suspicious user agents
    if (this.isSuspiciousUserAgent(userAgent)) {
      riskScore += 15;
      threatIndicators.push('suspicious_user_agent');
    }

    // Check for private/local IPs (higher risk in production)
    if (this.isPrivateIP(ipAddress)) {
      riskScore += 10;
      threatIndicators.push('private_ip');
    }

    // Generate device fingerprint
    const deviceFingerprint = this.generateDeviceFingerprint(req);

    return {
      ipAddress,
      userAgent,
      geolocation,
      deviceFingerprint,
      riskScore,
      threatIndicators,
    };
  }

  /**
   * Check if IP is whitelisted
   */
  private static isIPWhitelisted(ipAddress: string, whitelist: string[]): boolean {
    return whitelist.some(whitelistedIP => {
      // Support CIDR notation and exact matches
      if (whitelistedIP.includes('/')) {
        // CIDR notation - simplified check (would need proper IP library in production)
        return whitelistedIP.split('/')[0] === ipAddress.split('.').slice(0, 3).join('.');
      }
      return whitelistedIP === ipAddress;
    });
  }

  /**
   * Validate geolocation against policy
   */
  private static validateGeolocation(
    geolocation: GeoLocation,
    geoRestrictions?: SecurityPolicy['geoRestrictions']
  ): string[] {
    const violations: string[] = [];

    if (!geoRestrictions) return violations;

    // Check allowed countries
    if (geoRestrictions.allowedCountries.length > 0) {
      if (!geoRestrictions.allowedCountries.includes(geolocation.country)) {
        violations.push(`Access from ${geolocation.country} not allowed`);
      }
    }

    // Check VPN blocking
    if (geoRestrictions.blockVPN && geolocation.isVPN) {
      violations.push('VPN access blocked');
    }

    // Check Tor blocking
    if (geoRestrictions.blockTor && geolocation.isTor) {
      violations.push('Tor access blocked');
    }

    return violations;
  }

  /**
   * Get concurrent session count for user
   */
  private static async getConcurrentSessionCount(userId: string): Promise<number> {
    try {
      const activeSessionsCount = await prisma.systemUserSession.count({
        where: {
          userId,
          expiresAt: {
            gt: new Date(),
          },
        },
      });

      return activeSessionsCount;
    } catch (error) {
      console.error('Failed to get concurrent session count:', error);
      return 0;
    }
  }

  /**
   * Detect security threats
   */
  private static detectThreats(securityContext: SecurityContext): string[] {
    const threats: string[] = [];

    // Check for known bad user agents
    if (securityContext.userAgent.toLowerCase().includes('bot')) {
      threats.push('automated_access_detected');
    }

    // Check for suspicious IP patterns
    if (securityContext.geolocation?.isProxy) {
      threats.push('proxy_detected');
    }

    // Check for multiple threat indicators
    if (securityContext.threatIndicators.length > 2) {
      threats.push('multiple_threat_indicators');
    }

    return threats;
  }

  /**
   * Get client IP address from request
   */
  private static getClientIP(req: Request): string {
    const forwarded = req.headers['x-forwarded-for'] as string;
    const real = req.headers['x-real-ip'] as string;
    const clientIP = forwarded?.split(',')?.[0] || real || req.socket.remoteAddress || '127.0.0.1';
    
    // Clean up IPv6 mapped IPv4 addresses
    return clientIP.replace(/^::ffff:/, '');
  }

  /**
   * Check if user agent is suspicious
   */
  private static isSuspiciousUserAgent(userAgent: string): boolean {
    const suspiciousPatterns = [
      /curl/i,
      /wget/i,
      /python/i,
      /bot/i,
      /spider/i,
      /crawler/i,
      /scanner/i,
    ];

    return suspiciousPatterns.some(pattern => pattern.test(userAgent));
  }

  /**
   * Check if IP is private/local
   */
  private static isPrivateIP(ip: string): boolean {
    const privateRanges = [
      /^127\./,          // *********/8
      /^10\./,           // 10.0.0.0/8
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./,  // **********/12
      /^192\.168\./,     // ***********/16
      /^::1$/,           // IPv6 loopback
      /^fc00:/,          // IPv6 unique local
    ];

    return privateRanges.some(range => range.test(ip));
  }

  /**
   * Generate device fingerprint
   */
  private static generateDeviceFingerprint(req: Request): string {
    const components = [
      req.headers['user-agent'] || '',
      req.headers['accept-language'] || '',
      req.headers['accept-encoding'] || '',
      req.headers['accept'] || '',
    ];

    // Simple hash of fingerprint components
    const fingerprint = Buffer.from(components.join('|')).toString('base64');
    return fingerprint.substring(0, 16); // Truncate for readability
  }
}