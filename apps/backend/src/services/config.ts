/**
 * Environment Configuration Service
 * Superman-level configuration management with security and validation
 */

import { getEnv, type EnvConfig } from '@underwriting/config';
import { ErrorHandlingService } from './error-handling';

export interface DatabaseConfig {
  url: string;
  maxConnections: number;
  connectionTimeout: number;
  queryTimeout: number;
}

export interface RedisConfig {
  url: string;
  maxConnections: number;
  connectTimeout: number;
  commandTimeout: number;
}

export interface SecurityConfig {
  jwtSecret: string;
  systemJwtSecret: string;
  sessionSecret: string;
  bcryptRounds: number;
  jwtExpiresIn: string;
  corsOrigins: string[];
  rateLimitWindow: number;
  rateLimitMax: number;
}

export interface ServiceConfig {
  openrouter: {
    apiKey?: string;
    baseUrl: string;
  };
  ollama: {
    url: string;
    model: string;
  };
  smtp: {
    host: string;
    port: number;
    user: string;
    pass: string;
    from: string;
    secure: boolean;
  };
  jumio?: {
    apiKey: string;
    apiSecret: string;
    baseUrl: string;
  };
  plaid?: {
    clientId: string;
    secret: string;
    environment: 'sandbox' | 'development' | 'production';
  };
}

export interface AppConfig {
  environment: 'development' | 'production' | 'test';
  nodeEnv: string;
  port: number;
  logLevel: 'error' | 'warn' | 'info' | 'debug';
  enableMetrics: boolean;
  enableSwagger: boolean;
  database: DatabaseConfig;
  redis: RedisConfig;
  security: SecurityConfig;
  services: ServiceConfig;
}

class ConfigurationService {
  private static instance: ConfigurationService;
  private config: AppConfig;
  private env: EnvConfig;

  private constructor() {
    this.env = getEnv();
    this.config = this.buildConfiguration();
    this.validateConfiguration();
  }

  public static getInstance(): ConfigurationService {
    if (!ConfigurationService.instance) {
      ConfigurationService.instance = new ConfigurationService();
    }
    return ConfigurationService.instance;
  }

  /**
   * Get complete application configuration
   */
  public getConfig(): AppConfig {
    return { ...this.config }; // Return copy to prevent mutation
  }

  /**
   * Get database configuration
   */
  public getDatabaseConfig(): DatabaseConfig {
    return { ...this.config.database };
  }

  /**
   * Get Redis configuration
   */
  public getRedisConfig(): RedisConfig {
    return { ...this.config.redis };
  }

  /**
   * Get security configuration
   */
  public getSecurityConfig(): SecurityConfig {
    return { ...this.config.security };
  }

  /**
   * Get external services configuration
   */
  public getServicesConfig(): ServiceConfig {
    return { ...this.config.services };
  }

  /**
   * Check if running in development mode
   */
  public isDevelopment(): boolean {
    return this.config.environment === 'development';
  }

  /**
   * Check if running in production mode
   */
  public isProduction(): boolean {
    return this.config.environment === 'production';
  }

  /**
   * Check if running in test mode
   */
  public isTest(): boolean {
    return this.config.environment === 'test';
  }

  /**
   * Get configuration value by path (dot notation)
   */
  public get<T = unknown>(path: string): T | undefined {
    const keys = path.split('.');
    let current: unknown = this.config;

    for (const key of keys) {
      if (current === null || current === undefined || typeof current !== 'object') {
        return undefined;
      }
      current = (current as Record<string, unknown>)[key];
    }

    return current as T;
  }

  /**
   * Check if a configuration value exists
   */
  public has(path: string): boolean {
    return this.get(path) !== undefined;
  }

  /**
   * Build application configuration from environment variables
   */
  private buildConfiguration(): AppConfig {
    const corsOrigins = this.env.CORS_ORIGINS 
      ? this.env.CORS_ORIGINS.split(',').map(origin => origin.trim())
      : ['http://localhost:3000', 'http://localhost:3001'];

    return {
      environment: this.env.NODE_ENV as 'development' | 'production' | 'test',
      nodeEnv: this.env.NODE_ENV,
      port: this.env.BACKEND_PORT,
      logLevel: (this.env.LOG_LEVEL as 'error' | 'warn' | 'info' | 'debug') || 'info',
      enableMetrics: String(this.env.ENABLE_METRICS) === 'true',
      enableSwagger: String(this.env.ENABLE_SWAGGER) === 'true' || this.env.NODE_ENV === 'development',

      database: {
        url: this.env.DATABASE_URL,
        maxConnections: this.env.DB_MAX_CONNECTIONS || 10,
        connectionTimeout: this.env.DB_CONNECTION_TIMEOUT || 30000,
        queryTimeout: this.env.DB_QUERY_TIMEOUT || 60000,
      },

      redis: {
        url: this.env.REDIS_URL,
        maxConnections: this.env.REDIS_MAX_CONNECTIONS || 10,
        connectTimeout: this.env.REDIS_CONNECT_TIMEOUT || 10000,
        commandTimeout: this.env.REDIS_COMMAND_TIMEOUT || 5000,
      },

      security: {
        jwtSecret: this.env.JWT_SECRET,
        systemJwtSecret: this.env.SYSTEM_JWT_SECRET || this.env.JWT_SECRET + '_system',
        sessionSecret: this.env.SESSION_SECRET,
        bcryptRounds: this.env.BCRYPT_ROUNDS,
        jwtExpiresIn: this.env.JWT_EXPIRES_IN,
        corsOrigins,
        rateLimitWindow: this.env.RATE_LIMIT_WINDOW || 900000, // 15 minutes
        rateLimitMax: this.env.RATE_LIMIT_MAX || 100,
      },

      services: {
        openrouter: {
          apiKey: this.env.OPENROUTER_API_KEY,
          baseUrl: this.env.OPENROUTER_BASE_URL,
        },
        ollama: {
          url: this.env.OLLAMA_URL,
          model: this.env.OLLAMA_MODEL,
        },
        smtp: {
          host: this.env.SMTP_HOST || 'localhost',
          port: this.env.SMTP_PORT || 587,
          user: this.env.SMTP_USER || '',
          pass: this.env.SMTP_PASS || '',
          from: this.env.SMTP_FROM || '<EMAIL>',
          secure: String(this.env.SMTP_SECURE) === 'true',
        },
        jumio: this.env.JUMIO_API_KEY ? {
          apiKey: this.env.JUMIO_API_KEY,
          apiSecret: this.env.JUMIO_API_SECRET!,
          baseUrl: this.env.JUMIO_BASE_URL || 'https://netverify.com/api/netverify/v2',
        } : undefined,
        plaid: this.env.PLAID_CLIENT_ID ? {
          clientId: this.env.PLAID_CLIENT_ID,
          secret: this.env.PLAID_SECRET!,
          environment: (this.env.PLAID_ENVIRONMENT as 'sandbox' | 'development' | 'production') || 'sandbox',
        } : undefined,
      },
    };
  }

  /**
   * Validate configuration for security and completeness
   */
  private validateConfiguration(): void {
    const errors: string[] = [];

    // Validate required security configurations
    if (this.config.security.jwtSecret.length < 32) {
      errors.push('JWT authentication key must be at least 32 characters long');
    }

    if (this.config.security.sessionSecret.length < 32) {
      errors.push('Session authentication key must be at least 32 characters long');
    }

    if (this.config.security.systemJwtSecret.length < 32) {
      errors.push('System JWT authentication key must be at least 32 characters long');
    }

    if (this.config.security.bcryptRounds < 10) {
      errors.push('BCrypt rounds should be at least 10 for security');
    }

    // Validate database configuration
    if (!this.config.database.url || !this.config.database.url.startsWith('postgresql://')) {
      errors.push('Invalid database URL - must be a PostgreSQL connection string');
    }

    if (!this.config.redis.url || (!this.config.redis.url.startsWith('redis://') && !this.config.redis.url.startsWith('rediss://'))) {
      errors.push('Invalid Redis URL - must be a Redis connection string');
    }

    // Validate production-specific requirements
    if (this.config.environment === 'production') {
      if (!this.config.services.openrouter.apiKey) {
        errors.push('OpenRouter API key is required in production');
      }

      if (this.config.security.jwtSecret === this.config.security.sessionSecret) {
        errors.push('JWT and session authentication keys should be different in production');
      }

      if (this.config.security.corsOrigins.includes('*') || 
          this.config.security.corsOrigins.some(origin => origin.includes('localhost'))) {
        errors.push('Production CORS origins should not include wildcards or localhost');
      }

      if (this.config.logLevel === 'debug') {
        errors.push('Debug logging should not be enabled in production');
      }
    }

    // Validate service configurations if provided
    if (this.config.services.jumio) {
      if (!this.config.services.jumio.apiSecret) {
        errors.push('Jumio API credential is required when API key is provided');
      }
    }

    if (this.config.services.plaid) {
      if (!this.config.services.plaid.secret) {
        errors.push('Plaid credential is required when client ID is provided');
      }
    }

    // Validate SMTP configuration for production
    if (this.config.environment === 'production' && 
        (!this.config.services.smtp.host || !this.config.services.smtp.user)) {
      errors.push('SMTP configuration is required in production');
    }

    if (errors.length > 0) {
      throw ErrorHandlingService.createInternalError(
        `Configuration validation failed: ${errors.join(', ')}`,
        'Invalid server configuration'
      );
    }
  }

  /**
   * Get sanitized configuration for logging (removes sensitive data)
   */
  public getSanitizedConfig(): Record<string, unknown> {
    const sanitized = JSON.parse(JSON.stringify(this.config)) as {
      security: { jwtSecret: string; systemJwtSecret: string; sessionSecret: string };
      database: { url: string };
      redis: { url: string };
      services: {
        openrouter: { apiKey?: string };
        smtp: { pass?: string };
        jumio?: { apiSecret?: string };
        plaid?: { secret?: string };
      };
    };
    
    // Remove or mask sensitive data
    sanitized.security.jwtSecret = '***MASKED***';
    sanitized.security.systemJwtSecret = '***MASKED***';
    sanitized.security.sessionSecret = '***MASKED***';
    sanitized.database.url = this.maskConnectionString(sanitized.database.url);
    sanitized.redis.url = this.maskConnectionString(sanitized.redis.url);
    
    if (sanitized.services.openrouter.apiKey) {
      sanitized.services.openrouter.apiKey = '***MASKED***';
    }
    
    if (sanitized.services.smtp.pass) {
      sanitized.services.smtp.pass = '***MASKED***';
    }
    
    if (sanitized.services.jumio?.apiSecret) {
      sanitized.services.jumio.apiSecret = '***MASKED***';
    }
    
    if (sanitized.services.plaid?.secret) {
      sanitized.services.plaid.secret = '***MASKED***';
    }

    return sanitized;
  }

  /**
   * Mask connection strings for logging
   */
  private maskConnectionString(connectionString: string): string {
    try {
      const url = new URL(connectionString);
      if (url.password) {
        url.password = '***MASKED***';
      }
      return url.toString();
    } catch {
      return '***INVALID_URL***';
    }
  }

  /**
   * Reload configuration from environment (for testing/development)
   */
  public reload(): void {
    if (this.config.environment === 'production') {
      throw ErrorHandlingService.createAuthorizationError(
        'Configuration reload is not allowed in production',
        'Operation not permitted'
      );
    }

    this.env = getEnv();
    this.config = this.buildConfiguration();
    this.validateConfiguration();
  }
}

// Export singleton instance
export const configService = ConfigurationService.getInstance();
export default configService;