import axios from 'axios';

export interface GeoLocation {
  country: string;
  countryCode: string;
  region: string;
  city: string;
  latitude: number;
  longitude: number;
  timezone: string;
  isp: string;
  organization: string;
  isVPN: boolean;
  isTor: boolean;
  isProxy: boolean;
  threatLevel: 'low' | 'medium' | 'high';
}

/**
 * Geolocation Service
 * Provides IP geolocation and threat detection capabilities
 */
export class GeoLocationService {
  private static readonly CACHE_TTL = 60 * 60 * 1000; // 1 hour
  private static readonly locationCache = new Map<string, { data: GeoLocation; timestamp: number }>();

  /**
   * Get geolocation data for an IP address
   */
  static async getLocation(ipAddress: string): Promise<GeoLocation> {
    try {
      // Check cache first
      const cached = this.locationCache.get(ipAddress);
      if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
        return cached.data;
      }

      // Skip geolocation for private IPs
      if (this.isPrivateIP(ipAddress)) {
        const localData: GeoLocation = {
          country: 'Local',
          countryCode: 'LOCAL',
          region: 'Local',
          city: 'Local',
          latitude: 0,
          longitude: 0,
          timezone: 'UTC',
          isp: 'Local Network',
          organization: 'Local Network',
          isVPN: false,
          isTor: false,
          isProxy: false,
          threatLevel: 'low',
        };
        
        this.locationCache.set(ipAddress, { data: localData, timestamp: Date.now() });
        return localData;
      }

      // Use a free geolocation service (in production, use a paid service)
      const location = await this.fetchFromFreeGeoIP(ipAddress);
      
      // Cache the result
      this.locationCache.set(ipAddress, { data: location, timestamp: Date.now() });
      
      return location;
    } catch (error) {
      console.error('Geolocation lookup failed:', error);
      
      // Return default location on error
      const fallbackData: GeoLocation = {
        country: 'Unknown',
        countryCode: 'XX',
        region: 'Unknown',
        city: 'Unknown',
        latitude: 0,
        longitude: 0,
        timezone: 'UTC',
        isp: 'Unknown',
        organization: 'Unknown',
        isVPN: false,
        isTor: false,
        isProxy: false,
        threatLevel: 'medium', // Unknown is medium risk
      };
      
      return fallbackData;
    }
  }

  /**
   * Check if an IP address is from a high-risk location
   */
  static async isHighRiskLocation(ipAddress: string): Promise<boolean> {
    try {
      const location = await this.getLocation(ipAddress);
      
      // High-risk indicators
      const highRiskCountries = ['XX', 'Local']; // Add actual high-risk country codes
      const highRiskFactors = [
        highRiskCountries.includes(location.countryCode),
        location.isVPN,
        location.isTor,
        location.isProxy,
        location.threatLevel === 'high',
      ];
      
      return highRiskFactors.some(factor => factor);
    } catch {
      return true; // Treat unknown as high risk
    }
  }

  /**
   * Get threat score for an IP address
   */
  static async getThreatScore(ipAddress: string): Promise<number> {
    try {
      const location = await this.getLocation(ipAddress);
      let score = 0;
      
      // Base threat scoring
      if (location.isVPN) score += 30;
      if (location.isTor) score += 50;
      if (location.isProxy) score += 25;
      
      switch (location.threatLevel) {
        case 'high':
          score += 40;
          break;
        case 'medium':
          score += 20;
          break;
        case 'low':
          score += 0;
          break;
      }
      
      // Cap at 100
      return Math.min(score, 100);
    } catch {
      return 50; // Medium risk for unknown
    }
  }

  /**
   * Batch lookup for multiple IP addresses
   */
  static async getLocationsBatch(ipAddresses: string[]): Promise<Map<string, GeoLocation>> {
    const results = new Map<string, GeoLocation>();
    
    // Process in parallel with rate limiting
    const batchSize = 5;
    for (let i = 0; i < ipAddresses.length; i += batchSize) {
      const batch = ipAddresses.slice(i, i + batchSize);
      const promises = batch.map(async (ip) => {
        try {
          const location = await this.getLocation(ip);
          results.set(ip, location);
        } catch (error) {
          console.error(`Failed to get location for ${ip}:`, error);
        }
      });
      
      await Promise.all(promises);
      
      // Rate limiting delay
      if (i + batchSize < ipAddresses.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    return results;
  }

  /**
   * Clear cache for specific IP or all IPs
   */
  static clearCache(ipAddress?: string): void {
    if (ipAddress) {
      this.locationCache.delete(ipAddress);
    } else {
      this.locationCache.clear();
    }
  }

  /**
   * Get cache statistics
   */
  static getCacheStats(): { size: number; hitRate: number } {
    // This is a simplified implementation
    // In production, you'd track hits/misses properly
    return {
      size: this.locationCache.size,
      hitRate: 0.85, // Placeholder
    };
  }

  /**
   * Fetch geolocation from free service
   * Note: In production, use a paid service like MaxMind, IPinfo, or similar
   */
  private static async fetchFromFreeGeoIP(ipAddress: string): Promise<GeoLocation> {
    try {
      // Using ipapi.co as a free service (has rate limits)
      const response = await axios.get(`https://ipapi.co/${ipAddress}/json/`, {
        timeout: 5000,
        headers: {
          'User-Agent': 'Underwriting Platform Security Service',
        },
      });

      const data = response.data as {
        error?: boolean;
        reason?: string;
        country_name?: string;
        country_code?: string;
        region?: string;
        city?: string;
        latitude?: string | number;
        longitude?: string | number;
        timezone?: string;
        org?: string;
      };
      
      if (data.error) {
        throw new Error(`Geolocation API error: ${data.reason || 'Unknown error'}`);
      }

      // Map the response to our interface
      const location: GeoLocation = {
        country: data.country_name || 'Unknown',
        countryCode: data.country_code || 'XX',
        region: data.region || 'Unknown',
        city: data.city || 'Unknown',
        latitude: typeof data.latitude === 'string' ? parseFloat(data.latitude) : (data.latitude || 0),
        longitude: typeof data.longitude === 'string' ? parseFloat(data.longitude) : (data.longitude || 0),
        timezone: data.timezone || 'UTC',
        isp: data.org || 'Unknown ISP',
        organization: data.org || 'Unknown Organization',
        isVPN: this.detectVPN(data),
        isTor: this.detectTor(data),
        isProxy: this.detectProxy(data),
        threatLevel: this.calculateThreatLevel(data),
      };

      return location;
    } catch (error) {
      console.error('Free GeoIP service failed:', error);
      throw error;
    }
  }

  /**
   * Detect VPN usage (simplified detection)
   */
  private static detectVPN(data: { org?: string }): boolean {
    const org = data.org?.toLowerCase() || '';
    const vpnIndicators = [
      'vpn',
      'virtual private network',
      'proxy',
      'hosting',
      'cloud',
      'datacenter',
      'server',
    ];
    
    return vpnIndicators.some(indicator => org.includes(indicator));
  }

  /**
   * Detect Tor usage
   */
  private static detectTor(data: { org?: string }): boolean {
    const org = data.org?.toLowerCase() || '';
    return org.includes('tor') || org.includes('onion');
  }

  /**
   * Detect proxy usage
   */
  private static detectProxy(data: { org?: string }): boolean {
    const org = data.org?.toLowerCase() || '';
    const proxyIndicators = ['proxy', 'relay', 'anonymizer'];
    
    return proxyIndicators.some(indicator => org.includes(indicator));
  }

  /**
   * Calculate threat level based on various factors
   */
  private static calculateThreatLevel(data: { org?: string; country_code?: string }): 'low' | 'medium' | 'high' {
    const org = data.org?.toLowerCase() || '';
    const country = data.country_code?.toLowerCase() || '';
    
    // High-risk indicators
    const highRiskCountries = ['xx', 'unknown']; // Add actual high-risk countries
    const suspiciousOrgs = ['hosting', 'datacenter', 'cloud', 'server'];
    
    if (highRiskCountries.includes(country)) {
      return 'high';
    }
    
    if (suspiciousOrgs.some(suspicious => org.includes(suspicious))) {
      return 'medium';
    }
    
    return 'low';
  }

  /**
   * Check if IP is private/local
   */
  private static isPrivateIP(ip: string): boolean {
    const privateRanges = [
      /^127\./,          // *********/8
      /^10\./,           // 10.0.0.0/8
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./,  // **********/12
      /^192\.168\./,     // ***********/16
      /^::1$/,           // IPv6 loopback
      /^fc00:/,          // IPv6 unique local
      /^localhost$/i,    // localhost
    ];

    return privateRanges.some(range => range.test(ip));
  }
}