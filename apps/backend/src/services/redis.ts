import Redis from 'ioredis';
import { getEnv } from '@underwriting/config';

const env = getEnv();

// Redis configuration
const redisConfig = {
  host: 'localhost',
  port: env.REDIS_PORT,
  retryDelayOnFailover: 100,
  retryDelayOnClusterDown: 300,
  retryDelayOnClusterFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  keepAlive: 30000,
  family: 4, // 4 (IPv4) or 6 (IPv6)
  keyPrefix: 'underwriting:',
};

// Create Redis instances
export const redis = new Redis(redisConfig);
export const sessionRedis = new Redis({
  ...redisConfig,
  keyPrefix: 'underwriting:session:',
});

// Redis connection event handlers
redis.on('connect', () => {
  console.log('✅ Redis connected');
});

redis.on('error', (error) => {
  console.error('❌ Redis connection error:', error);
});

redis.on('ready', () => {
  console.log('🚀 Redis ready for operations');
});

sessionRedis.on('connect', () => {
  console.log('✅ Session Redis connected');
});

sessionRedis.on('error', (error) => {
  console.error('❌ Session Redis connection error:', error);
});

// Session management utilities
export class SessionManager {
  private static readonly SESSION_TTL = 24 * 60 * 60; // 24 hours in seconds
  private static readonly REFRESH_TOKEN_TTL = 7 * 24 * 60 * 60; // 7 days in seconds

  /**
   * Store session data in Redis
   */
  static async setSession(sessionId: string, data: unknown): Promise<void> {
    try {
      await sessionRedis.setex(
        `session:${sessionId}`,
        this.SESSION_TTL,
        JSON.stringify(data)
      );
    } catch (error) {
      console.error('Failed to set session:', error);
      throw new Error('Session storage failed');
    }
  }

  /**
   * Get session data from Redis
   */
  static async getSession(sessionId: string): Promise<unknown | null> {
    try {
      const data = await sessionRedis.get(`session:${sessionId}`);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Failed to get session:', error);
      return null;
    }
  }

  /**
   * Delete session from Redis
   */
  static async deleteSession(sessionId: string): Promise<void> {
    try {
      await sessionRedis.del(`session:${sessionId}`);
    } catch (error) {
      console.error('Failed to delete session:', error);
    }
  }

  /**
   * Update session TTL
   */
  static async refreshSession(sessionId: string): Promise<void> {
    try {
      await sessionRedis.expire(`session:${sessionId}`, this.SESSION_TTL);
    } catch (error) {
      console.error('Failed to refresh session:', error);
    }
  }

  /**
   * Store refresh token
   */
  static async setRefreshToken(tokenId: string, userId: string): Promise<void> {
    try {
      await sessionRedis.setex(
        `refresh:${tokenId}`,
        this.REFRESH_TOKEN_TTL,
        userId
      );
    } catch (error) {
      console.error('Failed to set refresh token:', error);
      throw new Error('Refresh token storage failed');
    }
  }

  /**
   * Get user ID from refresh token
   */
  static async getRefreshToken(tokenId: string): Promise<string | null> {
    try {
      return await sessionRedis.get(`refresh:${tokenId}`);
    } catch (error) {
      console.error('Failed to get refresh token:', error);
      return null;
    }
  }

  /**
   * Delete refresh token
   */
  static async deleteRefreshToken(tokenId: string): Promise<void> {
    try {
      await sessionRedis.del(`refresh:${tokenId}`);
    } catch (error) {
      console.error('Failed to delete refresh token:', error);
    }
  }

  /**
   * Delete all sessions for a user
   */
  static async deleteAllUserSessions(userId: string): Promise<void> {
    try {
      const pattern = `session:*${userId}*`;
      const keys = await sessionRedis.keys(pattern);

      if (keys.length > 0) {
        await sessionRedis.del(...keys);
      }
    } catch (error) {
      console.error('Failed to delete user sessions:', error);
    }
  }

  /**
   * Get active session count for user
   */
  static async getUserSessionCount(userId: string): Promise<number> {
    try {
      const pattern = `session:*${userId}*`;
      const keys = await sessionRedis.keys(pattern);
      return keys.length;
    } catch (error) {
      console.error('Failed to get user session count:', error);
      return 0;
    }
  }
}

// Cache utilities for general purpose caching
export class CacheManager {
  private static readonly DEFAULT_TTL = 60 * 60; // 1 hour in seconds

  /**
   * Set cache value with TTL
   */
  static async set(
    key: string,
    value: unknown,
    ttl: number = this.DEFAULT_TTL
  ): Promise<void> {
    try {
      await redis.setex(key, ttl, JSON.stringify(value));
    } catch (error) {
      console.error('Failed to set cache:', error);
    }
  }

  /**
   * Get cache value
   */
  static async get(key: string): Promise<unknown | null> {
    try {
      const data = await redis.get(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Failed to get cache:', error);
      return null;
    }
  }

  /**
   * Delete cache value
   */
  static async delete(key: string): Promise<void> {
    try {
      await redis.del(key);
    } catch (error) {
      console.error('Failed to delete cache:', error);
    }
  }

  /**
   * Check if cache key exists
   */
  static async exists(key: string): Promise<boolean> {
    try {
      const result = await redis.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Failed to check cache existence:', error);
      return false;
    }
  }

  /**
   * Set cache with pattern expiration
   */
  static async setPattern(
    pattern: string,
    value: unknown,
    ttl: number = this.DEFAULT_TTL
  ): Promise<void> {
    try {
      await redis.setex(pattern, ttl, JSON.stringify(value));
    } catch (error) {
      console.error('Failed to set pattern cache:', error);
    }
  }

  /**
   * Delete all keys matching pattern
   */
  static async deletePattern(pattern: string): Promise<void> {
    try {
      const keys = await redis.keys(pattern);
      if (keys.length > 0) {
        await redis.del(...keys);
      }
    } catch (error) {
      console.error('Failed to delete pattern cache:', error);
    }
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('Closing Redis connections...');
  await redis.quit();
  await sessionRedis.quit();
});

process.on('SIGINT', async () => {
  console.log('Closing Redis connections...');
  await redis.quit();
  await sessionRedis.quit();
});

// RedisService class for dependency injection
export class RedisService {
  private client: Redis;

  constructor() {
    this.client = redis;
  }

  async get(key: string): Promise<string | null> {
    return this.client.get(key);
  }

  async set(key: string, value: string): Promise<void> {
    await this.client.set(key, value);
  }

  async setex(key: string, seconds: number, value: string): Promise<void> {
    await this.client.setex(key, seconds, value);
  }

  async del(key: string): Promise<void> {
    await this.client.del(key);
  }

  async exists(key: string): Promise<boolean> {
    const result = await this.client.exists(key);
    return result === 1;
  }
}

export default redis;
