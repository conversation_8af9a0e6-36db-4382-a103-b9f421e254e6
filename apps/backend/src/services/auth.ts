import { User, User<PERSON><PERSON>, <PERSON><PERSON>ser, SystemUserRole } from '@prisma/client';
import { prisma } from '../prisma/client';
import { JWTService, TokenPair } from '../utils/jwt';
import { PasswordService } from '../utils/password';
import { SessionManager } from './redis';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface LoginResult {
  success: boolean;
  tokens?: TokenPair;
  user?: {
    id: string;
    email: string;
    name?: string;
    role: string;
    tenantId?: string;
    isSystemUser: boolean;
  };
  error?: string;
}

export interface CreateUserData {
  email: string;
  password?: string;
  name?: string;
  tenantId: string;
  role?: UserRole;
}

export interface CreateSystemUserData {
  email: string;
  password: string;
  name: string;
  role: SystemUserRole;
}

export class AuthService {
  /**
   * Authenticate user (tenant user)
   */
  static async login(credentials: LoginCredentials): Promise<LoginResult> {
    try {
      const { email, password } = credentials;

      // Find user by email
      const user = await prisma.user.findUnique({
        where: { email },
        include: { tenant: true },
      });

      if (!user || !user.passwordHash) {
        return {
          success: false,
          error: 'Invalid email or password',
        };
      }

      // Check if user is active
      if (!user.tenant || user.tenant.status !== 'ACTIVE') {
        return {
          success: false,
          error: 'Account is suspended or inactive',
        };
      }

      // Verify password
      const isPasswordValid = await PasswordService.verifyPassword(
        password,
        user.passwordHash
      );
      if (!isPasswordValid) {
        return {
          success: false,
          error: 'Invalid email or password',
        };
      }

      // Create session
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Store session in database
      const session = await prisma.userSession.create({
        data: {
          userId: user.id,
          sessionToken: '', // Will be updated with JWT
          expiresAt,
          device: 'web',
          ipAddress: null, // Will be set by controller
          userAgent: null, // Will be set by controller
        },
      });

      // Generate tokens
      const tokens = JWTService.generateTokenPair(
        {
          userId: user.id,
          email: user.email,
          role: user.role,
          tenantId: user.tenantId,
          isSystemUser: false,
        },
        session.id
      );

      // Update session with access token
      await prisma.userSession.update({
        where: { id: session.id },
        data: { sessionToken: tokens.accessToken },
      });

      // Store session data in Redis
      await SessionManager.setSession(session.id, {
        userId: user.id,
        email: user.email,
        role: user.role,
        tenantId: user.tenantId,
        isSystemUser: false,
      });

      // Update last login
      await prisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() },
      });

      return {
        success: true,
        tokens,
        user: {
          id: user.id,
          email: user.email,
          name: user.name || undefined,
          role: user.role,
          tenantId: user.tenantId,
          isSystemUser: false,
        },
      };
    } catch (error) {
      console.error('Login failed:', error);
      return {
        success: false,
        error: 'Login failed',
      };
    }
  }

  /**
   * Authenticate system user (platform admin)
   */
  static async systemLogin(
    credentials: LoginCredentials
  ): Promise<LoginResult> {
    try {
      const { email, password } = credentials;

      // Find system user by email
      const systemUser = await prisma.systemUser.findUnique({
        where: { email },
      });

      if (!systemUser) {
        return {
          success: false,
          error: 'Invalid email or password',
        };
      }

      // Check if system user is active
      if (!systemUser.isActive) {
        return {
          success: false,
          error: 'Account is suspended',
        };
      }

      // Verify password
      const isPasswordValid = await PasswordService.verifyPassword(
        password,
        systemUser.passwordHash
      );
      if (!isPasswordValid) {
        return {
          success: false,
          error: 'Invalid email or password',
        };
      }

      // Create session
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Store session in database
      const session = await prisma.systemUserSession.create({
        data: {
          userId: systemUser.id,
          sessionToken: '', // Will be updated with JWT
          expiresAt,
          device: 'web',
          ipAddress: null, // Will be set by controller
          userAgent: null, // Will be set by controller
        },
      });

      // Generate tokens
      const tokens = JWTService.generateTokenPair(
        {
          userId: systemUser.id,
          email: systemUser.email,
          role: systemUser.role,
          isSystemUser: true,
        },
        session.id
      );

      // Update session with access token
      await prisma.systemUserSession.update({
        where: { id: session.id },
        data: { sessionToken: tokens.accessToken },
      });

      // Store session data in Redis
      await SessionManager.setSession(session.id, {
        userId: systemUser.id,
        email: systemUser.email,
        role: systemUser.role,
        isSystemUser: true,
      });

      // Update last login
      await prisma.systemUser.update({
        where: { id: systemUser.id },
        data: { lastLoginAt: new Date() },
      });

      return {
        success: true,
        tokens,
        user: {
          id: systemUser.id,
          email: systemUser.email,
          name: systemUser.name,
          role: systemUser.role,
          isSystemUser: true,
        },
      };
    } catch (error) {
      console.error('System login failed:', error);
      return {
        success: false,
        error: 'Login failed',
      };
    }
  }

  /**
   * Logout user and invalidate session
   */
  static async logout(
    sessionId: string,
    isSystemUser: boolean = false
  ): Promise<boolean> {
    try {
      // Delete session from database
      if (isSystemUser) {
        await prisma.systemUserSession.delete({
          where: { id: sessionId },
        });
      } else {
        await prisma.userSession.delete({
          where: { id: sessionId },
        });
      }

      // Delete session from Redis
      await SessionManager.deleteSession(sessionId);

      return true;
    } catch (error) {
      console.error('Logout failed:', error);
      return false;
    }
  }

  /**
   * Refresh access token using refresh token
   */
  static async refreshToken(refreshToken: string): Promise<LoginResult> {
    try {
      // Verify refresh token
      const refreshPayload = JWTService.verifyRefreshToken(refreshToken);
      if (!refreshPayload) {
        return {
          success: false,
          error: 'Invalid refresh token',
        };
      }

      // Get session from Redis
      const sessionData = (await SessionManager.getSession(
        refreshPayload.sessionId
      )) as {
        userId: string;
        email: string;
        role: string;
        tenantId?: string;
        isSystemUser: boolean;
      } | null;

      if (!sessionData) {
        return {
          success: false,
          error: 'Session not found',
        };
      }

      // Verify session in database
      let session: { id: string; expiresAt: Date } | null = null;

      if (sessionData.isSystemUser) {
        session = await prisma.systemUserSession.findUnique({
          where: { id: refreshPayload.sessionId },
        });
      } else {
        session = await prisma.userSession.findUnique({
          where: { id: refreshPayload.sessionId },
        });
      }

      if (!session || session.expiresAt < new Date()) {
        return {
          success: false,
          error: 'Session expired',
        };
      }

      // Generate new tokens
      const tokens = JWTService.generateTokenPair(
        {
          userId: sessionData.userId,
          email: sessionData.email,
          role: sessionData.role,
          tenantId: sessionData.tenantId,
          isSystemUser: sessionData.isSystemUser,
        },
        refreshPayload.sessionId
      );

      // Update session with new access token
      if (sessionData.isSystemUser) {
        await prisma.systemUserSession.update({
          where: { id: refreshPayload.sessionId },
          data: {
            sessionToken: tokens.accessToken,
            lastActiveAt: new Date(),
          },
        });
      } else {
        await prisma.userSession.update({
          where: { id: refreshPayload.sessionId },
          data: {
            sessionToken: tokens.accessToken,
            lastActiveAt: new Date(),
          },
        });
      }

      return {
        success: true,
        tokens,
        user: {
          id: sessionData.userId,
          email: sessionData.email,
          role: sessionData.role,
          tenantId: sessionData.tenantId,
          isSystemUser: sessionData.isSystemUser,
        },
      };
    } catch (error) {
      console.error('Token refresh failed:', error);
      return {
        success: false,
        error: 'Token refresh failed',
      };
    }
  }

  /**
   * Create new tenant user
   */
  static async createUser(userData: CreateUserData): Promise<User> {
    const { email, password, name, tenantId, role = 'USER' } = userData;

    // Generate password if not provided
    const finalPassword =
      password || PasswordService.generateTemporaryPassword();

    // Hash password
    const passwordHash = await PasswordService.hashPassword(finalPassword);

    // Create user
    const user = await prisma.user.create({
      data: {
        email,
        passwordHash,
        name,
        tenantId,
        role,
        emailVerified: false,
      },
    });

    return user;
  }

  /**
   * Create new system user
   */
  static async createSystemUser(
    userData: CreateSystemUserData
  ): Promise<SystemUser> {
    const { email, password, name, role } = userData;

    // Validate password
    const passwordValidation =
      PasswordService.validatePasswordStrength(password);
    if (!passwordValidation.isValid) {
      throw new Error(
        `Password validation failed: ${passwordValidation.errors.join(', ')}`
      );
    }

    // Hash password
    const passwordHash = await PasswordService.hashPassword(password);

    // Create system user
    const systemUser = await prisma.systemUser.create({
      data: {
        email,
        passwordHash,
        name,
        role,
        emailVerified: false,
        isActive: true,
      },
    });

    return systemUser;
  }

  /**
   * Change user password
   */
  static async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string,
    isSystemUser: boolean = false
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Validate new password
      const passwordValidation =
        PasswordService.validatePasswordStrength(newPassword);
      if (!passwordValidation.isValid) {
        return {
          success: false,
          error: `Password validation failed: ${passwordValidation.errors.join(', ')}`,
        };
      }

      // Get user
      let user: { id: string; passwordHash: string } | null = null;

      if (isSystemUser) {
        const systemUser = await prisma.systemUser.findUnique({
          where: { id: userId },
          select: { id: true, passwordHash: true },
        });
        user = systemUser;
      } else {
        const regularUser = await prisma.user.findUnique({
          where: { id: userId },
          select: { id: true, passwordHash: true },
        });
        user =
          regularUser && regularUser.passwordHash
            ? { id: regularUser.id, passwordHash: regularUser.passwordHash }
            : null;
      }

      if (!user) {
        return {
          success: false,
          error: 'User not found',
        };
      }

      // Verify current password
      const isCurrentPasswordValid = await PasswordService.verifyPassword(
        currentPassword,
        user.passwordHash
      );

      if (!isCurrentPasswordValid) {
        return {
          success: false,
          error: 'Current password is incorrect',
        };
      }

      // Hash new password
      const newPasswordHash = await PasswordService.hashPassword(newPassword);

      // Update user password
      if (isSystemUser) {
        await prisma.systemUser.update({
          where: { id: userId },
          data: { passwordHash: newPasswordHash },
        });
      } else {
        await prisma.user.update({
          where: { id: userId },
          data: { passwordHash: newPasswordHash },
        });
      }

      // Invalidate all sessions for this user
      await SessionManager.deleteAllUserSessions(userId);

      return { success: true };
    } catch (error) {
      console.error('Password change failed:', error);
      return {
        success: false,
        error: 'Password change failed',
      };
    }
  }
}
