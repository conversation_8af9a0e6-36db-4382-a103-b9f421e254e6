import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import { getEnv } from '@underwriting/config';
import { ApiResponse } from '@underwriting/shared';
import { apiRoutes } from './routes';
import { initializeContainer, shutdownContainer, ServiceRegistry } from './core/container-setup';
import { globalErrorHandler, notFoundHandler } from './services/error-handling';
import { createScopeMiddleware } from './core/di-container';
import { logger } from './utils/logger';
// import { initializePaymentService } from './billing/controllers/paymentController';
// import { initializeInvoiceService } from './billing/controllers/invoiceController';

const env = getEnv();
const app = express();
const PORT = env.BACKEND_PORT;

// Middleware
app.use(helmet());
app.use(
  cors({
    origin: env.CORS_ORIGINS.split(','),
    credentials: true,
  })
);
app.use(compression());
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Request-scoped dependency injection
app.use(createScopeMiddleware());

// Health check endpoint with service container status
app.get('/health', (_req, res) => {
  const containerHealth = ServiceRegistry.getHealthStatus();
  
  const response: ApiResponse = {
    success: true,
    data: {
      status: containerHealth.dependenciesValid ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: env.NODE_ENV,
      container: {
        initialized: containerHealth.initialized,
        servicesRegistered: containerHealth.servicesRegistered,
        dependenciesValid: containerHealth.dependenciesValid,
      },
    },
  };
  res.status(200).json(response);
});

// API routes
app.use('/api', apiRoutes);

// 404 handler
app.use(notFoundHandler);

// Global error handling middleware
app.use(globalErrorHandler);

// Enterprise Application Startup Sequence
async function startEnterpriseServer(): Promise<void> {
  try {
    logger.info('🚀 Initiating Enterprise Server Startup Sequence...');
    
    // Phase 1: Initialize Dependency Injection Container
    await initializeContainer();
    logger.info('✨ Service Container initialized successfully');
    
    // Phase 2: Start HTTP Server
    const server = app.listen(PORT, () => {
      logger.info(`🚀 Enterprise Server running on port ${PORT}`);
      logger.info(`📊 Health check: http://localhost:${PORT}/health`);
      logger.info(`🔌 API endpoint: http://localhost:${PORT}/api`);
      logger.info('⚡ Enterprise-level architecture fully operational!');
    });

    // Phase 3: Graceful Shutdown Handlers
    const gracefulShutdown = async (signal: string): Promise<void> => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);
      
      server.close(() => {
        logger.info('HTTP server closed');
      });

      try {
        await shutdownContainer();
        logger.info('Service Container shut down successfully');
        process.exit(0);
      } catch (error) {
        logger.error('Error during shutdown:', { error });
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    
  } catch (error) {
    logger.error('Failed to start Enterprise Server:', { error });
    process.exit(1);
  }
}

// Initialize the Enterprise Server
startEnterpriseServer();
