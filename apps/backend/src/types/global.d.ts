/**
 * Global Type Declarations for Test Environment
 * Provides type safety for global test utilities and mocks
 */

/* eslint-disable no-undef, @typescript-eslint/no-explicit-any */
// ESLint disabled for Jest global types and test mocking utilities

import type { PrismaClient } from '@prisma/client';

declare global {
  namespace NodeJS {
    interface Global {
      testUtils: TestUtilities;
    }
  }

  // Make testUtils available on global scope
  var testUtils: TestUtilities;
}

export interface TestUtilities {
  // Mock utilities for Express
  createMockRequest: (overrides?: Record<string, unknown>) => Record<string, unknown>;
  createMockResponse: () => Record<string, unknown>;
  createMockNext: () => jest.MockedFunction<(error?: Error) => void>;
  
  // Utility functions
  wait: (ms: number) => Promise<void>;
  generateTestId: (prefix?: string) => string;
  
  // Test data
  mockSystemUser: Record<string, unknown>;
  mockTenant: Record<string, unknown>;
  
  // Database utilities
  prisma?: PrismaClient;
  
  // Test data factories
  createTestTenant?: (overrides?: Partial<{
    name: string;
    slug: string; 
    status: string;
    subscriptionTier: string;
  }>) => {
    id: string;
    name: string;
    slug: string;
    status: string;
    subscriptionTier: string;
    createdAt: Date;
    updatedAt: Date;
    domain: string | null;
    branding: Record<string, unknown>;
    customDomain: string | null;
    settings: Record<string, unknown>;
  };
  
  createTestUser?: (tenantId: string, overrides?: Partial<{
    email: string;
    role: string;
    name: string;
  }>) => {
    id: string;
    tenantId: string;
    email: string;
    role: string;
    name: string | null;
    emailVerified: boolean;
    emailVerifiedAt: Date | null;
    passwordHash: string | null;
    lastLoginAt: Date | null;
    createdAt: Date;
    updatedAt: Date;
    permissions: Record<string, unknown>;
    preferences: Record<string, unknown>;
    metadata: Record<string, unknown>;
  };
  
  createTestApplication?: (tenantId: string, overrides?: Partial<{
    businessName: string;
    businessType: string;
    industry: string;
    requestedAmount: number;
    status: string;
  }>) => {
    id: string;
    tenantId: string;
    applicationNumber: string;
    businessName: string;
    businessType: string;
    industry: string;
    businessEmail: string;
    businessAddress: Record<string, unknown>;
    requestedAmount: number;
    status: string;
    createdAt: Date;
    updatedAt: Date;
    completedAt: Date | null;
    metadata: Record<string, unknown>;
    riskLevel: string | null;
  };
  
  // Utility functions
  generateUniqueId?: () => string;
  
  // Mock utilities
  mockPrisma: MockPrismaClient;
}

// Enhanced mock types for better type safety
export interface MockPrismaClient {
  tenant: {
    findUnique: jest.MockedFunction<any>;
    findMany: jest.MockedFunction<any>;
    create: jest.MockedFunction<any>;
    update: jest.MockedFunction<any>;
    delete: jest.MockedFunction<any>;
    deleteMany: jest.MockedFunction<any>;
  };
  user: {
    findUnique: jest.MockedFunction<any>;
    findMany: jest.MockedFunction<any>;
    create: jest.MockedFunction<any>;
    update: jest.MockedFunction<any>;
    delete: jest.MockedFunction<any>;
    deleteMany: jest.MockedFunction<any>;
  };
  application: {
    findUnique: jest.MockedFunction<any>;
    findMany: jest.MockedFunction<any>;
    create: jest.MockedFunction<any>;
    update: jest.MockedFunction<any>;
    delete: jest.MockedFunction<any>;
    deleteMany: jest.MockedFunction<any>;
  };
  subscription: {
    findUnique: jest.MockedFunction<any>;
    findMany: jest.MockedFunction<any>;
    create: jest.MockedFunction<any>;
    update: jest.MockedFunction<any>;
    delete: jest.MockedFunction<any>;
    deleteMany: jest.MockedFunction<any>;
  };
  communicationTemplate: {
    findUnique: jest.MockedFunction<any>;
    findMany: jest.MockedFunction<any>;
    create: jest.MockedFunction<any>;
    update: jest.MockedFunction<any>;
    delete: jest.MockedFunction<any>;
    deleteMany: jest.MockedFunction<any>;
  };
  communicationLog: {
    findUnique: jest.MockedFunction<any>;
    findMany: jest.MockedFunction<any>;
    create: jest.MockedFunction<any>;
    update: jest.MockedFunction<any>;
    delete: jest.MockedFunction<any>;
    deleteMany: jest.MockedFunction<any>;
  };
  bulkCommunicationJob: {
    findUnique: jest.MockedFunction<any>;
    findMany: jest.MockedFunction<any>;
    create: jest.MockedFunction<any>;
    update: jest.MockedFunction<any>;
    delete: jest.MockedFunction<any>;
    deleteMany: jest.MockedFunction<any>;
  };
  $disconnect: jest.MockedFunction<() => Promise<void>>;
}

export {};