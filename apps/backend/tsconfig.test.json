{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    // More lenient settings for test files
    "noImplicitAny": false,
    "strictNullChecks": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noUncheckedIndexedAccess": false,
    "skipLibCheck": true
  },
  "include": [
    "src/**/__tests__/**/*",
    "src/**/*.test.ts", 
    "src/**/*.spec.ts",
    "src/types/global.d.ts"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
}