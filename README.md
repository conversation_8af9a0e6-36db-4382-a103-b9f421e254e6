# Merchant Underwriting Platform

A modern, scalable platform for automated merchant underwriting and risk assessment. Built with
TypeScript, Next.js, and Express in a monorepo architecture.

> **BMad Development Status:** This project is developed using BMad (autonomous AI development agents) with quality gates, architectural validation, and continuous integration. BMad maintains 85+ quality scores and ensures TypeScript strict mode compliance.

## Features

- 🚀 **Modern Stack** - Next.js 15, React 19, TypeScript 5.8
- 📊 **Real-time Analytics** - Interactive dashboards and reporting
- 🔐 **Secure Authentication** - JWT-based auth with role management
- 🗄️ **Database Management** - PostgreSQL with Prisma ORM
- 📱 **Responsive Design** - Mobile-first UI with Tailwind CSS
- 🔌 **API Integration** - RESTful APIs with comprehensive documentation
- 🐳 **Docker Support** - Containerized development environment
- 🧪 **Testing Ready** - Jest testing framework configured
- 📈 **Scalable Architecture** - Monorepo with Turborepo optimization

## Quick Start

### Prerequisites

- Node.js 20+ with npm 10+
- Docker Desktop
- Git

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd OLA
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Setup environment**

   ```bash
   npm run setup:env
   ```

   Edit the created `.env` file with your configuration values.

4. **Start services**

   ```bash
   npm run setup:all
   ```

5. **Start development servers**
   ```bash
   npm run dev
   ```

## Development

### Available Scripts

| Command             | Description                   |
| ------------------- | ----------------------------- |
| `npm run dev`       | Start all development servers |
| `npm run build`     | Build entire monorepo         |
| `npm run test`      | Run all tests                 |
| `npm run lint`      | Lint all code                 |
| `npm run typecheck` | TypeScript type checking      |
| `npm run clean`     | Clean build artifacts         |

### Database Commands

| Command               | Description                    |
| --------------------- | ------------------------------ |
| `npm run setup:db`    | Setup database with migrations |
| `npm run db:migrate`  | Run database migrations        |
| `npm run db:generate` | Generate Prisma client         |
| `npm run db:studio`   | Open Prisma Studio             |
| `npm run db:reset`    | Reset database                 |

### Docker Commands

| Command                | Description                  |
| ---------------------- | ---------------------------- |
| `npm run docker:up`    | Start all services           |
| `npm run docker:down`  | Stop all services            |
| `npm run docker:logs`  | View service logs            |
| `npm run docker:clean` | Clean containers and volumes |

## Architecture

### Monorepo Structure

```
├── apps/
│   ├── frontend/          # Next.js application
│   └── backend/           # Express API server
├── packages/
│   ├── shared/           # Shared utilities and types
│   ├── ui/               # Shared UI components
│   └── config/           # Shared configurations
├── docs/                 # Documentation
├── scripts/              # Build and utility scripts
└── docker-compose.yml    # Development services
```

### Technology Stack

**Frontend**

- Next.js 15 with App Router
- React 19 + TypeScript
- Tailwind CSS + shadcn/ui components
- Framer Motion for animations
- React Hook Form + Zod validation

**Backend**

- Express 4.21 + TypeScript
- Prisma ORM with PostgreSQL
- JWT authentication
- Redis for caching
- Winston logging

**Development Tools**

- Turborepo for monorepo management
- ESLint + Prettier for code quality
- Husky for git hooks
- Jest for testing
- Docker for containerization

## Services

### Development URLs

| Service       | URL                   | Description         |
| ------------- | --------------------- | ------------------- |
| Frontend      | http://localhost:3000 | Next.js application |
| Backend API   | http://localhost:5000 | Express server      |
| Prisma Studio | http://localhost:5555 | Database management |
| PostgreSQL    | localhost:5433        | Database server     |
| Redis         | localhost:6380        | Cache server        |

### Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# Database
DATABASE_URL="postgresql://postgres:password@localhost:5433/ai_underwriting"
REDIS_URL="redis://localhost:6380"

# Authentication
JWT_SECRET="your-jwt-secret"
SESSION_SECRET="your-session-secret"

# Ports
FRONTEND_PORT=3000
BACKEND_PORT=5000
POSTGRES_PORT=5433
REDIS_PORT=6380
```

## Development Workflow

### Adding New Features

1. **Create feature branch**

   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Develop with hot reload**

   ```bash
   npm run dev
   ```

3. **Run quality checks**

   ```bash
   npm run lint
   npm run typecheck
   npm run test
   ```

4. **Build and verify**
   ```bash
   npm run build
   ```

### Code Quality

- TypeScript strict mode enforced
- ESLint configuration with modern rules
- Prettier for consistent formatting
- Pre-commit hooks for quality assurance
- Comprehensive test coverage expected

### Database Changes

1. **Modify schema**

   ```bash
   # Edit apps/backend/prisma/schema.prisma
   ```

2. **Create migration**

   ```bash
   npm run db:migrate
   ```

3. **Generate client**
   ```bash
   npm run db:generate
   ```

## API Documentation

The backend API provides RESTful endpoints for:

- User authentication and management
- Merchant data processing
- Risk assessment workflows
- Reporting and analytics

API documentation is available at `http://localhost:5000/docs` when running in development mode.

## Testing

### Running Tests

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test -- --watch

# Run tests for specific workspace
npm run test:frontend
npm run test:backend
```

### Test Structure

- Unit tests for utilities and services
- Integration tests for API endpoints
- Component tests for React components
- End-to-end tests for critical workflows

## Deployment

### Production Build

```bash
# Build all applications
npm run build

# Start production servers
npm run start
```

### Docker Production

```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Start production stack
docker-compose -f docker-compose.prod.yml up -d
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Commit Guidelines

- Use conventional commit format
- Include tests for new features
- Update documentation as needed
- Ensure all quality checks pass

## License

This project is proprietary software owned by Base LLC.

## Support

For questions and support, please contact the development team or refer to the documentation in the
`docs/` directory.
