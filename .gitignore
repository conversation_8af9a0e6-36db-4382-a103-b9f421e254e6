# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out/

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# Docker
*.log
docker-compose.override.yml

# Database
*.db
*.sqlite
*.sqlite3

# Prisma
prisma/dev.db
prisma/dev.db-journal

# Redis dumps
dump.rdb

# Logs
logs
*.log

# Temporary files
tmp/
temp/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build outputs
dist/
build/
out/

# Testing
coverage/
.coverage
.pytest_cache/
.tox/

# Python (if any Python scripts)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Java (if any Java components)
*.class
*.jar
*.war
*.ear
target/

# Backup files
*.bak
*.backup
*.orig

# Certificate files
*.pem
*.key
*.crt
*.cert

# AI and ML models
*.model
*.pkl
*.joblib
models/
checkpoints/

# Large files that shouldn't be committed
*.zip
*.tar.gz
*.rar
*.7z

# Documentation build
docs/_build/
docs/site/

# Monitoring and analytics
.nyc_output/

# Mobile specific
*.ipa
*.apk

# Platform specific
Thumbs.db
ehthumbs.db

# Package managers
package-lock.json.backup
yarn.lock.backup

# Terraform (if used for infrastructure)
*.tfstate
*.tfstate.*
.terraform/

# Kubernetes (if used for deployment)
*.kubeconfig

# Local development
.local/
.sandbox/

# Turborepo cache
.turbo/

# BMad validation reports
.bmad-validation-report.json
.bmad-healing-report.json

# AI
.bmad-core/
.bmad-infrastructure-devops/
.claude/
.gemini/
.naptha/
.openapi/
.roomodes
