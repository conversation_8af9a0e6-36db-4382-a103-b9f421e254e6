{"name": "ai-underwriting-platform", "version": "1.0.0", "private": true, "description": "AI-Powered Merchant Underwriting Platform - Monorepo", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build --concurrency=1", "start": "turbo run start", "test": "turbo run test --concurrency=1", "lint": "turbo run lint --concurrency=1", "lint:fix": "turbo run lint:fix --concurrency=1", "typecheck": "turbo run typecheck --concurrency=1", "clean": "turbo run clean && rm -rf node_modules", "setup:env": "cp .env.example .env && echo '✅ Environment file created. Please update .env with your values.'", "setup:db": "docker-compose up -d postgres redis && cd apps/backend && npx prisma migrate dev", "setup:ai": "docker-compose up -d ollama && sleep 10 && docker exec ${OLLAMA_CONTAINER_NAME:-ollama-dev} ollama pull ${OLLAMA_MODEL:-llama3.1:8b}", "setup:all": "npm run setup:env && npm run setup:db && npm run setup:ai", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:clean": "docker-compose down -v && docker system prune -f", "db:migrate": "cd apps/backend && npx prisma migrate dev", "db:generate": "cd apps/backend && npx prisma generate", "db:studio": "cd apps/backend && npx prisma studio --port ${PRISMA_STUDIO_PORT:-5555}", "db:reset": "cd apps/backend && npx prisma migrate reset", "db:seed": "cd apps/backend && npx prisma db seed", "test:db:sync": "./scripts/sync-test-db.sh", "test:db:reset": "cd apps/backend && DATABASE_URL=\"postgresql://postgres:password@localhost:5433/underwriting_test\" npx prisma db push --accept-data-loss --force-reset", "test:db:setup": "docker exec postgres-dev psql -U postgres -c \"DROP DATABASE IF EXISTS underwriting_test; CREATE DATABASE underwriting_test;\" && npm run test:db:sync", "ai:pull": "docker exec ${OLLAMA_CONTAINER_NAME:-ollama-dev} ollama pull ${OLLAMA_MODEL:-llama3.1:8b}", "ai:list": "docker exec ${OLLAMA_CONTAINER_NAME:-ollama-dev} ollama list", "prepare": "husky", "pre-commit": "lint-staged", "ci:validate": "npm run typecheck && npm run lint && npm run build", "bmad:validate": "node scripts/bmad-validate.js", "bmad:self-heal": "node scripts/bmad-self-heal.js", "bmad:quality-score": "node -e \"const report = require('./.bmad-validation-report.json'); console.log('BMad Quality Score:', report.qualityScore + '/100');\"", "dev:pre-epic": "node scripts/dev-workflow.js pre-epic", "dev:validate-file": "node scripts/dev-workflow.js validate-file", "dev:micro-validate": "node scripts/dev-workflow.js micro-validate", "dev:task-complete": "node scripts/dev-workflow.js task-complete", "dev:auto-fix": "node scripts/dev-workflow.js auto-fix", "dev:watch": "node scripts/watch-validation.js"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "chokidar": "^4.0.3", "concurrently": "^9.2.0", "eslint": "^9.31.0", "husky": "^9.1.7", "lint-staged": "^15.2.10", "lodash.debounce": "^4.0.8", "prettier": "^3.6.2", "turbo": "^2.5.5", "typescript": "^5.8.3"}, "packageManager": "npm@10.9.2", "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/base-do/ai-underwriting-platform.git"}, "keywords": ["ai", "underwriting", "fintech", "openrouter", "llama", "typescript", "monorepo"], "author": "Base LLC", "license": "Proprietary", "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}}