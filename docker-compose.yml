services:
  postgres:
    image: postgres:17.2
    container_name: ${POSTGRES_CONTAINER_NAME:-postgres-dev}
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "${POSTGRES_PORT:-5433}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - underwriting-network

  redis:
    image: redis:7-alpine
    container_name: ${REDIS_CONTAINER_NAME:-redis-dev}
    ports:
      - "${REDIS_PORT:-6380}:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - underwriting-network

  ollama:
    image: ollama/ollama:latest
    container_name: ${OLLAMA_CONTAINER_NAME:-ollama-dev}
    ports:
      - "${OLLAMA_PORT:-11434}:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_MODELS=${OLLAMA_MODEL:-llama3.1:8b}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - underwriting-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  ollama_data:
    driver: local

networks:
  underwriting-network:
    driver: bridge
