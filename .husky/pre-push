echo "🚀 BMad Pre-Push Quality Gate"
echo "==============================="

# 1. Lint check
echo "📝 Running lint check..."
npm run lint
if [ $? -ne 0 ]; then
  echo "❌ Lint check failed! Fix errors before pushing."
  exit 1
fi
echo "✅ Lint check passed"

# 2. Type check
echo "🔍 Running type check..."
npm run typecheck
if [ $? -ne 0 ]; then
  echo "❌ Type check failed! Fix type errors before pushing."
  exit 1
fi
echo "✅ Type check passed"

# 3. Run tests
echo "🧪 Running tests..."
npm run test
if [ $? -ne 0 ]; then
  echo "❌ Tests failed! Fix failing tests before pushing."
  exit 1
fi
echo "✅ Tests passed"

# 4. Build check
echo "🏗️  Running build check..."
npm run build
if [ $? -ne 0 ]; then
  echo "❌ Build failed! Fix build errors before pushing."
  exit 1
fi
echo "✅ Build passed"

# 5. BMad validation (if available)
if [ -f "scripts/bmad-validate.js" ]; then
  echo "🤖 Running BMad validation..."
  npm run bmad:validate
  if [ $? -ne 0 ]; then
    echo "❌ BMad validation failed!"
    echo "🔧 Attempting BMad self-heal..."
    npm run bmad:self-heal
    if [ $? -eq 0 ]; then
      echo "✅ BMad self-heal successful! Re-running validation..."
      npm run bmad:validate
      if [ $? -ne 0 ]; then
        echo "❌ BMad validation still failing after self-heal. Manual intervention required."
        exit 1
      fi
      echo "✅ BMad validation passed after self-heal"
    else
      echo "❌ BMad self-heal failed. Manual intervention required."
      exit 1
    fi
  else
    echo "✅ BMad validation passed"
  fi
fi

echo "🎉 All quality gates passed! Push approved."
echo "==============================="