#!/bin/bash

# Strict Pre-Commit Quality Gate
# Prevents commits that would fail in CI

set -e

echo "🛡️  STRICT QUALITY GATE - Pre-Commit Validation"
echo "=============================================="

# 1. Clean environment - regenerate all types
echo "🔄 Regenerating Prisma client..."
cd apps/backend && npm run db:generate

# 2. Clear all caches
echo "🧹 Clearing TypeScript and Turbo caches..."
cd ../..
npx turbo clean
rm -rf node_modules/.cache
rm -rf apps/backend/node_modules/.cache

# 3. TypeScript validation with zero tolerance
echo "🔍 TypeScript validation (zero errors)..."
npm run typecheck || {
    echo "❌ TypeScript errors detected - commit blocked"
    exit 1
}

# 4. ESLint validation with error threshold
echo "🔍 ESLint validation..."
LINT_OUTPUT=$(npm run lint 2>&1) || {
    # Count actual errors (not warnings)
    ERROR_COUNT=$(echo "$LINT_OUTPUT" | grep -c "error" || echo "0")
    if [ "$ERROR_COUNT" -gt 0 ]; then
        echo "❌ ESLint errors detected: $ERROR_COUNT - commit blocked"
        echo "$LINT_OUTPUT"
        exit 1
    fi
    
    # Count warnings and set threshold
    WARNING_COUNT=$(echo "$LINT_OUTPUT" | grep -c "warning" || echo "0")
    if [ "$WARNING_COUNT" -gt 50 ]; then
        echo "⚠️  Too many ESLint warnings: $WARNING_COUNT (max: 50) - commit blocked"
        echo "$LINT_OUTPUT"
        exit 1
    fi
}

# 5. Build validation
echo "🔨 Build validation..."
npm run build || {
    echo "❌ Build failed - commit blocked"
    exit 1
}

# 6. BMad quality validation
echo "🎯 BMad quality validation..."
QUALITY_SCORE=$(npm run bmad:quality-score 2>/dev/null | grep -o '[0-9]\+' | tail -1)
if [ "$QUALITY_SCORE" -lt 85 ]; then
    echo "❌ BMad quality score too low: $QUALITY_SCORE/100 (min: 85) - commit blocked"
    exit 1
fi

echo "✅ All quality gates passed - commit allowed"
echo "📊 Quality Score: $QUALITY_SCORE/100"