{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "useTabs": false, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "endOfLine": "lf", "quoteProps": "as-needed", "jsxSingleQuote": false, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "embeddedLanguageFormatting": "auto", "overrides": [{"files": "*.md", "options": {"printWidth": 100, "proseWrap": "always"}}, {"files": "*.{yml,yaml}", "options": {"tabWidth": 2, "useTabs": false}}]}