#!/usr/bin/env node

/**
 * Automated Quality Monitor
 * Continuously monitors code quality and prevents degradation
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class QualityMonitor {
  constructor() {
    this.qualityThresholds = {
      bmadScore: 85,
      eslintErrors: 0,
      eslintWarnings: 50,
      typeScriptErrors: 0,
      testCoverage: 80
    };
    
    this.reportPath = './.quality-report.json';
  }

  async runQualityCheck() {
    console.log('🔍 Running comprehensive quality check...\n');
    
    const report = {
      timestamp: new Date().toISOString(),
      passed: true,
      issues: [],
      metrics: {}
    };

    try {
      // 1. TypeScript Check
      await this.checkTypeScript(report);
      
      // 2. ESLint Check
      await this.checkESLint(report);
      
      // 3. BMad Quality Score
      await this.checkBMadScore(report);
      
      // 4. Build Check
      await this.checkBuild(report);
      
      // 5. Generate Report
      this.generateReport(report);
      
      if (report.passed) {
        console.log('✅ All quality checks passed!');
        process.exit(0);
      } else {
        console.log('❌ Quality checks failed!');
        console.log('Issues found:', report.issues.length);
        report.issues.forEach(issue => console.log(`  - ${issue}`));
        process.exit(1);
      }
      
    } catch (error) {
      console.error('❌ Quality monitoring failed:', error.message);
      process.exit(1);
    }
  }

  async checkTypeScript(report) {
    try {
      console.log('🔍 Checking TypeScript...');
      execSync('npm run typecheck', { stdio: 'pipe' });
      report.metrics.typeScriptErrors = 0;
      console.log('✅ TypeScript: No errors');
    } catch (error) {
      const errorCount = (error.stdout?.toString() || '').split('error TS').length - 1;
      report.metrics.typeScriptErrors = errorCount;
      
      if (errorCount > this.qualityThresholds.typeScriptErrors) {
        report.passed = false;
        report.issues.push(`TypeScript errors: ${errorCount} (max: ${this.qualityThresholds.typeScriptErrors})`);
      }
      console.log(`❌ TypeScript: ${errorCount} errors detected`);
    }
  }

  async checkESLint(report) {
    try {
      console.log('🔍 Checking ESLint...');
      const output = execSync('npm run lint', { stdio: 'pipe' }).toString();
      
      const errorMatches = output.match(/(\d+)\s+problems?\s*\(\s*(\d+)\s+errors?\s*,\s*(\d+)\s+warnings?\s*\)/);
      const errors = errorMatches ? parseInt(errorMatches[2]) : 0;
      const warnings = errorMatches ? parseInt(errorMatches[3]) : 0;
      
      report.metrics.eslintErrors = errors;
      report.metrics.eslintWarnings = warnings;
      
      if (errors > this.qualityThresholds.eslintErrors) {
        report.passed = false;
        report.issues.push(`ESLint errors: ${errors} (max: ${this.qualityThresholds.eslintErrors})`);
      }
      
      if (warnings > this.qualityThresholds.eslintWarnings) {
        report.passed = false;
        report.issues.push(`ESLint warnings: ${warnings} (max: ${this.qualityThresholds.eslintWarnings})`);
      }
      
      console.log(`✅ ESLint: ${errors} errors, ${warnings} warnings`);
    } catch (error) {
      // ESLint returns non-zero exit code for warnings/errors
      const output = error.stdout?.toString() || '';
      const errorMatches = output.match(/(\d+)\s+problems?\s*\(\s*(\d+)\s+errors?\s*,\s*(\d+)\s+warnings?\s*\)/);
      
      if (errorMatches) {
        const errors = parseInt(errorMatches[2]);
        const warnings = parseInt(errorMatches[3]);
        
        report.metrics.eslintErrors = errors;
        report.metrics.eslintWarnings = warnings;
        
        if (errors > this.qualityThresholds.eslintErrors) {
          report.passed = false;
          report.issues.push(`ESLint errors: ${errors} (max: ${this.qualityThresholds.eslintErrors})`);
        }
        
        if (warnings > this.qualityThresholds.eslintWarnings) {
          report.passed = false;
          report.issues.push(`ESLint warnings: ${warnings} (max: ${this.qualityThresholds.eslintWarnings})`);
        }
        
        console.log(`⚠️  ESLint: ${errors} errors, ${warnings} warnings`);
      }
    }
  }

  async checkBMadScore(report) {
    try {
      console.log('🔍 Checking BMad Quality Score...');
      const output = execSync('npm run bmad:quality-score', { stdio: 'pipe' }).toString();
      const scoreMatch = output.match(/(\d+)\/100/);
      const score = scoreMatch ? parseInt(scoreMatch[1]) : 0;
      
      report.metrics.bmadScore = score;
      
      if (score < this.qualityThresholds.bmadScore) {
        report.passed = false;
        report.issues.push(`BMad score too low: ${score}/100 (min: ${this.qualityThresholds.bmadScore})`);
      }
      
      console.log(`✅ BMad Quality Score: ${score}/100`);
    } catch (error) {
      report.passed = false;
      report.issues.push('Failed to get BMad quality score');
      console.log('❌ BMad Quality Score: Failed to retrieve');
    }
  }

  async checkBuild(report) {
    try {
      console.log('🔍 Checking build...');
      execSync('npm run build', { stdio: 'pipe' });
      report.metrics.buildPassed = true;
      console.log('✅ Build: Successful');
    } catch (error) {
      report.metrics.buildPassed = false;
      report.passed = false;
      report.issues.push('Build failed');
      console.log('❌ Build: Failed');
    }
  }

  generateReport(report) {
    fs.writeFileSync(this.reportPath, JSON.stringify(report, null, 2));
    console.log(`\n📊 Quality report saved to ${this.reportPath}`);
  }
}

// Run if called directly
if (require.main === module) {
  const monitor = new QualityMonitor();
  monitor.runQualityCheck().catch(console.error);
}

module.exports = QualityMonitor;