#!/bin/bash
# Test Database Synchronization Script
# Keeps test database in sync with development schema

set -e

echo "🔄 Syncing test database with development schema..."

# Load environment variables
source .env

# Navigate to backend directory
cd apps/backend

# Option 1: Generate and apply new migration (for new features)
echo "📝 Generating migration for schema changes..."
npx prisma migrate dev --name "sync_$(date +%Y%m%d_%H%M%S)" --create-only

# Apply migrations to test database
echo "🎯 Applying migrations to test database..."
DATABASE_URL="postgresql://postgres:password@localhost:5433/underwriting_test" npx prisma migrate deploy

# Option 2: Force sync (use with caution - data loss possible)
if [ "$1" == "--force-sync" ]; then
    echo "⚠️  FORCE SYNC: This will reset test database!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        DATABASE_URL="postgresql://postgres:password@localhost:5433/underwriting_test" npx prisma db push --accept-data-loss
    fi
fi

# Generate Prisma client
echo "🔧 Regenerating Prisma client..."
npx prisma generate

echo "✅ Test database sync complete!"
echo "📊 Verifying tables..."
docker exec postgres-dev psql -U postgres -d underwriting_test -c "\dt" | head -10

echo "🧪 Ready to run tests!"