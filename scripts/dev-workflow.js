#!/usr/bin/env node

/**
 * Enhanced Development Workflow Script
 * Provides quality-first development with integrated BMad validation
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class DevWorkflow {
  constructor() {
    this.qualityThreshold = 90;
    this.isVerbose = process.argv.includes('--verbose');
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      info: '🔍',
      success: '✅',
      warning: '⚠️',
      error: '❌',
    }[type];

    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  async executeCommand(command, description) {
    if (this.isVerbose) {
      this.log(`Executing: ${command}`, 'info');
    }

    try {
      const result = execSync(command, {
        encoding: 'utf8',
        stdio: this.isVerbose ? 'inherit' : 'pipe',
      });
      this.log(`${description} - PASSED`, 'success');
      return { success: true, output: result };
    } catch (error) {
      this.log(`${description} - FAILED`, 'error');
      if (this.isVerbose) {
        console.error(error.stdout || error.message);
      }
      return { success: false, error: error.message, output: error.stdout };
    }
  }

  /**
   * Pre-Epic Validation - Run before starting any epic implementation
   */
  async preEpicValidation() {
    this.log('🚀 Pre-Epic Validation Starting...', 'info');

    const checks = [
      {
        command: 'npx tsc --showConfig | grep \'"strict": true\'',
        description: 'TypeScript strict mode validation',
      },
      {
        command: 'npm run typecheck',
        description: 'TypeScript compilation check',
      },
      {
        command: 'npm run bmad:validate',
        description: 'BMad quality validation',
      },
      {
        command: 'git status --porcelain',
        description: 'Git working tree status',
        validator: (result) => {
          if (result.output.trim() !== '') {
            this.log('Working tree has uncommitted changes', 'warning');
            this.log(
              'Consider committing or stashing changes before starting epic',
              'warning'
            );
          }
          return true;
        },
      },
    ];

    let allPassed = true;
    for (const check of checks) {
      const result = await this.executeCommand(
        check.command,
        check.description
      );
      if (check.validator) {
        check.validator(result);
      }
      if (!result.success && check.required !== false) {
        allPassed = false;
      }
    }

    if (allPassed) {
      this.log('✅ Pre-Epic validation PASSED. Ready to implement!', 'success');
    } else {
      this.log(
        '❌ Pre-Epic validation FAILED. Fix issues before proceeding.',
        'error'
      );
      process.exit(1);
    }
  }

  /**
   * File-level validation - Run after creating/modifying each file
   */
  async validateFile(filePath) {
    if (!fs.existsSync(filePath)) {
      this.log(`File not found: ${filePath}`, 'error');
      return false;
    }

    this.log(`🔍 Validating file: ${filePath}`, 'info');

    const checks = [
      {
        command: `npx tsc --noEmit ${filePath}`,
        description: 'TypeScript compilation',
      },
      {
        command: `npx eslint ${filePath}`,
        description: 'ESLint validation',
      },
      {
        command: `npx prettier --check ${filePath}`,
        description: 'Prettier formatting',
      },
    ];

    let allPassed = true;
    for (const check of checks) {
      const result = await this.executeCommand(
        check.command,
        check.description
      );
      if (!result.success) {
        allPassed = false;
      }
    }

    return allPassed;
  }

  /**
   * Micro-validation - Run after each utility/service completion
   */
  async microValidation() {
    this.log('🔬 Running micro-validation...', 'info');

    const checks = [
      {
        command: 'npm run typecheck',
        description: 'TypeScript compilation',
      },
      {
        command: 'npm run lint',
        description: 'ESLint validation',
      },
      {
        command: 'npm run bmad:quality-score',
        description: 'BMad quality score check',
        validator: (result) => {
          const score = this.extractQualityScore(result.output);
          if (score < this.qualityThreshold) {
            this.log(
              `Quality score ${score} below threshold ${this.qualityThreshold}`,
              'error'
            );
            return false;
          }
          this.log(`Quality score: ${score}/100`, 'success');
          return true;
        },
      },
    ];

    let allPassed = true;
    for (const check of checks) {
      const result = await this.executeCommand(
        check.command,
        check.description
      );
      if (check.validator) {
        const validatorResult = check.validator(result);
        if (!validatorResult) allPassed = false;
      } else if (!result.success) {
        allPassed = false;
      }
    }

    return allPassed;
  }

  /**
   * Task completion workflow - Integrated quality gates and git operations
   */
  async taskCompletion(taskDescription, commitMessage) {
    this.log(`🎯 Task completion workflow for: ${taskDescription}`, 'info');

    // Step 1: Run comprehensive validation
    this.log('Step 1: Running quality validation...', 'info');
    const validationPassed = await this.microValidation();

    if (!validationPassed) {
      this.log(
        '❌ Quality validation failed. Fix issues before committing.',
        'error'
      );
      return false;
    }

    // Step 2: Check git status
    this.log('Step 2: Checking git status...', 'info');
    const statusResult = await this.executeCommand(
      'git status --porcelain',
      'Git status check'
    );

    if (statusResult.output.trim() === '') {
      this.log('No changes to commit', 'warning');
      return true;
    }

    // Step 3: Run final BMad validation
    this.log('Step 3: Final BMad validation...', 'info');
    const bmadResult = await this.executeCommand(
      'npm run bmad:validate',
      'BMad validation'
    );

    if (!bmadResult.success) {
      this.log(
        '❌ BMad validation failed. Cannot proceed with commit.',
        'error'
      );
      return false;
    }

    // Extract quality score
    const qualityScore = this.extractQualityScore(bmadResult.output);

    // Step 4: Git add and commit
    this.log('Step 4: Committing changes...', 'info');

    const addResult = await this.executeCommand('git add .', 'Git add');
    if (!addResult.success) {
      this.log('❌ Git add failed', 'error');
      return false;
    }

    // Create BMad-formatted commit message
    const bmadCommitMessage = this.createBMadCommitMessage(
      commitMessage,
      qualityScore
    );

    const commitResult = await this.executeCommand(
      `git commit -m "${bmadCommitMessage}"`,
      'Git commit'
    );

    if (!commitResult.success) {
      this.log('❌ Git commit failed', 'error');
      return false;
    }

    // Step 5: Final status check
    this.log('Step 5: Final validation...', 'info');
    const finalStatusResult = await this.executeCommand(
      'git status',
      'Final git status'
    );

    this.log('🎉 Task completion workflow SUCCESSFUL!', 'success');
    this.log(`Quality Score: ${qualityScore}/100`, 'success');
    this.log('Changes committed with BMad validation', 'success');

    return true;
  }

  /**
   * Auto-fix common issues
   */
  async autoFix() {
    this.log('🔧 Running auto-fix for common issues...', 'info');

    const fixes = [
      {
        command: 'npx prettier --write "src/**/*.{ts,js,json}"',
        description: 'Prettier formatting',
      },
      {
        command: 'npx eslint "src/**/*.ts" --fix',
        description: 'ESLint auto-fix',
      },
      {
        command: 'npm run bmad:self-heal',
        description: 'BMad self-healing',
      },
    ];

    for (const fix of fixes) {
      await this.executeCommand(fix.command, fix.description);
    }

    this.log('✅ Auto-fix completed', 'success');
  }

  /**
   * Extract quality score from BMad output
   */
  extractQualityScore(output) {
    const match = output.match(/BMad Quality Score: (\d+)\/100/);
    return match ? parseInt(match[1]) : 0;
  }

  /**
   * Create BMad-formatted commit message
   */
  createBMadCommitMessage(message, qualityScore) {
    const timestamp = new Date().toISOString().split('T')[0];
    return `${message}

BMad-Agent: development-workflow
BMad-Validation: ✅ typecheck, lint, bmad-validate
BMad-Quality-Score: ${qualityScore}/100
BMad-Timestamp: ${timestamp}`;
  }
}

// CLI Interface
async function main() {
  const workflow = new DevWorkflow();
  const command = process.argv[2];
  const args = process.argv.slice(3);

  try {
    switch (command) {
      case 'pre-epic':
        await workflow.preEpicValidation();
        break;

      case 'validate-file':
        if (!args[0]) {
          console.error('Usage: npm run dev:validate-file <file-path>');
          process.exit(1);
        }
        const isValid = await workflow.validateFile(args[0]);
        process.exit(isValid ? 0 : 1);
        break;

      case 'micro-validate':
        const passed = await workflow.microValidation();
        process.exit(passed ? 0 : 1);
        break;

      case 'task-complete':
        if (!args[0] || !args[1]) {
          console.error(
            'Usage: npm run dev:task-complete "<task-description>" "<commit-message>"'
          );
          process.exit(1);
        }
        const success = await workflow.taskCompletion(args[0], args[1]);
        process.exit(success ? 0 : 1);
        break;

      case 'auto-fix':
        await workflow.autoFix();
        break;

      default:
        console.log(`
Enhanced Development Workflow Commands:

  pre-epic              Run pre-epic validation
  validate-file <path>  Validate specific file
  micro-validate        Run micro-validation
  task-complete <desc> <msg>  Complete task with quality gates and git commit
  auto-fix              Auto-fix common issues

Options:
  --verbose             Verbose output

Examples:
  npm run dev:pre-epic
  npm run dev:validate-file src/utils/example.ts
  npm run dev:micro-validate
  npm run dev:task-complete "Implement user service" "feat: add user authentication service"
  npm run dev:auto-fix
        `);
        break;
    }
  } catch (error) {
    console.error('❌ Workflow error:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = DevWorkflow;
