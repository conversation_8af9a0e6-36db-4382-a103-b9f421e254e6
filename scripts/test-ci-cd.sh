#!/bin/bash

# 🧪 CI/CD Pipeline Test Script
# Tests the CI/CD setup without making commits

set -e

echo "🧪 =============================================="
echo "    TESTING CI/CD PIPELINE SETUP"
echo "🧪 =============================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}🔍 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Test 1: GitHub Actions Workflow Files
print_status "Testing GitHub Actions workflow files..."

if [ -f ".github/workflows/ci.yml" ]; then
    print_success "Main CI workflow exists"
else
    print_error "Main CI workflow missing"
    exit 1
fi

if [ -f ".github/workflows/pr-validation.yml" ]; then
    print_success "PR validation workflow exists"
else
    print_error "PR validation workflow missing"
    exit 1
fi

# Test 2: Pre-commit Hook Setup
print_status "Testing pre-commit hook setup..."

if [ -f ".husky/pre-commit" ] && [ -x ".husky/pre-commit" ]; then
    print_success "Pre-commit hook exists and is executable"
else
    print_error "Pre-commit hook missing or not executable"
    exit 1
fi

if [ -f ".husky/commit-msg" ] && [ -x ".husky/commit-msg" ]; then
    print_success "Commit message hook exists and is executable"
else
    print_error "Commit message hook missing or not executable"
    exit 1
fi

# Test 3: Package.json Configuration
print_status "Testing package.json configuration..."

if grep -q "husky" package.json; then
    print_success "Husky configured in package.json"
else
    print_error "Husky not found in package.json"
    exit 1
fi

if grep -q "lint-staged" package.json; then
    print_success "lint-staged configured in package.json"
else
    print_error "lint-staged not found in package.json"
    exit 1
fi

# Test 4: Prettier Configuration
print_status "Testing Prettier configuration..."

if [ -f ".prettierrc.json" ]; then
    print_success "Prettier configuration exists"
else
    print_warning "Prettier configuration missing"
fi

# Test 5: TypeScript Validation
print_status "Testing TypeScript strict validation..."

if npm run typecheck > /dev/null 2>&1; then
    print_success "TypeScript validation passes"
else
    print_error "TypeScript validation fails - fix errors before proceeding"
    npm run typecheck
    exit 1
fi

# Test 6: ESLint Validation
print_status "Testing ESLint validation..."

if npm run lint > /dev/null 2>&1; then
    print_success "ESLint validation passes"
else
    print_warning "ESLint validation has warnings/errors"
    echo "Running ESLint to show issues:"
    npm run lint
fi

# Test 7: Build Process
print_status "Testing build process..."

if npm run build > /dev/null 2>&1; then
    print_success "Build process succeeds"
else
    print_error "Build process fails"
    npm run build
    exit 1
fi

# Test 8: BMad Integration Points
print_status "Testing BMad integration points..."

# Check for required directories
if [ -d "apps/frontend" ] && [ -d "apps/backend" ] && [ -d "packages" ]; then
    print_success "Monorepo structure ready for BMad agents"
else
    print_error "Monorepo structure incomplete"
    exit 1
fi

# Check for documentation
if [ -f "docs/CONTEXT.md" ] && [ -f "docs/PROGRESS.md" ] && [ -f "docs/AGENT-TEAMS.md" ]; then
    print_success "BMad documentation structure ready"
else
    print_warning "BMad documentation needs attention"
fi

# Test 9: Docker Services
print_status "Testing Docker services availability..."

if docker compose config > /dev/null 2>&1; then
    print_success "Docker Compose configuration valid"
else
    print_error "Docker Compose configuration invalid"
    exit 1
fi

# Test 10: Helper Scripts
print_status "Testing helper scripts..."

if [ -f "scripts/validate-local.sh" ] && [ -x "scripts/validate-local.sh" ]; then
    print_success "Local validation script ready"
else
    print_warning "Local validation script missing"
fi

if [ -f "scripts/bmad-validate.sh" ] && [ -x "scripts/bmad-validate.sh" ]; then
    print_success "BMad validation script ready"
else
    print_warning "BMad validation script missing"
fi

echo ""
echo "🎉 =============================================="
echo "    CI/CD PIPELINE TEST COMPLETE!"
echo "🎉 =============================================="
echo ""
echo "📊 Test Results Summary:"
echo "   ✅ GitHub Actions workflows configured"
echo "   ✅ Pre-commit hooks functional"
echo "   ✅ TypeScript strict validation working"
echo "   ✅ Build process operational"
echo "   ✅ BMad integration points ready"
echo ""
echo "🚀 Next steps:"
echo "   1. Install dependencies: npm install"
echo "   2. Initialize hooks: npx husky init"
echo "   3. Test a commit to validate pre-commit hooks"
echo "   4. Push to GitHub to trigger CI/CD pipeline"
echo "   5. Create a PR to test PR validation"
echo ""
echo "🤖 Your CI/CD pipeline is ready for BMad autonomous development!"
echo ""