#!/bin/bash
# Test Rehabilitation Phase 1: Critical Infrastructure Fixes
# Separates unit and integration tests, fixes mock conflicts

set -e

echo "🏥 Starting Test Rehabilitation Phase 1..."
echo "🎯 Goal: Fix critical mock vs database conflicts"

cd "$(dirname "$0")/.."

# Create new test directory structure
echo "📁 Creating separated test structure..."
mkdir -p apps/backend/src/__tests__/unit
mkdir -p apps/backend/src/__tests__/integration  
mkdir -p apps/backend/src/__tests__/fixtures

# Create Jest configuration for unit tests
echo "⚙️ Creating Jest configuration for unit tests..."
cat > apps/backend/jest.unit.config.js << 'EOF'
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  displayName: 'Unit Tests',
  testMatch: ['<rootDir>/src/__tests__/unit/**/*.test.ts'],
  setupFilesAfterEnv: ['<rootDir>/src/__tests__/setup.unit.ts'],
  transform: {
    '^.+\\.tsx?$': ['ts-jest', {
      tsconfig: 'tsconfig.json',
    }],
  },
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/__tests__/**',
    '!src/prisma/**',
  ],
  coverageReporters: ['text', 'lcov'],
  testTimeout: 10000,
};
EOF

# Create Jest configuration for integration tests
echo "⚙️ Creating Jest configuration for integration tests..."
cat > apps/backend/jest.integration.config.js << 'EOF'
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  displayName: 'Integration Tests',
  testMatch: ['<rootDir>/src/__tests__/integration/**/*.test.ts'],
  setupFilesAfterEnv: ['<rootDir>/src/__tests__/setup.integration.ts'],
  transform: {
    '^.+\\.tsx?$': ['ts-jest', {
      tsconfig: 'tsconfig.json',
    }],
  },
  testTimeout: 30000,
  maxWorkers: 1, // Prevent database conflicts
};
EOF

# Create unit test setup (uses mocks)
echo "🔧 Creating unit test setup..."
cat > apps/backend/src/__tests__/setup.unit.ts << 'EOF'
/**
 * Unit Test Setup
 * Uses mocks for all external dependencies
 */

import { jest } from '@jest/globals';

// Mock Prisma Client with complete type safety
const mockPrisma = {
  tenant: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    deleteMany: jest.fn(),
  },
  user: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    deleteMany: jest.fn(),
  },
  application: {
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    deleteMany: jest.fn(),
  },
  $disconnect: jest.fn(),
} as any;

// Mock the prisma module
jest.mock('../prisma/client', () => ({
  prisma: mockPrisma,
  default: mockPrisma,
}));

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-32-chars-minimum-required';

// Global test timeout
jest.setTimeout(10000);

// Global test utilities
global.testUtils = {
  mockPrisma,
  createMockRequest: (overrides = {}) => ({
    body: {},
    params: {},
    query: {},
    headers: {},
    user: null,
    ip: '127.0.0.1',
    ...overrides,
  }),
  createMockResponse: () => {
    const res = {
      status: jest.fn(),
      json: jest.fn(),
      send: jest.fn(),
      setHeader: jest.fn(),
    } as any;
    
    res.status.mockReturnValue(res);
    res.json.mockReturnValue(res);
    res.send.mockReturnValue(res);
    
    return res;
  },
};

export {};
EOF

# Create integration test setup (uses real database)
echo "🔧 Creating integration test setup..."
cat > apps/backend/src/__tests__/setup.integration.ts << 'EOF'
/**
 * Integration Test Setup
 * Uses real test database for end-to-end testing
 */

import { PrismaClient } from '@prisma/client';

// Real Prisma client for integration tests
const testPrisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.TEST_DATABASE_URL || 'postgresql://postgres:password@localhost:5433/underwriting_test',
    },
  },
});

// Test environment variables
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 'postgresql://postgres:password@localhost:5433/underwriting_test';

// Global test timeout for integration tests
jest.setTimeout(30000);

// Clean up test data before each test
beforeEach(async () => {
  // Clean test data (order matters due to foreign keys)
  await testPrisma.communicationLog.deleteMany({
    where: { recipient: { contains: 'test' } }
  }).catch(() => {});
  
  await testPrisma.application.deleteMany({
    where: { tenantId: { contains: 'test' } }
  }).catch(() => {});
  
  await testPrisma.user.deleteMany({
    where: { email: { contains: 'test' } }
  }).catch(() => {});
  
  await testPrisma.tenant.deleteMany({
    where: { name: { contains: 'Test' } }
  }).catch(() => {});
});

// Disconnect after all tests
afterAll(async () => {
  await testPrisma.$disconnect();
});

// Global test utilities for integration tests
global.testUtils = {
  prisma: testPrisma,
  generateUniqueId: () => `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
  createTestTenant: (overrides = {}) => ({
    id: global.testUtils.generateUniqueId(),
    name: `Test Tenant ${Date.now()}`,
    slug: `test-slug-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`,
    ...overrides,
  }),
  createTestUser: (tenantId: string, overrides = {}) => ({
    id: global.testUtils.generateUniqueId(),
    email: `test-user-${Date.now()}@example.com`,
    name: `Test User ${Date.now()}`,
    tenantId,
    ...overrides,
  }),
};

export {};
EOF

# Update package.json scripts
echo "📝 Updating package.json scripts..."
cd apps/backend

# Backup current package.json
cp package.json package.json.backup

# Add new test scripts using Node.js to modify JSON
node -e "
const pkg = JSON.parse(require('fs').readFileSync('package.json', 'utf8'));
pkg.scripts = {
  ...pkg.scripts,
  'test:unit': 'jest --config jest.unit.config.js',
  'test:integration': 'jest --config jest.integration.config.js',
  'test:all': 'npm run test:unit && npm run test:integration',
  'test:watch:unit': 'jest --config jest.unit.config.js --watch',
  'test:watch:integration': 'jest --config jest.integration.config.js --watch'
};
require('fs').writeFileSync('package.json', JSON.stringify(pkg, null, 2));
"

echo "✅ Phase 1 infrastructure setup complete!"
echo ""
echo "📊 Next Steps:"
echo "1. Move existing tests to appropriate directories:"
echo "   - Unit tests → src/__tests__/unit/"
echo "   - Integration tests → src/__tests__/integration/"
echo ""
echo "2. Test the new setup:"
echo "   npm run test:unit        # Run unit tests with mocks"
echo "   npm run test:integration # Run integration tests with real DB"
echo ""
echo "3. Run Phase 2 to implement data isolation:"
echo "   ./scripts/test-rehab-phase2.sh"