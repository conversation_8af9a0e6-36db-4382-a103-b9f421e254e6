#!/usr/bin/env node

/**
 * Real-time Validation Watcher
 * Monitors file changes and runs validation in real-time
 */

const chokidar = require('chokidar');
const { execSync } = require('child_process');
const path = require('path');
const debounce = require('lodash.debounce');

class ValidationWatcher {
  constructor() {
    this.isRunning = false;
    this.lastQualityScore = 0;
    this.validationQueue = new Set();
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const prefix = {
      info: '👀',
      success: '✅',
      warning: '⚠️',
      error: '❌',
      quality: '📊',
    }[type];

    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  async runValidation(filePath) {
    if (this.isRunning) {
      this.validationQueue.add(filePath);
      return;
    }

    this.isRunning = true;

    try {
      // Quick TypeScript check for the specific file
      const tsCheck = this.quickCommand(`npx tsc --noEmit ${filePath}`);
      if (!tsCheck.success) {
        this.log(`TypeScript error in ${filePath}`, 'error');
        this.log(tsCheck.output.split('\n')[0], 'error');
        return;
      }

      // Quick ESLint check
      const lintCheck = this.quickCommand(
        `npx eslint ${filePath} --format=compact`
      );
      if (!lintCheck.success) {
        this.log(`ESLint error in ${filePath}`, 'warning');
        const errors = lintCheck.output
          .split('\n')
          .filter((line) => line.includes('error')).length;
        this.log(`${errors} ESLint errors found`, 'warning');
        return;
      }

      this.log(`${path.basename(filePath)} validation passed`, 'success');

      // Run quality check every 5 files or 30 seconds
      this.scheduleQualityCheck();
    } catch (error) {
      this.log(`Validation error: ${error.message}`, 'error');
    } finally {
      this.isRunning = false;

      // Process queue
      if (this.validationQueue.size > 0) {
        const nextFile = Array.from(this.validationQueue)[0];
        this.validationQueue.delete(nextFile);
        setTimeout(() => this.runValidation(nextFile), 100);
      }
    }
  }

  quickCommand(command) {
    try {
      const output = execSync(command, { encoding: 'utf8', timeout: 5000 });
      return { success: true, output };
    } catch (error) {
      return { success: false, output: error.stdout || error.message };
    }
  }

  scheduleQualityCheck = debounce(async () => {
    this.log('Running quality assessment...', 'quality');

    const qualityCheck = this.quickCommand('npm run bmad:quality-score');
    if (qualityCheck.success) {
      const match = qualityCheck.output.match(/BMad Quality Score: (\d+)\/100/);
      if (match) {
        const score = parseInt(match[1]);
        const trend =
          score > this.lastQualityScore
            ? '📈'
            : score < this.lastQualityScore
              ? '📉'
              : '➡️';
        this.log(`Quality Score: ${score}/100 ${trend}`, 'quality');
        this.lastQualityScore = score;

        if (score < 85) {
          this.log(
            'Quality score below threshold! Consider running auto-fix.',
            'warning'
          );
        }
      }
    }
  }, 30000);

  start() {
    this.log('🚀 Starting real-time validation watcher...', 'info');
    this.log('Watching: src/**/*.{ts,js}', 'info');
    this.log('Press Ctrl+C to stop', 'info');

    const watcher = chokidar.watch(['src/**/*.ts', 'src/**/*.js'], {
      ignored: /(^|[\/\\])\../, // ignore dotfiles
      persistent: true,
      ignoreInitial: true,
    });

    watcher
      .on('change', (filePath) => {
        this.log(`File changed: ${filePath}`, 'info');
        this.runValidation(filePath);
      })
      .on('add', (filePath) => {
        this.log(`File added: ${filePath}`, 'info');
        this.runValidation(filePath);
      })
      .on('error', (error) => {
        this.log(`Watcher error: ${error}`, 'error');
      });

    // Initial quality check
    setTimeout(() => this.scheduleQualityCheck(), 2000);

    // Graceful shutdown
    process.on('SIGINT', () => {
      this.log('Stopping validation watcher...', 'info');
      watcher.close();
      process.exit(0);
    });
  }
}

// Start watcher if run directly
if (require.main === module) {
  const watcher = new ValidationWatcher();
  watcher.start();
}

module.exports = ValidationWatcher;
