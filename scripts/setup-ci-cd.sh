#!/bin/bash

# 🚀 AI Underwriting Platform - CI/CD Setup Script
# This script sets up the complete CI/CD pipeline and development tools

set -e  # Exit on any error

echo "🚀 =============================================="
echo "    AI UNDERWRITING PLATFORM - CI/CD SETUP"
echo "🚀 =============================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -f "turbo.json" ]; then
    print_error "This script must be run from the project root directory"
    exit 1
fi

print_status "Checking Node.js version..."
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 20 ]; then
    print_error "Node.js 20+ is required. Current version: $(node -v)"
    exit 1
fi
print_success "Node.js version check passed: $(node -v)"

print_status "Installing CI/CD dependencies..."
npm install --save-dev husky lint-staged
print_success "Dependencies installed"

print_status "Initializing Husky..."
npx husky init
print_success "Husky initialized"

print_status "Setting up pre-commit hooks..."
echo '#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🔍 Running pre-commit checks..."

# TypeScript strict validation
echo "🚫 Enforcing ZERO TypeScript errors policy..."
npm run typecheck
if [ $? -ne 0 ]; then
  echo "❌ TypeScript errors detected. Commit blocked."
  echo "💡 Fix all TypeScript errors before committing."
  exit 1
fi

# Run lint-staged
echo "🧹 Running lint-staged..."
npx lint-staged
if [ $? -ne 0 ]; then
  echo "❌ Linting errors detected. Commit blocked."
  exit 1
fi

echo "✅ Pre-commit checks passed!"
' > .husky/pre-commit

chmod +x .husky/pre-commit
print_success "Pre-commit hooks configured"

print_status "Setting up commit message validation..."
echo '#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Commit message validation
commit_regex="^(feat|fix|docs|style|refactor|test|chore|epic|story)(\(.+\))?: .{1,50}"

if ! grep -qE "$commit_regex" "$1"; then
  echo "❌ Invalid commit message format!"
  echo ""
  echo "📋 Commit message should follow the format:"
  echo "   type(scope): description"
  echo ""
  echo "📌 Types: feat, fix, docs, style, refactor, test, chore, epic, story"
  echo "📌 Examples:"
  echo "   feat(auth): add JWT authentication"
  echo "   fix(api): resolve database connection issue"
  echo "   epic(1): implement AI conversation interface"
  echo "   story(1.1): add OpenRouter integration"
  echo ""
  exit 1
fi

echo "✅ Commit message format is valid"
' > .husky/commit-msg

chmod +x .husky/commit-msg
print_success "Commit message validation configured"

print_status "Creating CI/CD helper scripts..."
mkdir -p scripts

# Create validation script
echo '#!/bin/bash

# 🔍 Local CI/CD Validation Script
# Run this to validate your changes before pushing

echo "🔍 Running local CI/CD validation..."

echo "📦 Installing dependencies..."
npm ci

echo "🚫 TypeScript strict validation..."
npm run typecheck

echo "🧹 ESLint validation..."
npm run lint

echo "🧪 Running tests..."
npm run test

echo "🏗️ Building project..."
npm run build

echo "✅ All validations passed! Ready to push."
' > scripts/validate-local.sh

chmod +x scripts/validate-local.sh

# Create BMad agent helper script
echo '#!/bin/bash

# 🤖 BMad Agent Development Helper
# Validates BMad agent requirements and integration points

echo "🤖 BMad Agent Integration Validation..."

echo "🎨 Frontend Team validation..."
if [ -d "apps/frontend" ] && [ -f "apps/frontend/package.json" ]; then
  echo "✅ Frontend infrastructure ready"
else
  echo "❌ Frontend infrastructure missing"
fi

echo "⚙️ Backend Team validation..."
if [ -d "apps/backend" ] && [ -f "apps/backend/package.json" ]; then
  echo "✅ Backend infrastructure ready"
else
  echo "❌ Backend infrastructure missing"
fi

echo "📦 Shared packages validation..."
if [ -d "packages" ]; then
  echo "✅ Shared packages structure ready"
else
  echo "❌ Shared packages missing"
fi

echo "🚫 TypeScript strict policy validation..."
npm run typecheck

echo "📋 Documentation check..."
if [ -f "docs/CONTEXT.md" ] && [ -f "docs/PROGRESS.md" ]; then
  echo "✅ BMad documentation structure ready"
else
  echo "⚠️ BMad documentation needs updates"
fi

echo "🤖 BMad agent environment is ready for autonomous development!"
' > scripts/bmad-validate.sh

chmod +x scripts/bmad-validate.sh

print_success "Helper scripts created"

print_status "Validating GitHub Actions workflows..."
if [ -f ".github/workflows/ci.yml" ]; then
    print_success "Main CI workflow configured"
else
    print_warning "Main CI workflow not found"
fi

if [ -f ".github/workflows/pr-validation.yml" ]; then
    print_success "PR validation workflow configured"
else
    print_warning "PR validation workflow not found"
fi

print_status "Testing the setup..."
echo "🧪 Running initial validation..."

# Test TypeScript
if npm run typecheck > /dev/null 2>&1; then
    print_success "TypeScript validation working"
else
    print_warning "TypeScript validation needs attention"
fi

# Test linting
if npm run lint > /dev/null 2>&1; then
    print_success "ESLint validation working"
else
    print_warning "ESLint validation needs attention"
fi

echo ""
echo "🎉 =============================================="
echo "    CI/CD SETUP COMPLETE!"
echo "🎉 =============================================="
echo ""
echo "📋 What was configured:"
echo "   ✅ GitHub Actions CI/CD pipeline"
echo "   ✅ Pre-commit hooks with TypeScript validation"
echo "   ✅ Commit message validation"
echo "   ✅ Code formatting (Prettier)"
echo "   ✅ BMad agent integration validation"
echo ""
echo "🚀 Next steps:"
echo "   1. Push your changes to trigger the CI/CD pipeline"
echo "   2. Create a pull request to test PR validation"
echo "   3. Deploy BMad autonomous agent teams"
echo "   4. Begin Epic 1 development"
echo ""
echo "🛠️ Useful commands:"
echo "   npm run ci:validate       - Run full local validation"
echo "   ./scripts/validate-local.sh  - Test before pushing"
echo "   ./scripts/bmad-validate.sh   - Validate BMad integration"
echo ""
echo "🤖 Your infrastructure is now ready for autonomous development!"
echo ""