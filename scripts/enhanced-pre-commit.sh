#!/bin/bash

# Enhanced Pre-commit Hook with Quality Gates
# Prevents commits that don't meet quality standards

set -e

echo "🔍 Enhanced Pre-commit Validation Starting..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "success")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "error")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "warning")
            echo -e "${YELLOW}⚠️ $message${NC}"
            ;;
        *)
            echo "🔍 $message"
            ;;
    esac
}

# Check if there are any staged changes
if git diff --cached --quiet; then
    print_status "warning" "No staged changes found"
    exit 0
fi

# Get list of staged TypeScript files
STAGED_TS_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(ts|js)$' || true)

if [ -z "$STAGED_TS_FILES" ]; then
    print_status "success" "No TypeScript/JavaScript files to validate"
    exit 0
fi

print_status "info" "Validating staged files: $(echo $STAGED_TS_FILES | wc -w) files"

# Step 1: TypeScript compilation check
print_status "info" "Step 1: TypeScript compilation check..."
if npm run typecheck > /dev/null 2>&1; then
    print_status "success" "TypeScript compilation passed"
else
    print_status "error" "TypeScript compilation failed"
    echo "Run 'npm run typecheck' to see details"
    exit 1
fi

# Step 2: ESLint check for staged files
print_status "info" "Step 2: ESLint validation..."
ESLINT_ERRORS=0
for file in $STAGED_TS_FILES; do
    if [ -f "$file" ]; then
        if ! npx eslint "$file" > /dev/null 2>&1; then
            ESLINT_ERRORS=$((ESLINT_ERRORS + 1))
        fi
    fi
done

if [ $ESLINT_ERRORS -gt 0 ]; then
    print_status "error" "ESLint validation failed for $ESLINT_ERRORS files"
    print_status "info" "Run 'npm run dev:auto-fix' to attempt automatic fixes"
    print_status "info" "Or run 'npm run lint' to see details"
    exit 1
else
    print_status "success" "ESLint validation passed"
fi

# Step 3: Prettier formatting check
print_status "info" "Step 3: Prettier formatting check..."
PRETTIER_ERRORS=0
for file in $STAGED_TS_FILES; do
    if [ -f "$file" ]; then
        if ! npx prettier --check "$file" > /dev/null 2>&1; then
            PRETTIER_ERRORS=$((PRETTIER_ERRORS + 1))
        fi
    fi
done

if [ $PRETTIER_ERRORS -gt 0 ]; then
    print_status "warning" "Prettier formatting issues found in $PRETTIER_ERRORS files"
    print_status "info" "Auto-fixing formatting..."
    
    for file in $STAGED_TS_FILES; do
        if [ -f "$file" ]; then
            npx prettier --write "$file" > /dev/null 2>&1 || true
        fi
    done
    
    # Re-stage the formatted files
    git add $STAGED_TS_FILES
    print_status "success" "Formatting issues fixed and re-staged"
fi

# Step 4: BMad quality validation
print_status "info" "Step 4: BMad quality validation..."
if npm run bmad:validate > /dev/null 2>&1; then
    # Extract quality score
    QUALITY_SCORE=$(npm run bmad:quality-score 2>/dev/null | grep -o '[0-9]\+/100' | head -1 | cut -d'/' -f1)
    
    if [ -n "$QUALITY_SCORE" ] && [ "$QUALITY_SCORE" -ge 85 ]; then
        print_status "success" "BMad validation passed (Score: $QUALITY_SCORE/100)"
    else
        print_status "error" "BMad quality score too low: $QUALITY_SCORE/100 (minimum: 85)"
        print_status "info" "Run 'npm run bmad:self-heal' to attempt improvements"
        exit 1
    fi
else
    print_status "error" "BMad validation failed"
    print_status "info" "Run 'npm run bmad:validate' to see details"
    exit 1
fi

# Step 5: Check for common security issues
print_status "info" "Step 5: Security checks..."
SECURITY_ISSUES=0

for file in $STAGED_TS_FILES; do
    if [ -f "$file" ]; then
        # Check for potential secrets
        if grep -qE "(password|secret|key|token).*=.*['\"][^'\"]{8,}" "$file"; then
            print_status "warning" "Potential secret found in $file"
            SECURITY_ISSUES=$((SECURITY_ISSUES + 1))
        fi
        
        # Check for console.log in production code (excluding dev scripts)
        if [[ "$file" != *"scripts/"* ]] && [[ "$file" != *"dev-"* ]] && grep -q "console\.log" "$file"; then
            print_status "warning" "console.log found in $file (consider using proper logging)"
        fi
        
        # Check for TODO/FIXME comments
        if grep -qE "(TODO|FIXME|XXX)" "$file"; then
            print_status "warning" "TODO/FIXME comments found in $file"
        fi
    fi
done

if [ $SECURITY_ISSUES -gt 0 ]; then
    print_status "error" "Security issues found in $SECURITY_ISSUES files"
    print_status "info" "Review the files and remove any hardcoded secrets"
    exit 1
fi

# Final success message
print_status "success" "All pre-commit checks passed!"
print_status "info" "Quality Score: $QUALITY_SCORE/100"
print_status "info" "Files validated: $(echo $STAGED_TS_FILES | wc -w)"

exit 0