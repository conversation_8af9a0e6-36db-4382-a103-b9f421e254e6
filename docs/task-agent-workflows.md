# Task Agent Completion Workflows

## Overview

This document defines standardized completion workflows for Task agents to ensure consistent quality
gates and git operations are integrated into every task completion.

## Standard Task Completion Pattern

Every Task agent should follow this completion pattern:

### Phase 1: Implementation

1. Implement the required functionality
2. Use BMad-compliant templates from `/templates/`
3. Follow strict TypeScript typing
4. Include comprehensive error handling

### Phase 2: Validation

```bash
# Real-time validation during development
npm run dev:watch  # In separate terminal

# File-level validation after each file
npm run dev:validate-file src/path/to/new-file.ts

# Micro-validation after each utility/service
npm run dev:micro-validate
```

### Phase 3: Quality Gates

```bash
# Comprehensive quality check
npm run dev:task-complete "Task Description" "commit: message format"
```

This single command runs:

1. TypeScript compilation check
2. ESLint validation
3. BMad quality validation (minimum 85/100)
4. Git status check
5. Automated git add and commit with BMad format
6. Final validation confirmation

## Task Agent Instructions

### For Human Task Delegation

When delegating to Task agents, always include these instructions:

````markdown
## Task Completion Requirements

**CRITICAL: Follow this exact completion sequence:**

1. **Implementation Phase**
   - Use templates from `/templates/` directory
   - Follow strict TypeScript typing (no `any` types)
   - Include comprehensive error handling
   - Add input validation with zod schemas

2. **Real-time Validation** (Optional but recommended)
   ```bash
   npm run dev:watch  # Run in background during development
   ```
````

3. **File Validation** (After each file creation)

   ```bash
   npm run dev:validate-file <file-path>
   ```

4. **Task Completion** (REQUIRED)
   ```bash
   npm run dev:task-complete "Task description" "commit message"
   ```

**Quality Requirements:**

- Zero TypeScript compilation errors
- Zero ESLint errors
- BMad quality score ≥ 85/100
- All changes committed with BMad format

**If quality gates fail:**

- Run `npm run dev:auto-fix` to attempt automatic fixes
- Manual fixes may be required for complex issues
- Do not proceed with commit until all gates pass

````

### For Claude Code Task Tool

When using the Task tool, include this in the prompt:

```markdown
IMPORTANT: This task must follow BMad quality-first development workflow.

After completing the implementation:

1. Run file validation for each created file:
   `npm run dev:validate-file <file-path>`

2. Complete the task with integrated quality gates and git commit:
   `npm run dev:task-complete "Task description" "commit message"`

Requirements:
- Zero TypeScript errors
- Zero ESLint errors
- BMad quality score ≥ 85/100
- BMad-formatted commit message
- Use templates from `/templates/` directory

If quality gates fail, run `npm run dev:auto-fix` and retry.
````

## BMad Commit Message Format

All commits should follow this format:

```
feat: implement user authentication service

BMad-Agent: development-workflow
BMad-Validation: ✅ typecheck, lint, bmad-validate
BMad-Quality-Score: 92/100
BMad-Timestamp: 2025-01-20
```

## Quality Gate Thresholds

| Check      | Requirement        | Action if Failed             |
| ---------- | ------------------ | ---------------------------- |
| TypeScript | Zero errors        | Fix compilation issues       |
| ESLint     | Zero errors        | Run `npm run dev:auto-fix`   |
| BMad Score | ≥ 85/100           | Run `npm run bmad:self-heal` |
| Security   | No secrets         | Remove hardcoded credentials |
| Formatting | Prettier compliant | Auto-fixed during pre-commit |

## Error Recovery Workflows

### TypeScript Errors

```bash
# Check specific errors
npm run typecheck

# Common fixes:
# - Add missing return types
# - Replace 'any' with proper types
# - Add missing imports
# - Fix type compatibility issues
```

### ESLint Errors

```bash
# Attempt auto-fix
npm run dev:auto-fix

# Manual review for:
# - @typescript-eslint/no-explicit-any
# - @typescript-eslint/no-unsafe-*
# - Missing return type annotations
```

### BMad Quality Issues

```bash
# Attempt self-healing
npm run bmad:self-heal

# Check current score
npm run bmad:quality-score

# Manual improvements:
# - Add comprehensive error handling
# - Improve code documentation
# - Follow architectural patterns
# - Add input validation
```

## Development Environment Setup

### Prerequisites

```bash
# Install required dependencies
npm install chokidar lodash.debounce

# Make scripts executable
chmod +x scripts/*.sh
chmod +x scripts/*.js

# Setup enhanced git hooks
cp scripts/enhanced-pre-commit.sh .husky/pre-commit
```

### Real-time Development

```bash
# Terminal 1: Start real-time validation watcher
npm run dev:watch

# Terminal 2: Start development servers
npm run dev

# Terminal 3: Development work
# - Create files using templates
# - Run file validation after each file
# - Run task completion when done
```

## Best Practices

### 1. Use Templates

- Always start with templates from `/templates/`
- Customize for specific use case
- Maintain BMad compliance patterns

### 2. Incremental Validation

- Validate each file after creation
- Don't wait until the end to check quality
- Fix issues immediately when found

### 3. Quality-First Mindset

- Aim for 90+ BMad quality score
- Zero tolerance for TypeScript errors
- Comprehensive error handling
- Input validation for all public methods

### 4. Git Hygiene

- Commit after each completed utility/service
- Use BMad-formatted commit messages
- Never commit with failing quality gates
- Keep commits focused and atomic

## Common Patterns

### Service Implementation

```typescript
// 1. Start with template
cp templates/service-template.ts src/services/user-service.ts

// 2. Customize for specific service
// 3. Validate during development
npm run dev:validate-file src/services/user-service.ts

// 4. Complete task
npm run dev:task-complete "Implement user service" "feat: add user authentication service"
```

### Utility Implementation

```typescript
// 1. Start with template
cp templates/utility-template.ts src/utils/validation-util.ts

// 2. Implement utility functions
// 3. Validate
npm run dev:validate-file src/utils/validation-util.ts

// 4. Complete task
npm run dev:task-complete "Add validation utility" "feat: add input validation utilities"
```

This workflow ensures consistent quality and eliminates the 154 ESLint errors we encountered in
Epic-9A by catching issues early and enforcing standards at every step.
