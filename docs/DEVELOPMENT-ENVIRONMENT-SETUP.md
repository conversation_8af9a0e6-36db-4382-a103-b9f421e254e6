# Development Environment Setup Guide

**AI Underwriting Platform - Monorepo with Turborepo**

---

## 🎯 **Quick Start**

### Prerequisites

- **Node.js 20+** with npm 10+
- **Docker Desktop** running
- **Git** installed

### One-Command Setup

```bash
./setup-development-environment.sh
```

---

## 🏗️ **Architecture Overview**

### Monorepo Structure

```
/Users/<USER>/Developer/Base/OLA/
├── apps/
│   ├── frontend/          # Next.js 15 + React 19 + shadcn/ui
│   └── backend/           # Express 4.x + Prisma 6 + TypeScript
├── packages/
│   ├── shared/           # Shared utilities
│   ├── ui/               # Shared UI components
│   └── config/           # Shared configurations
├── docs/                 # Documentation
├── docker-compose.yml    # Services (PostgreSQL, Redis, Ollama)
├── turbo.json           # Turborepo configuration
└── setup-development-environment.sh
```

### Technology Stack

- **Frontend**: Next.js 15, React 19, TypeScript 5.8, Tailwind CSS, shadcn/ui
- **Backend**: Express 4.21, Prisma 6.12, TypeScript 5.8
- **Database**: PostgreSQL 15 + Redis 7
- **AI**: Ollama (local) + OpenRouter (cloud)
- **Monorepo**: Turborepo 2.5+ with npm workspaces

---

## 🚀 **Development Commands**

### Primary Commands

```bash
# Start all development servers
npm run dev

# Build entire monorepo
npm run build

# Run tests across workspaces
npm run test

# Lint all code
npm run lint

# Type check all TypeScript
npm run typecheck
```

### Specific Workspaces

```bash
# Frontend only
npm run dev:frontend

# Backend only
npm run dev:backend

# Build specific workspace
npm run build:frontend
npm run build:backend
```

### Database Commands

```bash
# Setup database
npm run setup:db

# Generate Prisma client
npm run db:generate

# Run migrations
npm run db:migrate

# Open Prisma Studio
npm run db:studio

# Reset database
npm run db:reset
```

### Docker Commands

```bash
# Start all services
npm run docker:up

# Stop all services
npm run docker:down

# View logs
npm run docker:logs

# Clean all containers
npm run docker:clean
```

---

## 🔧 **Environment Configuration**

### Required Files

1. **Root `.env`** - Main environment variables
2. **Frontend `.env.local`** - Next.js specific variables

### Environment Setup

```bash
# Copy template and edit
cp .env.example .env

# Required API Keys:
# - OPENROUTER_API_KEY (from https://openrouter.ai/)
# - Other services as needed
```

### Key Environment Variables

```bash
# Database
DATABASE_URL="postgresql://postgres:password@localhost:5432/ai_underwriting"
REDIS_URL="redis://localhost:6379"

# Ports
FRONTEND_PORT=3000
BACKEND_PORT=5000
POSTGRES_PORT=5432
REDIS_PORT=6379
OLLAMA_PORT=11434

# OpenRouter API
OPENROUTER_API_KEY=your-api-key-here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Local LLM
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=llama3.2

# Security
JWT_SECRET=your-jwt-secret
SESSION_SECRET=your-session-secret
```

---

## 🐳 **Docker Services**

### Services Included

- **PostgreSQL 15**: Main database
- **Redis 7**: Caching and sessions
- **Ollama**: Local LLM server

### Service Management

```bash
# Start services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Manual Docker Setup (if needed)

```bash
# PostgreSQL
docker run --name postgres-dev \
  -e POSTGRES_DB=ai_underwriting \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 \
  -d postgres:15

# Redis
docker run --name redis-dev \
  -p 6379:6379 \
  -d redis:7-alpine

# Ollama
docker run --name ollama-dev \
  -p 11434:11434 \
  -d ollama/ollama
```

---

## 📱 **Frontend Development**

### Technology Stack

- **Framework**: Next.js 15 with App Router
- **UI**: React 19 + TypeScript 5.8
- **Styling**: Tailwind CSS + shadcn/ui
- **Components**: Radix UI primitives
- **Forms**: React Hook Form + Zod validation
- **Charts**: Recharts
- **Real-time**: Socket.IO client

### Project Structure

```
apps/frontend/
├── src/
│   ├── app/              # Next.js App Router
│   │   ├── (auth)/      # Authentication routes
│   │   ├── (dashboard)/ # Dashboard routes
│   │   └── api/         # API routes
│   ├── components/
│   │   ├── ui/          # shadcn/ui components
│   │   ├── forms/       # Form components
│   │   ├── charts/      # Chart components
│   │   └── layout/      # Layout components
│   ├── lib/
│   │   ├── utils.ts     # Utility functions
│   │   ├── api.ts       # API client
│   │   └── types.ts     # TypeScript types
│   ├── hooks/           # Custom React hooks
│   └── store/           # State management
├── public/              # Static assets
├── components.json      # shadcn/ui config
└── package.json
```

### shadcn/ui Components

```bash
# Add new components
npx shadcn@latest add button
npx shadcn@latest add input
npx shadcn@latest add card
npx shadcn@latest add dialog
```

---

## ⚙️ **Backend Development**

### Technology Stack

- **Framework**: Express 4.21 + TypeScript 5.8
- **Database**: Prisma 6.12 ORM + PostgreSQL 15
- **Authentication**: JWT + bcrypt
- **Validation**: Joi
- **Logging**: Winston
- **Real-time**: Socket.IO
- **Caching**: Redis + ioredis

### Project Structure

```
apps/backend/
├── src/
│   ├── routes/          # API routes
│   ├── controllers/     # Request handlers
│   ├── services/        # Business logic
│   ├── middleware/      # Express middleware
│   ├── utils/           # Utility functions
│   ├── types/           # TypeScript types
│   └── index.ts         # Server entry point
├── prisma/
│   ├── schema.prisma    # Database schema
│   └── migrations/      # Database migrations
├── tests/               # Test files
├── tsconfig.json        # TypeScript config
└── package.json
```

### Prisma Commands

```bash
# Generate client
npx prisma generate

# Create migration
npx prisma migrate dev --name migration-name

# Reset database
npx prisma migrate reset

# Open Studio
npx prisma studio
```

---

## 🤖 **AI Services Setup**

### OpenRouter (Cloud LLMs)

1. **Get API Key**: Visit https://openrouter.ai/
2. **Add Credits**: Fund your account
3. **Configure**: Set `OPENROUTER_API_KEY` in `.env`

### Ollama (Local LLM)

```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Pull model
ollama pull llama3.2

# Start server (Docker handles this)
ollama serve
```

---

## 🔍 **Development URLs**

### Primary Services

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **Prisma Studio**: http://localhost:5555

### Docker Services

- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379
- **Ollama**: http://localhost:11434

### Health Checks

```bash
# Backend health
curl http://localhost:5000/health

# Ollama health
curl http://localhost:11434/api/tags

# PostgreSQL
docker exec postgres-dev pg_isready -U postgres

# Redis
docker exec redis-dev redis-cli ping
```

---

## 🧪 **Testing & Quality**

### Running Tests

```bash
# All tests
npm run test

# Frontend tests
npm run test:frontend

# Backend tests
npm run test:backend

# Watch mode
npm run test -- --watch
```

### Code Quality

```bash
# Lint all code
npm run lint

# Fix linting issues
npm run lint:fix

# Type checking
npm run typecheck

# Format code
npm run format
```

---

## 🔧 **Troubleshooting**

### Common Issues

#### Port Conflicts

```bash
# Check port usage
lsof -i :3000
lsof -i :5000
lsof -i :5432

# Kill processes
kill -9 $(lsof -t -i:3000)
```

#### Docker Issues

```bash
# Reset Docker
npm run docker:clean

# Restart services
npm run docker:down
npm run docker:up
```

#### Dependency Issues

```bash
# Clean install
rm -rf node_modules package-lock.json
npm install

# Reset Turborepo cache
npx turbo clean
```

#### Database Issues

```bash
# Reset Prisma
npm run db:reset

# Regenerate client
npm run db:generate
```

---

## 🚀 **Getting Started Checklist**

### Initial Setup

- [ ] Clone repository
- [ ] Install Node.js 20+
- [ ] Install Docker Desktop
- [ ] Run `npm install`
- [ ] Copy `.env.example` to `.env`
- [ ] Get OpenRouter API key
- [ ] Run `./setup-development-environment.sh`

### Development Ready

- [ ] Services running (`npm run docker:up`)
- [ ] Database migrated (`npm run db:migrate`)
- [ ] Frontend running (`npm run dev:frontend`)
- [ ] Backend running (`npm run dev:backend`)
- [ ] Health checks passing

### First Development Task

- [ ] Create first component
- [ ] Add API endpoint
- [ ] Test integration
- [ ] Review documentation

---

## 📚 **Additional Resources**

- **PRD**: `./docs/prd.md`
- **Epic Stories**: `./docs/epic-*.md`
- **Infrastructure**: `./docs/INFRASTRUCTURE-REQUIREMENTS.md`
- **TypeScript Policy**: `./docs/TYPESCRIPT-STRICT-POLICY.md`

---

## 🆘 **Getting Help**

### Quick Fixes

1. **Restart everything**: `npm run docker:down && npm run docker:up && npm run dev`
2. **Clean rebuild**: `npm run clean && npm install && npm run build`
3. **Reset database**: `npm run db:reset && npm run db:migrate`

### Support

- Check logs: `npm run docker:logs`
- Review setup script: `./setup-development-environment.sh`
- Verify environment: Check all `.env` variables
