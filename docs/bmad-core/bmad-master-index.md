# 🎭 BMad Master Index - AI Underwriting Platform

> **BMad Control Center** | Status: Active | Last Updated: [Current Date]
>
> **Purpose:** Single entry point for all BMad operations and agent coordination **Scope:** Complete
> project orchestration and tracking

## 🚀 Quick Start Commands

### Essential BMad Commands

```bash
# Core Operations
*help                    # Show all available commands and agents
*status                  # Current project status and active tasks
*agent [name]           # Transform into specialized agent
*workflow [name]        # Execute structured workflow
*plan                   # Create detailed execution plan

# Immediate Actions Available (SEQUENCE RESTORED)
🎯 *agent story-manager      # Break down Epic-9 into actionable stories
🎯 *workflow epic-to-stories epic-9  # Execute Epic-9 breakdown workflow
📋 *agent tech-architect     # Design authentication architecture
🚀 *agent backend-specialist # Implement user management system
```

## 📊 Current Project Status

### Overall Progress

- **Project Phase:** Foundation (Phase 1 of 3) - PROPER SEQUENCE RESTORED
- **Current Focus:** Epic-9 (User Management & Authentication) - Starting
- **BMad Readiness:** 🟢 Back on Track - Following Dependencies
- **Next Milestone:** Complete Epic-9 authentication foundation
- **📍 Implementation GPS:** `/bmad-core/implementation-roadmap.md` (architectural path)

### Epic Status Dashboard

| Epic                              | Priority | Status                   | Agent Ready | Stories | Completion |
| --------------------------------- | -------- | ------------------------ | ----------- | ------- | ---------- |
| Epic-9: User Management           | P0       | 🔥 **STARTING NOW**      | ✅          | 0/6     | 0%         |
| Epic-7: Plugin Architecture       | P0       | ⏳ Next after Epic-9     | ✅          | 0/4     | 0%         |
| Epic-2: AI Decision Engine        | P0       | ⏳ After Epic-7          | ✅          | 0/5     | 0%         |
| Epic-6: Billing System            | P0       | ⏸️ **PAUSED** (48% done) | ✅          | 3/8     | 48%        |
| Epic-1: AI Application Experience | P1       | 📋 Documented            | ✅          | 0/6     | 0%         |

### Agent Activation Status

| Agent                 | Status     | Current Task                   | Ready for Activation |
| --------------------- | ---------- | ------------------------------ | -------------------- |
| 📋 Story Manager      | **NEEDED** | Epic-9 breakdown required      | ✅ Ready to Activate |
| 🏧 Tech Architect     | Standby    | Auth system design pending     | ✅ Ready             |
| 🚀 Backend Specialist | Standby    | User management implementation | ✅ Ready             |
| 🏗️ Tech Architect     | Standby    | Awaiting system design request | ✅ Ready             |
| 🎨 UI Designer        | Standby    | Waiting for backend APIs       | ✅ Ready             |
| 🔗 Integration Agent  | Configured | Plugin architecture planning   | ✅ Ready             |
| 💰 Finance Agent      | Configured | Billing system design          | ✅ Ready             |

## 🗂️ BMad Structure Overview

### Core Directories

```
/docs/bmad-core/
├── project-context/     # Master PRD and context documents
├── epics/              # All 9 epics with detailed specifications
├── workflows/          # Structured execution workflows
├── agents/             # Specialized agent configurations
├── data/               # Reference data and knowledge base
├── templates/          # Reusable document templates
└── utils/              # Helper scripts and tools
```

### Key Documents

- **📄 Master PRD:** `/project-context/prd-master.md` - Complete product vision
- **📊 Epic Registry:** `/epic-registry.md` - Epic tracking and coordination
- **📋 Story Manager:** `/agents/story-manager.md` - Epic breakdown specialist
- **🏗️ Tech Architect:** `/agents/tech-architect.md` - System design specialist
- **⚡ Epic Workflow:** `/workflows/epic-to-stories.md` - Structured breakdown process

## 🎯 Recommended Immediate Actions

### Priority 1: Epic-9 Breakdown (User Management & Auth)

**Why Critical:** True foundation - everything needs authentication and users **Estimated Time:**
1-2 days for breakdown, 1-2 weeks for implementation **Commands:**

```bash
*agent story-manager     # Transform to Story Manager Agent
*workflow epic-to-stories epic-9  # Execute structured breakdown
```

### Priority 2: Technical Architecture Design

**Why Important:** Backend-first approach requires solid foundation **Estimated Time:** 1-2 weeks
**Commands:**

```bash
*agent tech-architect    # Transform to Tech Architect Agent
*task system-design epic-2  # Design AI decision engine architecture
```

### Priority 3: Plugin Architecture Framework

**Why Strategic:** Enables mock-first development and gradual integration **Estimated Time:** 1 week
**Commands:**

```bash
*agent tech-architect    # System design specialist
*task integration-design epic-7  # Design plugin adapter framework
```

## 🔄 BMad Workflow Sequences

### Epic Processing Workflow

```mermaid
graph LR
    A[Epic Analysis] --> B[Story Breakdown]
    B --> C[Technical Specification]
    C --> D[Agent Assignment]
    D --> E[Implementation]
    E --> F[Quality Validation]
```

### Agent Collaboration Flow

```mermaid
graph LR
    A[Story Manager] --> B[Tech Architect]
    B --> C[Implementation Agents]
    C --> D[Quality Validation]
    D --> E[Epic Completion]
```

## 📋 Quality Assurance

### BMad Quality Standards

- **Story Quality:** 85+ BMad score required for all stories
- **Technical Compliance:** Zero TypeScript errors tolerance
- **Test Coverage:** 90%+ required for all implementations
- **Documentation:** Complete specifications for all components

### Validation Checkpoints

1. **Epic Breakdown:** Story Manager validation
2. **Technical Design:** Tech Architect review
3. **Implementation:** Agent-specific quality gates
4. **Integration:** Cross-system testing
5. **Deployment:** Production readiness checklist

## 🎮 Agent Specializations

### Available Specialist Agents

- **📋 Story Manager** - Epic decomposition and backlog management
- **🏗️ Tech Architect** - System design and technical architecture
- **🎨 UI Designer** - Interface design and user experience
- **🔗 Integration Agent** - External services and API integration
- **💰 Finance Agent** - Billing system and revenue management
- **🔐 Security Agent** - Authentication, authorization, and compliance
- **📱 Mobile Agent** - Mobile optimization and accessibility

### Agent Coordination

- **Primary Owner:** Each epic has a primary agent assignment
- **Collaborators:** Cross-functional teams for complex features
- **Handoffs:** Structured deliverables between agent phases
- **Quality Gates:** BMad validation at each transition

## 📈 Success Metrics & KPIs

### Project-Level Metrics

- **Epic Completion Rate:** 0/9 epics completed
- **Story Delivery Velocity:** Not yet established
- **BMad Quality Score:** Target 85+ average
- **Technical Debt Ratio:** Target <10%

### Agent Performance Metrics

- **Story Quality:** 100% BMad compliance target
- **Estimation Accuracy:** <20% variance target
- **Handoff Efficiency:** <1 day transition time target
- **Cross-Agent Collaboration:** 4.5/5 satisfaction target

## 🔧 BMad Configuration

### Current Environment

- **Project Type:** AI-Powered SaaS Platform
- **Architecture:** Multi-tenant, self-hosted
- **Technology Stack:** Node.js/TypeScript, Next.js, PostgreSQL, Redis
- **Development Approach:** Backend-first with plugin architecture

### BMad Integrations

- **Quality Gates:** Automated BMad validation on git push
- **Agent Memory:** Context preservation across sessions
- **Workflow Automation:** Structured process execution
- **Documentation Generation:** Automated spec updates

---

## 🎯 Immediate Next Step

**Ready to begin Epic-2 breakdown immediately.**

**Recommended Command:** `*agent story-manager`

This will transform into the Story Manager Agent and begin the structured breakdown of Epic-2 (AI
Decision Engine) into actionable user stories with complete technical specifications.

**Alternative:** `*workflow epic-to-stories epic-2` to execute the complete structured workflow.

---

_BMad Master Index - Your gateway to efficient, quality-driven development_
