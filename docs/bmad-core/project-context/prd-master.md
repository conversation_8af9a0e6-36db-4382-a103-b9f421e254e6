# AI-Powered Merchant Underwriting Platform - Master PRD

> **BMad Context Document** | Status: Active | Version: 2.0
>
> **Purpose:** Master product requirements serving as single source of truth for all BMad agents
> **Scope:** Complete platform vision, requirements, and technical specifications **Dependencies:**
> Referenced by all epic and story development workflows

## Quick Reference for BMad Agents

### Core Platform Identity

- **Product:** AI-Powered Merchant Underwriting Platform
- **Architecture:** 100% Self-Hosted, Multi-Tenant SaaS
- **Differentiators:** Universal white-label, explainable AI, outcome-based pricing
- **Target Market:** ISOs, ISVs, MSPs, Payment Processors

### Key Technical Stack

- **Frontend:** Next.js 15 + React 19 + TypeScript + Tailwind + shadcn/ui
- **Backend:** Express 4.21 + Prisma 6.12 + TypeScript + PostgreSQL
- **AI Services:** OpenRouter (conversational) + Local Ollama/Llama (ML models)
- **Integration:** Plugin adapter architecture with mock-first development

### Success Metrics Dashboard

- **Revenue Target:** $4-8M ARR by Year 3
- **Technical KPIs:** >95% decision accuracy, <30s processing time, 99.9% uptime
- **User Experience:** >85% application completion, >90% NPS score
- **Business Impact:** 40% reduction in application abandonment, 50% cost reduction

---

## Section 1: Executive Summary & Vision

### Product Vision

Create the most advanced, self-hosted AI-powered merchant underwriting platform that transforms how
financial services companies onboard and assess merchant applications through conversational AI,
explainable decision-making, and complete white-label customization.

### Mission Statement

Reduce merchant application abandonment by 40%, decrease underwriting operational costs by 50%, and
achieve 80% straight-through processing while maintaining complete data sovereignty through 100%
self-hosted architecture.

### Market Opportunity

- **Global TAM**: $378M (AI-powered underwriting software, 2024)
- **Target SAM**: $40M (US, Canada, UK, EU markets)
- **Market Growth**: 23-28% CAGR (2024-2029)
- **Target Segments**: ISOs, ISVs, MSPs, Payment Processors

### Key Differentiators

1. **100% Self-Hosted Architecture** - Complete data sovereignty and compliance control
2. **Universal White-Label Capabilities** - Full branding across all pricing tiers
3. **True Explainable AI** - Transparent decision-making with regulatory compliance
4. **Outcome-Based Pricing** - Aligned incentives with customer success
5. **Conversational AI Interface** - Natural language application experience
6. **Provider-Agnostic Design** - Customer choice in verification providers

---

## Section 2: User Personas & Journeys

### Primary Personas

#### 1. Merchant Applicant

- **Profile**: Business owner applying for merchant services/payment processing
- **Demographics**: Small to medium business, limited underwriting knowledge
- **Goals**:
  - Complete application quickly and easily
  - Understand requirements and status clearly
  - Minimize paperwork and friction
- **Pain Points**:
  - Complex application processes
  - Unclear requirements
  - Long wait times for decisions
  - Multiple form submissions
- **Success Metrics**: Application completion rate, time to submit, satisfaction score

#### 2. Tenant Admin/Underwriter

- **Profile**: Financial services company using the platform for merchant underwriting
- **Demographics**: Risk managers, underwriters, operations directors
- **Goals**:
  - Efficient underwriting with high accuracy
  - Reduce operational costs
  - Maintain compliance standards
  - Scale processing capacity
- **Pain Points**:
  - Manual review processes
  - Inconsistent decision-making
  - Compliance burden
  - Limited scalability
- **Success Metrics**: Processing speed, decision accuracy, cost reduction, compliance rate

#### 3. System Administrator

- **Profile**: Platform operator managing the multi-tenant system
- **Demographics**: IT professionals, DevOps engineers
- **Goals**:
  - System reliability and performance
  - Efficient tenant onboarding
  - Security and compliance maintenance
  - Cost optimization
- **Pain Points**:
  - Complex multi-tenant management
  - Security vulnerabilities
  - Performance bottlenecks
  - Vendor dependencies
- **Success Metrics**: System uptime, security incidents, tenant onboarding time

### User Journey Maps

#### Merchant Application Journey

1. **Discovery**: Learns about application process
2. **Registration**: Creates account with email verification
3. **Application**: Completes application via AI assistant or traditional forms
4. **Document Upload**: Submits required documents with AI guidance
5. **Review**: Tracks application status and responds to requests
6. **Decision**: Receives approval/decline with clear reasoning
7. **Onboarding**: Proceeds to merchant setup (if approved)

#### Tenant Underwriter Journey

1. **Login**: Accesses secure tenant portal
2. **Dashboard**: Reviews application pipeline and metrics
3. **Case Review**: Analyzes AI-flagged applications
4. **Decision Making**: Approves, declines, or requests more information
5. **Communication**: Sends messages to applicants when needed
6. **Reporting**: Generates compliance and performance reports
7. **Configuration**: Adjusts HITL rules and thresholds

---

## Section 3: Epic Overview for BMad Agents

> **Note for Agents:** Detailed epic specifications are in `/bmad-core/epics/` directory

### Epic Registry Summary

| Epic                                | Priority | Status   | Agent Assignment               |
| ----------------------------------- | -------- | -------- | ------------------------------ |
| Epic 1: AI Application Experience   | P0       | Planning | Frontend Agent + NLP Agent     |
| Epic 2: AI Decision Engine          | P0       | Planning | Backend Agent + ML Agent       |
| Epic 3: Tenant Management Portal    | P1       | Planning | Frontend Agent + Backend Agent |
| Epic 4: Communication & Workflow    | P1       | Planning | Integration Agent              |
| Epic 5: Integration & API Platform  | P1       | Planning | Integration Agent              |
| Epic 6: Billing System              | P0       | Planning | Backend Agent + Finance Agent  |
| Epic 7: Plugin Adapter Architecture | P0       | Planning | Architecture Agent             |
| Epic 8: Mobile & Accessibility      | P2       | Planning | Frontend Agent + UX Agent      |
| Epic 9: System User Management      | P1       | Planning | Backend Agent + Security Agent |

---

## Section 4: Success Metrics & KPIs

### Business Metrics

- **Revenue Growth**: Target $4-8M ARR by Year 3
- **Customer Acquisition**: 35+ customers across all tiers
- **Market Share**: 5-10% of target segments
- **Customer Lifetime Value**: $240K Professional, $590K Enterprise, $865K Platform
- **Customer Acquisition Cost**: <$15K Professional, <$50K Enterprise, <$100K Platform
- **Net Revenue Retention**: >110% indicating expansion revenue

### Product Performance Metrics

- **Decision Accuracy**: >95% true positive rate, <5% false positive rate
- **Processing Speed**: <30 seconds average decision time for 90% of applications
- **API Performance**: 99.9% uptime, <200ms response time
- **Application Completion Rate**: >85% of started applications completed
- **Customer Satisfaction**: >90% NPS score

### Operational Metrics

- **Platform Availability**: 99.9% uptime
- **Security Incidents**: Zero major security breaches
- **Compliance Rate**: 100% regulatory compliance
- **Support Response Time**: <2 hours for critical issues
- **Deployment Success Rate**: >95% successful deployments

### AI/ML Metrics

- **Model Performance**: 10%+ quarterly accuracy improvement
- **Explainability Score**: 100% decisions with reasoning
- **Bias Detection**: <2% demographic bias in decisions
- **Feature Drift**: Automated detection and alerting
- **Training Efficiency**: <24 hours for model retraining

---

## Section 5: Technical Architecture for BMad Implementation

### Self-Hosted Requirements

**Mandate**: 100% self-hosted deployment with no third-party SaaS dependencies

#### Infrastructure Components

- **Container Orchestration**: Docker Swarm or Kubernetes
- **Database**: PostgreSQL 17+ with replication
- **Cache/Sessions**: Redis cluster
- **Object Storage**: MinIO (S3-compatible)
- **Load Balancer**: Traefik or HAProxy
- **Monitoring**: Grafana + Prometheus + Loki stack

#### Hybrid AI Architecture

- **Conversational AI**: OpenRouter API with multi-model support (GPT-4, Claude, etc.)
- **Core ML Models**: Local Ollama + Llama for risk scoring and training
- **AI Cost Management**: Usage tracking, tenant billing, and model selection
- **Vector Database**: Self-hosted Qdrant for embeddings and search

#### Development Technology Stack

- **Backend**: Node.js/TypeScript with Express/Fastify
- **Frontend**: React/Next.js with TypeScript
- **Database ORM**: Prisma
- **Testing**: Jest/Vitest for unit, Playwright for E2E
- **CI/CD**: GitHub Actions with BMad quality gates

---

## Section 6: Implementation Roadmap for BMad

### Phase 1: Foundation & Billing Infrastructure (Months 1-6)

**Goal**: MVP with billing system and basic AI functionality

- **Epic 6**: Complete billing system implementation
- **Epic 9**: System user management and platform administration
- **Epic 7**: Plugin adapter framework with mock implementations
- **Epic 2**: Core AI decision engine with local Llama models
- **Epic 1**: Basic conversational interface using OpenRouter
- Multi-tenant architecture and self-hosted infrastructure

### Phase 2: AI Enhancement & Live Integrations (Months 7-12)

**Goal**: Advanced AI features and gradual real service integration

- **Epic 1**: Advanced conversational AI with model selection
- **Epic 2**: Document processing with OCR and fraud detection
- **Epic 3**: HITL rules engine and shadow mode
- **Epic 7**: First live service integrations
- **Epic 6**: Advanced billing analytics and revenue optimization

### Phase 3: Scale & Enterprise Features (Months 13-18)

**Goal**: Enterprise capabilities and comprehensive integrations

- **Epic 3**: Advanced analytics and white-label customization
- **Epic 5**: Comprehensive API platform and webhooks
- **Epic 8**: Mobile responsiveness and accessibility
- **Epic 7**: Full integration catalog with provider choice

### Success Gates

- **6 Months**: 3+ paying customers, $15K+ MRR
- **12 Months**: 10+ customers, $75K+ MRR, 95%+ accuracy
- **18 Months**: 25+ customers, $200K+ MRR, market recognition

---

## BMad Agent Context Summary

**Current State**: Foundation ready for epic breakdown and story creation **Immediate Priority**:
Epic 2 (AI Decision Engine) → Epic 6 (Billing) → Epic 1 (AI Interface) **Architecture Approach**:
Backend-first with plugin adapter pattern **Quality Requirements**: BMad 85+ score, zero TypeScript
errors, comprehensive testing

---

_Document Version: 2.0_  
_Creation Date: [Current Date]_  
_Last Updated: [Current Date] - Optimized for BMad agent consumption_  
_Ready for Epic Breakdown and Story Development_
