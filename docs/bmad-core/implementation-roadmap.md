# 🛣️ BMad Implementation Roadmap - Dependency-Ordered

> **BMad Navigation Guide** | Status: Active | **Follow This Sequence**
>
> **Purpose:** Never wonder what comes next - this is your implementation GPS **Rule:** Complete
> each phase before starting the next for optimal efficiency

## 🎯 Implementation Sequence (Dependency-Ordered)

### **Phase 1: Foundation Systems** ⚡ _Start Here_

**Goal:** Build the infrastructure everything else depends on **Duration:** 6-8 weeks

#### 1.1 Epic-9: User Management & Authentication

**Why First:** Everything needs users and auth **Duration:** 1-2 weeks

```bash
*agent story-manager
*workflow epic-to-stories epic-9
```

**Delivers:** JWT auth, RBAC, tenant isolation, user sessions

#### 1.2 Epic-7: Plugin Adapter Architecture

**Why Second:** Enables mock-first development for everything else **Duration:** 1-2 weeks

```bash
*agent tech-architect
*workflow epic-to-stories epic-7
```

**Delivers:** Mock services, adapter pattern, external service framework

#### 1.3 Epic-2: AI Decision Engine

**Why Third:** Core business logic, needs auth + plugins **Duration:** 2-3 weeks

```bash
*agent story-manager
*workflow epic-to-stories epic-2
```

**Delivers:** AI models, decision pipeline, OpenRouter integration, explainability

#### 1.4 Epic-6: Billing System

**Why Fourth:** Revenue infrastructure, needs users + AI usage data **Duration:** 2-3 weeks

```bash
*agent finance-specialist
*workflow epic-to-stories epic-6
```

**Delivers:** Usage tracking, subscription management, payment processing

### **Phase 2: User Interfaces** 🎨 _Data-Driven Design_

**Goal:** Build interfaces that display real data from Phase 1 systems **Duration:** 4-6 weeks

#### 2.1 Epic-1: AI Application Experience

**Why Now:** AI engine exists, can build real conversational interface **Duration:** 2-3 weeks

```bash
*agent ui-designer
*workflow epic-to-stories epic-1
```

**Delivers:** Conversational AI interface, document upload, application tracking

#### 2.2 Epic-3: Tenant Management Portal

**Why Second:** Admin interface needs billing data + user management **Duration:** 2-3 weeks

```bash
*agent dashboard-specialist
*workflow epic-to-stories epic-3
```

**Delivers:** Case management, analytics, rules engine, white-label

### **Phase 3: Integration & Scale** 🔗 _Connect Everything_

**Goal:** Connect systems and optimize for scale **Duration:** 4-6 weeks

#### 3.1 Epic-4: Communication & Workflow

**Why First:** Connects user interfaces with backend systems **Duration:** 1-2 weeks

```bash
*agent integration-specialist
*workflow epic-to-stories epic-4
```

**Delivers:** Real-time messaging, notifications, workflow automation

#### 3.2 Epic-5: Integration & API Platform

**Why Second:** External integrations need internal systems working **Duration:** 2-3 weeks

```bash
*agent api-specialist
*workflow epic-to-stories epic-5
```

**Delivers:** Public APIs, webhooks, SDKs, external provider integrations

#### 3.3 Epic-8: Mobile & Accessibility

**Why Last:** Optimization layer for existing working systems **Duration:** 2-3 weeks

```bash
*agent mobile-specialist
*workflow epic-to-stories epic-8
```

**Delivers:** Mobile responsiveness, PWA, accessibility compliance

## 🚀 Current Status & Next Action

### **Current Position:** Phase 1.1 Epic-9 Starting (Architectural Path)

```
✅ DECISION MADE: Option B - Return to Proper Sequence
⏸️  Epic-6 Progress Preserved: Stories 6.1-6.3 Complete (48% done)
🎯 Current Focus: Epic-9 (User Management & Authentication)
📋 Next Action: Execute epic-to-stories workflow for Epic-9

🛣️  PROPER SEQUENCE RESTORED:
Epic-9 → Epic-7 → Epic-2 → Resume Epic-6 (6.4-6.8)
```

### **Immediate Command Sequence**

#### **ACTIVE PATH: Epic-9 Foundation Implementation**

```bash
# Step 1: Break down Epic-9 into actionable stories
*agent story-manager
*workflow epic-to-stories epic-9

# Step 2: Design authentication architecture
*agent tech-architect
*task auth-system-design

# Step 3: Implement user management foundation
*agent backend-specialist
*task implement-user-auth
```

## ⚠️ CURRENT DEVIATION STATUS

### **What Happened**

- **Expected Path:** Epic-9 → Epic-7 → Epic-2 → Epic-6
- **Actual Path:** Jumped directly to Epic-6
- **Progress Made:** Stories 6.1, 6.2, 6.3 complete (subscription, payments, invoicing)
- **Current Work:** Story 6.4 (Tenant Billing Portal) in progress

### **Impact Assessment**

- **👍 Positive:** Substantial billing infrastructure completed
- **⚠️ Risk:** Missing foundational auth, plugins, AI engine
- **💰 Investment:** 30+ story points of billing work completed
- **🔄 Technical Debt:** Some TypeScript issues resolved, more remain

### **Decision Framework**

**Option A: Complete Epic-6 (Momentum Approach)**

- **Pros:** Preserve 48% investment, complete billing system
- **Cons:** Continue architectural debt, harder integration later
- **Time:** 2-3 weeks to finish Epic-6

**Option B: Return to Sequence (Architectural Approach)**

- **Pros:** Proper foundations, easier integration
- **Cons:** Pause Epic-6 progress, context switching
- **Time:** 4-6 weeks for Epic-9/7/2, then return to Epic-6

## 🔄 Auto-Progression Logic

### **Phase Completion Triggers**

Each phase auto-advances when:

- [ ] All epics in phase 100% complete
- [ ] BMad quality score 85+ achieved
- [ ] Integration tests passing
- [ ] Next phase dependencies satisfied

### **Navigation Commands**

```bash
# Check current status
*status                    # Shows current deviation and options
*epic-progress epic-6      # Shows Epic-6 completion status
*dependencies epic-6       # Shows what Epic-6 needs from other epics

# Decision commands
*commit-to-option-a        # Complete Epic-6 first
*commit-to-option-b        # Return to proper sequence
```

## 📊 Dependency Visualization

```mermaid
graph TD
    A[Epic-9: User Management] --> B[Epic-7: Plugin Architecture]
    A --> C[Epic-2: AI Decision Engine]
    A --> D[Epic-6: Billing System]

    B --> C
    B --> E[Epic-1: AI Interface]
    B --> F[Epic-3: Tenant Portal]

    C --> E
    C --> D
    D --> F

    E --> G[Epic-4: Communication]
    F --> G
    G --> H[Epic-5: API Platform]
    H --> I[Epic-8: Mobile & Accessibility]
```

## 🎯 Why This Order Works

### **Foundation First (Phase 1)**

- **No Circular Dependencies** - Each epic only depends on previous ones
- **Mock Development** - Plugin architecture enables rapid iteration
- **Core Value** - AI engine built on solid foundation
- **Revenue Ready** - Billing system tracks everything from day one

### **Interfaces Second (Phase 2)**

- **Real Data** - UIs built against working backend systems
- **Performance Reality** - Interface optimized for actual system behavior
- **User Validation** - Real features to test with users
- **Business Metrics** - Actual usage data flowing through billing

### **Scale Third (Phase 3)**

- **Integration Ready** - All internal systems working first
- **External APIs** - Built on proven internal architecture
- **Mobile Optimization** - Polishing working experiences
- **Production Ready** - System fully integrated and tested

## 🚨 Never Skip Ahead

### **Temptation Points & Why to Resist**

1. **"Let's build the UI first"** → No data to display, will need rebuilding
2. **"Let's integrate external APIs early"** → No internal systems to integrate with
3. **"Let's optimize for mobile now"** → Nothing working to optimize yet

### **Trust the Sequence**

This order eliminates:

- ❌ Circular dependencies
- ❌ Rework and throwaway code
- ❌ Integration surprises
- ❌ Performance unknowns
- ❌ "Which epic next?" decisions

---

## 🎯 NEXT ACTION: Epic-9 User Management

**DECISION CONFIRMED:** Following proper architectural sequence

### **Epic-9: User Management & Authentication**

**Why This is Critical:** Every system needs users, auth, and tenant isolation **Timeline:** 1-2
weeks for complete user management foundation **Dependencies:** None - this is the true foundation

### **Immediate Next Step**

Execute the Epic-9 breakdown using our exemplary BMad workflow:

```bash
*agent story-manager
*workflow epic-to-stories epic-9
```

This will create `/docs/bmad-core/epic-9-stories.md` with detailed implementation steps.

### **Epic-6 Work Preservation**

✅ **Stories 6.1-6.3 Complete** - Subscription management, payments, invoicing infrastructure 📋
**Stories 6.4-6.8 Paused** - Will resume after foundations are established 🔄 **Integration
Benefits** - Epic-6 billing will integrate cleanly with Epic-9 auth

### **Foundation Sequence**

1. **Epic-9** → User management, JWT auth, RBAC, tenant isolation
2. **Epic-7** → Plugin architecture, mock services, adapter patterns
3. **Epic-2** → AI decision engine, OpenRouter integration
4. **Resume Epic-6** → Complete billing portal with proper user context
