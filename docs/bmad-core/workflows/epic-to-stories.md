# BMad Workflow: Epic to Stories Breakdown

> **Workflow Type:** Core | **Purpose:** Convert epics into actionable user stories **Owner:** Story
> Manager Agent | **Quality Gate:** BMad 85+ score required **Execution Time:** 1-3 days per epic |
> **Output:** Complete story backlog

## Workflow Overview

### Objective

Transform large, complex epics into well-defined, implementable user stories with clear acceptance
criteria, technical specifications, and BMad quality compliance.

### Success Criteria

- Epic fully decomposed into 5-12 manageable stories
- All stories meet BMad quality standards (85+ score)
- Clear dependencies and sequencing identified
- Technical specifications complete and feasible
- Agent assignments clear and appropriate

## Workflow Steps

### Phase 1: Epic Analysis (Day 1, AM)

#### Step 1.1: Epic Discovery

```markdown
**Duration:** 1-2 hours **Agent:** Story Manager + Domain Expert **Inputs:** Epic document, PRD
context, user personas **Outputs:** Epic understanding document

**Actions:**

1. Review epic goals and business value
2. Identify affected user personas
3. Analyze technical complexity and dependencies
4. Review acceptance criteria and success metrics
5. Assess scope and boundary conditions
```

#### Step 1.2: User Journey Mapping

```markdown
**Duration:** 1-2 hours  
**Agent:** Story Manager + UX Consultant **Inputs:** User personas, epic scope **Outputs:** User
journey flowchart

**Actions:**

1. Map end-to-end user flows for epic
2. Identify key interaction points and decision nodes
3. Document pain points and improvement opportunities
4. Validate journey against epic goals
5. Highlight areas requiring special attention
```

### Phase 2: Story Extraction (Day 1, PM)

#### Step 2.1: Story Identification

```markdown
**Duration:** 2-3 hours **Agent:** Story Manager **Inputs:** Epic analysis, user journeys
**Outputs:** Initial story list

**Process:**

1. Break user journey into logical story chunks
2. Ensure each story delivers independent value
3. Apply INVEST criteria (Independent, Negotiable, Valuable, Estimable, Small, Testable)
4. Create story titles and basic descriptions
5. Validate story scope and boundaries
```

#### Step 2.2: Story Prioritization

```markdown
**Duration:** 1 hour **Agent:** Story Manager + Product Owner **Inputs:** Story list, business
priorities **Outputs:** Prioritized story backlog

**Criteria:**

- Business value and impact
- Technical dependencies and risk
- User experience criticality
- Implementation complexity
- Resource availability
```

### Phase 3: Story Specification (Day 2)

#### Step 3.1: Acceptance Criteria Definition

```markdown
**Duration:** 3-4 hours **Agent:** Story Manager + Domain Expert **Per Story Process:**

1. **Functional Requirements**
   - Define specific, testable acceptance criteria
   - Include edge cases and error conditions
   - Specify validation rules and constraints
   - Detail expected system behavior

2. **Non-Functional Requirements**
   - Performance expectations (response times, throughput)
   - Security requirements (authentication, authorization)
   - Accessibility compliance (WCAG 2.1 AA)
   - Mobile responsiveness requirements

3. **Integration Requirements**
   - External service dependencies
   - Data exchange specifications
   - Error handling and recovery
   - Monitoring and logging needs
```

#### Step 3.2: Technical Specification

```markdown
**Duration:** 2-3 hours **Agent:** Story Manager + Tech Architect **Per Story Output:**

1. **API Design**
   - Endpoint specifications (REST/GraphQL)
   - Request/response schemas
   - Authentication and authorization
   - Rate limiting and caching

2. **Data Models**
   - Database schema requirements
   - Data validation rules
   - Relationships and constraints
   - Migration considerations

3. **Integration Points**
   - External service contracts
   - Plugin adapter requirements
   - Real-time communication needs
   - Third-party API specifications
```

### Phase 4: Quality Validation (Day 3)

#### Step 4.1: BMad Quality Assessment

```markdown
**Duration:** 2 hours **Agent:** Story Manager + Quality Validator **Quality Checklist:**

- [ ] Story follows proper user story format
- [ ] Acceptance criteria are specific and testable
- [ ] Technical specifications are complete and feasible
- [ ] Dependencies are clearly identified
- [ ] Effort estimation is reasonable and justified
- [ ] BMad quality score 85+ achieved
- [ ] Zero ambiguous or unclear requirements
```

#### Step 4.2: Dependency Analysis

```markdown
**Duration:** 1-2 hours **Agent:** Story Manager + Tech Architect **Dependency Mapping:**

1. **Technical Dependencies**
   - Infrastructure requirements
   - Service availability needs
   - Data dependencies
   - API contract dependencies

2. **Story Sequencing**
   - Required completion order
   - Parallel development opportunities
   - Critical path identification
   - Risk mitigation strategies
```

### Phase 5: Agent Assignment & Handoff (Day 3)

#### Step 5.1: Agent Allocation

```markdown
**Duration:** 1 hour **Agent:** Story Manager + Team Leads **Assignment Criteria:**

- Technical expertise alignment
- Agent workload and availability
- Cross-functional collaboration needs
- Knowledge transfer requirements
```

#### Step 5.2: Handoff Package Creation

```markdown
**Duration:** 1 hour **Agent:** Story Manager **Deliverables:**

1. **Complete Story Backlog** - All stories with specifications
2. **Dependency Map** - Visual representation of story relationships
3. **Agent Assignment Matrix** - Clear ownership and responsibilities
4. **Success Metrics** - KPIs and acceptance criteria
5. **Risk Assessment** - Potential issues and mitigation strategies
```

## Workflow Templates

### Story Template

````markdown
## Story [EPIC-X.Y]: [Story Title]

**As a** [user persona]  
**I want** [specific functionality]  
**So that** [business value]

### Acceptance Criteria

- [ ] [Specific, testable requirement]
- [ ] [Performance requirement with metrics]
- [ ] [Security/compliance requirement]
- [ ] [Error handling requirement]
- [ ] [Integration requirement]

### Technical Specifications

**API Endpoints:**

- `GET /api/v1/[resource]` - [Description]
- `POST /api/v1/[resource]` - [Description]

**Data Models:**

```prisma
model [EntityName] {
  // Schema definition
}
```
````

**Integration Points:**

- [External service] via [plugin adapter]
- [Real-time updates] via [WebSocket/SSE]

### BMad Implementation Notes

- **Agent Assignment:** [Primary agent + collaborators]
- **Dependencies:** [Required prior stories]
- **Effort Estimate:** [Hours/days with justification]
- **Risk Level:** [Low/Medium/High with mitigation]

### Definition of Done

- [ ] Functional requirements implemented
- [ ] All acceptance criteria verified
- [ ] BMad quality score 85+ achieved
- [ ] Test coverage 90%+ achieved
- [ ] Documentation updated

````

## Quality Gates

### Story Quality Checklist
```markdown
**BMad Quality Requirements:**
- [ ] Clear user value proposition
- [ ] Specific, testable acceptance criteria
- [ ] Complete technical specifications
- [ ] Realistic effort estimation
- [ ] Proper dependency identification
- [ ] Agent assignment appropriate
- [ ] Risk assessment complete
- [ ] Quality score 85+ achieved

**Technical Quality Requirements:**
- [ ] API design follows REST/OpenAPI standards
- [ ] Data models are normalized and efficient
- [ ] Security requirements clearly specified
- [ ] Performance expectations quantified
- [ ] Error handling strategies defined
- [ ] Integration contracts complete
````

## Success Metrics

### Workflow Efficiency

- **Epic Breakdown Time:** <3 days per epic
- **Story Quality:** 100% achieving BMad 85+ score
- **Estimation Accuracy:** <20% variance in story delivery
- **Dependency Precision:** Zero blocking dependencies discovered during development

### Output Quality

- **Story Completeness:** 100% stories have all required sections
- **Technical Feasibility:** 95%+ stories deliverable as specified
- **User Value:** All stories traceable to user personas and journeys
- **Agent Satisfaction:** 4.5/5 rating from implementing agents

## Workflow Commands

```bash
# Execute complete workflow
*workflow epic-to-stories [epic-id]

# Execute individual phases
*task epic-analysis [epic-id]
*task story-extraction [epic-id]
*task story-specification [epic-id]
*task quality-validation [epic-id]
*task agent-handoff [epic-id]

# Quality validation
*checklist story-quality [story-id]
*checklist bmad-compliance [story-id]
```

---

**Next Recommended Action:** `*workflow epic-to-stories epic-2` **Current Priority:** Epic-2 (AI
Decision Engine) breakdown
