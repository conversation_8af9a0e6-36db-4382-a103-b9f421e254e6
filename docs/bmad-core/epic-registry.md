# Epic Registry - AI Underwriting Platform

> **BMad Master Registry** | Status: Active | Last Updated: [Current Date]
>
> **Purpose:** Master tracking and coordination document for all epics **Scope:** Complete epic
> lifecycle management for BMad agents **Owner:** BMad Orchestrator

## Epic Status Dashboard

| Epic ID | Name                        | Priority | Status                      | Agent Assignment    | Stories | Completion |
| ------- | --------------------------- | -------- | --------------------------- | ------------------- | ------- | ---------- |
| Epic-9A | Authentication Foundation   | P0       | ✅ Ready for Implementation | Backend Agent       | 3       | 0%         |
| Epic-7  | Plugin Adapter Architecture | P0       | 📋 Planning                 | Architecture        | 4       | 0%         |
| Epic-2  | AI Decision Engine          | P0       | 📋 Planning                 | Backend + ML        | 5       | 0%         |
| Epic-6  | Billing System              | P0       | 📋 Planning                 | Backend + Finance   | 8       | 0%         |
| Epic-1  | AI Application Experience   | P1       | 📋 Planning                 | Frontend + NLP      | 6       | 0%         |
| Epic-3  | Tenant Management Portal    | P1       | 📋 Planning                 | Frontend + Backend  | 7       | 0%         |
| Epic-4  | Communication & Workflow    | P1       | 📋 Planning                 | Integration         | 4       | 0%         |
| Epic-5  | Integration & API Platform  | P1       | 📋 Planning                 | Integration         | 3       | 0%         |
| Epic-8  | Mobile & Accessibility      | P2       | 📋 Planning                 | Frontend + UX       | 7       | 0%         |
| Epic-9B | Platform Administration     | P2       | 📋 Phase 2                  | Backend + Analytics | 5       | 0%         |

## Epic Execution Sequence

### Phase 1: Foundation (Months 1-6)

**Current Phase** ✅

```mermaid
graph LR
    A[Epic-2: AI Decision Engine] --> B[Epic-6: Billing System]
    B --> C[Epic-7: Plugin Architecture]
    C --> D[Epic-9: User Management]
    D --> E[Epic-1: Basic AI Interface]
```

### Phase 2: Enhancement (Months 7-12)

```mermaid
graph LR
    A[Epic-1: Advanced AI] --> B[Epic-3: Tenant Portal]
    B --> C[Epic-4: Communication]
    C --> D[Epic-5: API Platform]
```

### Phase 3: Scale (Months 13-18)

```mermaid
graph LR
    A[Epic-8: Mobile/Accessibility] --> B[Epic Integration Testing]
    B --> C[Production Readiness]
```

## Epic Dependencies

### Critical Path Dependencies

- **Epic-2** → **Epic-1** (AI engine must exist before interface)
- **Epic-6** → **Epic-3** (Billing data needed for tenant analytics)
- **Epic-7** → **Epic-2** (Plugin architecture enables AI integrations)
- **Epic-9** → **Epic-3** (User management enables tenant portal)

### Integration Dependencies

- **Epic-1** ↔ **Epic-3** (Shared components and state management)
- **Epic-4** → **Epic-1,3** (Communication layer serves both interfaces)
- **Epic-5** → **All** (API platform serves all functionality)

## Agent Assignment Strategy

### Primary Agent Teams

- **🤖 AI/ML Team**: Epic-2 (AI Decision Engine)
- **💰 Finance Team**: Epic-6 (Billing System)
- **🏗️ Architecture Team**: Epic-7 (Plugin Architecture)
- **🎨 Frontend Team**: Epic-1, Epic-3, Epic-8
- **🔐 Security Team**: Epic-9 (User Management)
- **🔗 Integration Team**: Epic-4, Epic-5

### Agent Handoff Points

1. **Epic-2 → Epic-1**: AI engine APIs ready for frontend integration
2. **Epic-6 → Epic-3**: Billing data models available for dashboard
3. **Epic-7 → Epic-2**: Plugin framework ready for AI service integration
4. **Epic-9 → Epic-3**: User management APIs ready for tenant portal

## Epic Success Criteria

### Definition of Epic Completion

- [ ] All user stories completed and tested
- [ ] BMad quality score 85+ achieved
- [ ] Integration tests passing
- [ ] Documentation updated
- [ ] Agent handoff completed (if applicable)

### Epic-Level KPIs

| Metric                 | Target  | Current | Status         |
| ---------------------- | ------- | ------- | -------------- |
| Story Completion Rate  | 100%    | 0%      | 🔴 Not Started |
| BMad Quality Score     | 85+     | N/A     | 🔴 Not Started |
| Test Coverage          | 90%+    | N/A     | 🔴 Not Started |
| Performance Benchmarks | All Met | N/A     | 🔴 Not Started |

## Quick Actions for BMad Agents

### For Story Manager Agent

- **Next Action**: Break down Epic-2 (AI Decision Engine) into actionable stories
- **Priority**: Focus on core ML model infrastructure first
- **Dependencies**: Review plugin architecture requirements

### For Tech Architect Agent

- **Next Action**: Design Epic-7 (Plugin Architecture) foundation
- **Priority**: Mock-first development strategy implementation
- **Dependencies**: Coordinate with Epic-2 AI service requirements

### For Integration Agent

- **Next Action**: Plan Epic-4 communication layer architecture
- **Priority**: Real-time notification system design
- **Dependencies**: Await user management and AI engine specifications

## Status Indicators

- 🔴 **Not Started** - Epic not yet initiated
- 🟡 **Planning** - Stories being defined and planned
- 🟢 **In Progress** - Active development underway
- 🔵 **Review** - Epic completed, undergoing review
- ✅ **Complete** - Epic delivered and verified

## BMad Command Reference

### Epic Management Commands

```bash
*agent story-manager     # Transform to break down epics
*workflow epic-to-stories # Execute epic breakdown workflow
*plan epic-2            # Create detailed plan for Epic-2
*status epic-1          # Check specific epic status
*checklist epic-complete # Run epic completion checklist
```

---

**Next Recommended Action**: `*agent story-manager` to begin Epic-2 breakdown **Current Focus**:
Epic-2 (AI Decision Engine) - Foundation Priority #1
