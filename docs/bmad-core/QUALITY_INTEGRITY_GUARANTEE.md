# 🚨 QUALITY INTEGRITY GUARANTEE 🚨

**MANDATORY INCLUSION:** This guarantee MUST be included at the top of ALL epics, stories, and technical documents.

## ABSOLUTE PROHIBITION AGAINST QUALITY SYSTEM GAMING

**Claude Code SHALL NEVER:**
- ❌ Remove files to avoid fixing TypeScript/ESLint errors
- ❌ Disable validation rules to pass quality checks
- ❌ Manipulate test results or coverage metrics
- ❌ Make superficial changes to satisfy automated checkers
- ❌ Claim quality achievements without genuine code improvements
- ❌ Circumvent BMad validation through technical workarounds
- ❌ Delete build artifacts to hide validation failures
- ❌ Comment out problematic code instead of fixing it
- ❌ Any form of metric manipulation or validation system gaming

## LEGITIMATE QUALITY IMPROVEMENT REQUIREMENTS

**ALL quality improvements MUST be achieved through:**
- ✅ Genuine TypeScript error resolution with proper types
- ✅ Authentic ESLint warning fixes with proper code refactoring
- ✅ Real architectural improvements and design pattern implementation
- ✅ Proper error handling and type safety implementation
- ✅ Legitimate code optimization and maintainability improvements
- ✅ Honest testing and coverage improvements through actual test writing

## COMMITMENT TO AUTHENTIC QUALITY

Quality scores must reflect **ACTUAL** code quality, not validation system manipulation. Every BMad score, TypeScript compliance metric, and ESLint result must represent genuine engineering excellence.

**VIOLATION CONSEQUENCES:** Any attempt to game quality systems is a fundamental breach of software engineering integrity and is absolutely unacceptable.

---

**This guarantee applies to ALL BMad agents, epics, stories, and development work.**