# AI-Powered Merchant Underwriting Platform - Entity Relationship Model

## Conceptual Database Design

**BMad Database Design Workflow - Phase 2**

## Entity Relationship Overview

This document presents the comprehensive conceptual model for the AI-powered merchant underwriting
platform, addressing multi-tenancy, AI integration, workflow automation, and enterprise-scale
requirements.

## Core Entity Domains

### 1. Multi-Tenancy & Organization Domain

```mermaid
erDiagram
    TENANT {
        cuid id PK
        string name
        string slug UK
        string domain UK
        TenantStatus status
        SubscriptionTier subscription_tier
        json branding
        string custom_domain
        int ai_quota_monthly
        int ai_usage_current_month
        boolean hitl_rules_enabled
        json features
        json settings
        datetime created_at
        datetime updated_at
    }

    TENANT_CONFIGURATION {
        cuid id PK
        cuid tenant_id FK
        string config_key
        json config_value
        datetime created_at
        datetime updated_at
    }

    SUBSCRIPTION_TIER {
        string tier PK
        string display_name
        int ai_quota_limit
        json features_included
        decimal monthly_cost
        boolean is_active
    }

    TENANT ||--o{ TENANT_CONFIGURATION : configures
    TENANT ||--|| SUBSCRIPTION_TIER : has
```

### 2. User Management & Authentication Domain

```mermaid
erDiagram
    USER {
        cuid id PK
        string email UK
        string name
        string password_hash
        cuid tenant_id FK
        UserRole role
        json permissions
        boolean email_verified
        datetime email_verified_at
        boolean mfa_enabled
        string mfa_secret
        string avatar
        string timezone
        string locale
        datetime last_login_at
        datetime created_at
        datetime updated_at
    }

    USER_SESSION {
        cuid id PK
        cuid user_id FK
        string session_token UK
        string refresh_token UK
        string device
        string ip_address
        string user_agent
        datetime expires_at
        datetime last_active_at
        datetime created_at
    }

    ROLE_PERMISSION {
        cuid id PK
        UserRole role
        string permission
        string resource
        json conditions
    }

    USER ||--o{ USER_SESSION : has
    USER ||--o{ ROLE_PERMISSION : assigned
```

### 3. Core Business Domain - Applications

```mermaid
erDiagram
    APPLICATION {
        cuid id PK
        string application_number UK
        cuid tenant_id FK
        ApplicationStatus status
        ApplicationStage stage
        Priority priority
        string business_name
        BusinessType business_type
        Industry industry
        string business_email
        string business_phone
        string business_website
        decimal monthly_revenue
        int monthly_transactions
        decimal average_ticket
        decimal requested_amount
        json business_address
        json mailing_address
        float risk_score
        RiskLevel risk_level
        Decision final_decision
        string decision_reason
        datetime decision_made_at
        cuid decision_made_by FK
        cuid assigned_to FK
        string source
        string referral_code
        json metadata
        datetime created_at
        datetime updated_at
        datetime submitted_at
        datetime completed_at
    }

    APPLICATION_DATA {
        cuid id PK
        cuid application_id FK
        string field_name
        string field_type
        json value
        DataSource source
        float confidence
        datetime created_at
        datetime updated_at
    }

    APPLICATION_STATUS_HISTORY {
        cuid id PK
        cuid application_id FK
        ApplicationStatus from_status
        ApplicationStatus to_status
        string reason
        string notes
        cuid changed_by FK
        datetime changed_at
    }

    APPLICATION ||--o{ APPLICATION_DATA : contains
    APPLICATION ||--o{ APPLICATION_STATUS_HISTORY : tracks
```

### 4. AI & Conversation Management Domain

```mermaid
erDiagram
    CONVERSATION {
        cuid id PK
        cuid application_id FK
        cuid tenant_id FK
        string title
        string language
        ConversationInterface interface
        string ai_provider
        string ai_model
        string system_prompt
        ConversationStatus status
        json context
        string summary
        int total_tokens
        decimal total_cost
        string session_id
        datetime last_active_at
        datetime created_at
        datetime updated_at
        datetime completed_at
    }

    CONVERSATION_MESSAGE {
        cuid id PK
        cuid conversation_id FK
        MessageRole role
        string content
        json metadata
        string model
        int token_count
        decimal cost
        cuid user_id FK
        float confidence
        int response_time
        datetime created_at
    }

    AI_MODEL_USAGE {
        cuid id PK
        cuid tenant_id FK
        string provider
        string model
        string endpoint
        int input_tokens
        int output_tokens
        int total_tokens
        decimal cost
        cuid conversation_id FK
        cuid application_id FK
        cuid user_id FK
        int response_time
        boolean successful
        string error_message
        datetime created_at
    }

    AI_MODEL_PERFORMANCE {
        cuid id PK
        string provider
        string model
        float avg_response_time
        float success_rate
        decimal avg_cost_per_token
        float accuracy_score
        json performance_metrics
        datetime period_start
        datetime period_end
        datetime created_at
    }

    CONVERSATION ||--o{ CONVERSATION_MESSAGE : contains
    CONVERSATION ||--o{ AI_MODEL_USAGE : tracks
    AI_MODEL_USAGE ||--|| AI_MODEL_PERFORMANCE : aggregates_to
```

### 5. Document Management Domain

```mermaid
erDiagram
    DOCUMENT {
        cuid id PK
        cuid application_id FK
        string filename
        string original_name
        int file_size
        string mime_type
        string file_hash
        string storage_path
        DocumentType document_type
        DocumentCategory category
        DocumentStatus status
        ProcessingStatus ocr_status
        string ocr_text
        float ocr_confidence
        float fraud_score
        float quality_score
        json extracted_data
        ReviewStatus review_status
        cuid reviewed_by FK
        datetime reviewed_at
        string review_notes
        json metadata
        string[] tags
        datetime uploaded_at
        datetime updated_at
    }

    DOCUMENT_ANNOTATION {
        cuid id PK
        cuid document_id FK
        cuid user_id FK
        AnnotationType type
        string content
        json coordinates
        datetime created_at
        datetime updated_at
    }

    DOCUMENT_VERSION {
        cuid id PK
        cuid document_id FK
        int version_number
        string storage_path
        string change_reason
        cuid created_by FK
        datetime created_at
    }

    DOCUMENT ||--o{ DOCUMENT_ANNOTATION : has
    DOCUMENT ||--o{ DOCUMENT_VERSION : versioned_as
```

### 6. Risk Assessment & Decision Engine Domain

```mermaid
erDiagram
    RISK_ASSESSMENT {
        cuid id PK
        cuid application_id FK
        string version
        string model_name
        float overall_score
        RiskLevel risk_level
        float credit_score
        float business_score
        float document_score
        float behavior_score
        Decision recommendation
        float confidence
        string reasoning
        json factors
        json features
        boolean human_reviewed
        cuid reviewed_by FK
        datetime reviewed_at
        datetime created_at
    }

    DECISION_FACTOR {
        cuid id PK
        cuid risk_assessment_id FK
        string factor_name
        FactorType factor_type
        json value
        float weight
        FactorImpact impact
        string explanation
        float confidence
    }

    SHADOW_MODE_EXECUTION {
        cuid id PK
        cuid application_id FK
        json shadow_decision
        json live_decision
        float accuracy
        boolean is_correct
        string variance_reason
        datetime executed_at
        datetime created_at
    }

    ML_MODEL {
        cuid id PK
        string name
        string version
        string model_type
        json configuration
        float accuracy
        float precision
        float recall
        json performance_metrics
        boolean is_active
        datetime trained_at
        datetime deployed_at
        datetime created_at
    }

    RISK_ASSESSMENT ||--o{ DECISION_FACTOR : contains
    RISK_ASSESSMENT ||--|| SHADOW_MODE_EXECUTION : validates
    RISK_ASSESSMENT ||--|| ML_MODEL : uses
```

### 7. External Integration & Plugin Domain

```mermaid
erDiagram
    PLUGIN {
        cuid id PK
        string name UK
        string display_name
        string description
        PluginCategory category
        string provider
        string version
        json config_schema
        boolean is_active
        boolean is_mockable
        string base_url
        AuthType auth_type
        datetime created_at
        datetime updated_at
    }

    TENANT_PLUGIN {
        cuid id PK
        cuid tenant_id FK
        cuid plugin_id FK
        json config
        boolean is_enabled
        boolean use_mock
        int usage_limit
        int current_usage
        datetime created_at
        datetime updated_at
    }

    EXTERNAL_PROVIDER_CALL {
        cuid id PK
        cuid tenant_id FK
        cuid plugin_id FK
        cuid application_id FK
        string endpoint
        string method
        json request_body
        json request_headers
        int status_code
        json response_body
        json response_headers
        int response_time
        decimal cost
        boolean successful
        string error_message
        int retry_count
        int max_retries
        datetime created_at
    }

    PROVIDER_PERFORMANCE {
        cuid id PK
        cuid plugin_id FK
        cuid tenant_id FK
        float avg_response_time
        float success_rate
        decimal cost_per_request
        float uptime_percent
        int sla_violations
        datetime period_start
        datetime period_end
        datetime created_at
    }

    PLUGIN ||--o{ TENANT_PLUGIN : configured_for
    TENANT_PLUGIN ||--o{ EXTERNAL_PROVIDER_CALL : generates
    PLUGIN ||--o{ PROVIDER_PERFORMANCE : measured_by
```

### 8. Verification Services Domain

```mermaid
erDiagram
    KYC_VERIFICATION {
        cuid id PK
        cuid application_id FK
        string provider
        string verification_id
        string document_type
        string document_number
        string first_name
        string last_name
        datetime date_of_birth
        json address
        VerificationStatus status
        VerificationResult overall_result
        VerificationResult face_match
        VerificationResult document_auth
        float confidence
        float fraud_score
        json details
        json webhook_data
        datetime processed_at
        datetime created_at
        datetime updated_at
    }

    CREDIT_REPORT {
        cuid id PK
        cuid application_id FK
        string provider
        string report_id
        CreditReportType report_type
        int credit_score
        string score_model
        string score_range
        string business_name
        json business_address
        int years_in_business
        int total_credit_lines
        decimal total_credit_limit
        decimal total_balance
        float utilization_rate
        json payment_history
        int delinquencies
        int bankruptcies
        int judgments
        int liens
        json raw_report
        datetime generated_at
        datetime created_at
    }

    BANK_VERIFICATION {
        cuid id PK
        cuid application_id FK
        string provider
        string account_id
        BankAccountType account_type
        string account_number
        string routing_number
        string bank_name
        VerificationStatus status
        boolean balance_verified
        boolean ownership_verified
        decimal current_balance
        decimal available_balance
        decimal average_balance
        decimal minimum_balance
        decimal monthly_deposits
        decimal monthly_withdrawals
        int transaction_count
        int nsf_count
        datetime analysis_start_date
        datetime analysis_end_date
        json raw_data
        datetime verified_at
        datetime created_at
    }

    KYC_VERIFICATION ||--|| APPLICATION : verifies
    CREDIT_REPORT ||--|| APPLICATION : reports_on
    BANK_VERIFICATION ||--|| APPLICATION : validates
```

### 9. Workflow & Task Management Domain

```mermaid
erDiagram
    WORKFLOW_DEFINITION {
        cuid id PK
        cuid tenant_id FK
        string name
        string description
        json steps
        json triggers
        json conditions
        boolean is_active
        int version
        datetime created_at
        datetime updated_at
    }

    WORKFLOW_EXECUTION {
        cuid id PK
        cuid workflow_definition_id FK
        cuid application_id FK
        WorkflowStatus status
        json context
        datetime started_at
        datetime completed_at
        datetime created_at
    }

    WORKFLOW_STEP_EXECUTION {
        cuid id PK
        cuid workflow_execution_id FK
        string step_name
        StepStatus status
        json input_data
        json output_data
        string error_message
        datetime started_at
        datetime completed_at
        datetime created_at
    }

    TASK {
        cuid id PK
        cuid application_id FK
        cuid workflow_step_execution_id FK
        string title
        string description
        TaskType type
        Priority priority
        TaskStatus status
        cuid assigned_to FK
        datetime assigned_at
        datetime due_date
        float estimated_hours
        float actual_hours
        string workflow_step
        string[] dependencies
        cuid completed_by FK
        datetime completed_at
        json result
        string notes
        datetime created_at
        datetime updated_at
    }

    WORKFLOW_DEFINITION ||--o{ WORKFLOW_EXECUTION : executes
    WORKFLOW_EXECUTION ||--o{ WORKFLOW_STEP_EXECUTION : contains
    WORKFLOW_STEP_EXECUTION ||--o{ TASK : generates
```

### 10. Communication & Messaging Domain

```mermaid
erDiagram
    MESSAGE {
        cuid id PK
        cuid application_id FK
        string subject
        string content
        MessageType message_type
        MessageChannel channel
        cuid sender_id FK
        cuid recipient_id FK
        MessageStatus status
        datetime delivered_at
        datetime read_at
        json attachments
        cuid thread_id FK
        cuid reply_to_id FK
        datetime created_at
    }

    NOTIFICATION_TEMPLATE {
        cuid id PK
        cuid tenant_id FK
        string name
        string template
        json variables
        MessageType message_type
        boolean is_active
        datetime created_at
        datetime updated_at
    }

    NOTIFICATION_QUEUE {
        cuid id PK
        cuid template_id FK
        cuid recipient_id FK
        json template_data
        Priority priority
        NotificationStatus status
        int retry_count
        int max_retries
        datetime scheduled_at
        datetime sent_at
        datetime created_at
    }

    COMMUNICATION_PREFERENCE {
        cuid id PK
        cuid user_id FK
        MessageType message_type
        boolean is_enabled
        json preferences
        datetime created_at
        datetime updated_at
    }

    MESSAGE ||--|| NOTIFICATION_TEMPLATE : uses
    NOTIFICATION_TEMPLATE ||--o{ NOTIFICATION_QUEUE : generates
    USER ||--o{ COMMUNICATION_PREFERENCE : has
```

### 11. Analytics & Reporting Domain

```mermaid
erDiagram
    ANALYTICS_EVENT {
        cuid id PK
        cuid tenant_id FK
        string event_type
        string event_name
        string category
        cuid user_id FK
        cuid application_id FK
        cuid session_id FK
        json properties
        string ip_address
        string user_agent
        string referer
        datetime timestamp
    }

    TENANT_METRICS {
        cuid id PK
        cuid tenant_id FK
        date metric_date
        int total_applications
        int approved_applications
        int rejected_applications
        float approval_rate
        decimal total_ai_cost
        int total_ai_tokens
        float avg_processing_time
        float avg_risk_score
        datetime created_at
    }

    APPLICATION_METRICS {
        cuid id PK
        cuid application_id FK
        int conversation_messages
        decimal ai_cost
        int ai_tokens
        int documents_uploaded
        int external_api_calls
        decimal external_api_cost
        float processing_time_hours
        datetime created_at
    }

    SYSTEM_METRICS {
        cuid id PK
        string metric_name
        string metric_type
        float value
        json metadata
        datetime timestamp
    }

    ANALYTICS_EVENT ||--|| TENANT_METRICS : aggregates_to
    APPLICATION ||--|| APPLICATION_METRICS : summarized_in
    SYSTEM_METRICS ||--|| TENANT_METRICS : contributes_to
```

### 12. Compliance & Audit Domain

```mermaid
erDiagram
    AUDIT_LOG {
        cuid id PK
        cuid tenant_id FK
        string action
        string entity_type
        cuid entity_id FK
        cuid user_id FK
        string ip_address
        string user_agent
        json old_values
        json new_values
        json metadata
        string reason
        AuditRiskLevel risk_level
        datetime timestamp
    }

    COMPLIANCE_EVENT {
        cuid id PK
        cuid tenant_id FK
        ComplianceEventType event_type
        string regulation
        string description
        AuditRiskLevel risk_level
        string impact
        ComplianceStatus status
        cuid assigned_to FK
        string resolution
        datetime resolved_at
        cuid application_id FK
        cuid user_id FK
        cuid document_id FK
        json metadata
        datetime created_at
        datetime updated_at
    }

    DATA_SUBJECT_REQUEST {
        cuid id PK
        cuid tenant_id FK
        DataRequestType request_type
        string subject_email
        RequestStatus status
        json request_details
        json response_data
        datetime submitted_at
        datetime completed_at
        string completion_notes
        datetime created_at
    }

    RETENTION_POLICY {
        cuid id PK
        cuid tenant_id FK
        string entity_type
        int retention_days
        boolean auto_delete
        json deletion_criteria
        boolean is_active
        datetime created_at
        datetime updated_at
    }

    AUDIT_LOG ||--|| COMPLIANCE_EVENT : triggers
    COMPLIANCE_EVENT ||--|| DATA_SUBJECT_REQUEST : generates
    RETENTION_POLICY ||--|| AUDIT_LOG : governs
```

## Key Relationships and Constraints

### 1. Multi-Tenant Data Isolation

- All business entities include `tenant_id` for strict isolation
- Cascading deletes ensure data consistency
- Cross-tenant queries prohibited at application level

### 2. Application Workflow Dependencies

```
Tenant → Application → Documents/Conversations/Risk Assessments
Application → Tasks → Workflow Executions
Application → Verifications → External Provider Calls
```

### 3. AI Cost Management Flow

```
Conversation → Messages → AI Model Usage → Tenant Billing
AI Model Usage → Performance Metrics → Cost Optimization
```

### 4. Audit and Compliance Chain

```
User Actions → Audit Logs → Compliance Events → Data Subject Requests
Entity Changes → Retention Policies → Automated Cleanup
```

### 5. Real-time Data Flow

```
Application Events → Analytics Events → Real-time Metrics
Workflow Events → Notifications → Communication Channels
```

## Data Integrity Constraints

### 1. Business Rules

- Application numbers must be unique per tenant
- AI usage cannot exceed tenant quotas
- Risk assessments require valid ML model references
- Document uploads must include file integrity checks

### 2. Temporal Constraints

- Status history must maintain chronological order
- Workflow executions cannot start before creation
- Audit logs must be immutable after creation
- Retention policies must respect regulatory minimums

### 3. Financial Constraints

- All cost calculations must maintain precision
- AI usage costs must be non-negative
- Billing aggregations must be consistent
- Currency conversion must be auditable

### 4. Security Constraints

- User access must respect tenant boundaries
- Sensitive data requires encryption at rest
- Audit trails must be tamper-evident
- Session tokens must have expiration limits

## Performance Considerations

### 1. Indexing Strategy

- Composite indexes on (tenant_id, status, created_at) for most entities
- Full-text search indexes on application and document content
- Partial indexes for active/pending records only
- Covering indexes for frequently accessed read-only queries

### 2. Partitioning Strategy

- Time-based partitioning for audit logs and analytics events
- Tenant-based partitioning for large-scale deployments
- Archive partitions for historical data retention

### 3. Caching Strategy

- Application-level caching for user sessions and preferences
- Query result caching for expensive analytics operations
- Real-time metrics caching with Redis

### 4. Scaling Considerations

- Read replicas for analytics and reporting workloads
- Connection pooling for high-concurrency scenarios
- Horizontal sharding by tenant for extreme scale

This conceptual model provides the foundation for a comprehensive, scalable, and compliant database
design that supports all aspects of the AI-powered merchant underwriting platform while maintaining
performance, security, and regulatory compliance.
