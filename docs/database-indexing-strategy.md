# Database Indexing Strategy & Physical Optimization

**BMad Database Design Workflow - Phase 4**

## Executive Summary

This document outlines the comprehensive indexing strategy for the AI-powered merchant underwriting
platform, designed to support high-performance operations at enterprise scale while maintaining
efficient query execution and optimal resource utilization.

## Core Indexing Principles

### 1. Multi-Tenant Performance Isolation

- **Tenant-First Indexing**: All composite indexes start with `tenant_id` for optimal data isolation
- **Partition Alignment**: Indexes aligned with tenant-based partitioning strategy
- **Cross-Tenant Prevention**: Indexes designed to prevent accidental cross-tenant queries

### 2. Workload-Specific Optimization

- **OLTP Optimization**: Fast lookups for real-time application processing
- **Analytics Support**: Efficient aggregation for reporting and analytics
- **AI Workload Support**: Optimized indexes for conversation and ML data access

## Primary Index Strategy

### 1. Core Business Entity Indexes

#### Applications (High-Traffic Primary Entity)

```sql
-- Primary access patterns
CREATE INDEX idx_applications_tenant_status_created ON applications(tenant_id, status, created_at DESC);
CREATE INDEX idx_applications_tenant_stage_priority ON applications(tenant_id, stage, priority);
CREATE INDEX idx_applications_assigned_user ON applications(assigned_to, status) WHERE assigned_to IS NOT NULL;
CREATE INDEX idx_applications_risk_level ON applications(tenant_id, risk_level, final_decision);
CREATE INDEX idx_applications_business_search ON applications USING gin(to_tsvector('english', business_name));

-- Analytics and reporting
CREATE INDEX idx_applications_decision_analytics ON applications(tenant_id, final_decision, decision_made_at)
  WHERE final_decision IS NOT NULL;
CREATE INDEX idx_applications_time_series ON applications(tenant_id, DATE(created_at), status);
```

#### Users (Authentication & Authorization)

```sql
-- Authentication
CREATE UNIQUE INDEX idx_users_email_tenant ON users(email, tenant_id);
CREATE INDEX idx_users_session_lookup ON users(id, email_verified, tenant_id);

-- Authorization and user management
CREATE INDEX idx_users_tenant_role ON users(tenant_id, role, created_at DESC);
CREATE INDEX idx_users_last_login ON users(tenant_id, last_login_at DESC) WHERE last_login_at IS NOT NULL;
```

#### Documents (High-Volume, Complex Queries)

```sql
-- Primary document access
CREATE INDEX idx_documents_application ON documents(application_id, document_type, status);
CREATE INDEX idx_documents_processing ON documents(ocr_status, created_at) WHERE ocr_status = 'PENDING';
CREATE INDEX idx_documents_review ON documents(tenant_id, review_status, reviewed_at DESC);

-- Full-text search for document content
CREATE INDEX idx_documents_content_search ON documents USING gin(to_tsvector('english', ocr_text));

-- Document analytics
CREATE INDEX idx_documents_fraud_analysis ON documents(tenant_id, fraud_score DESC, document_type)
  WHERE fraud_score > 0.5;
```

### 2. AI & Conversation Indexes

#### Conversations (Real-Time AI Processing)

```sql
-- Active conversation lookup
CREATE INDEX idx_conversations_active ON conversations(application_id, status, last_active_at DESC)
  WHERE status = 'ACTIVE';
CREATE INDEX idx_conversations_tenant_interface ON conversations(tenant_id, interface, created_at DESC);

-- AI cost tracking
CREATE INDEX idx_conversations_cost_analysis ON conversations(tenant_id, ai_provider, total_cost, created_at);
CREATE INDEX idx_conversations_session ON conversations(session_id) WHERE session_id IS NOT NULL;
```

#### AI Model Usage (High-Frequency Inserts)

```sql
-- Real-time cost tracking
CREATE INDEX idx_ai_usage_tenant_time ON ai_model_usage(tenant_id, created_at DESC);
CREATE INDEX idx_ai_usage_provider_model ON ai_model_usage(provider, model, created_at DESC);
CREATE INDEX idx_ai_usage_conversation ON ai_model_usage(conversation_id, created_at);

-- Cost optimization queries
CREATE INDEX idx_ai_usage_cost_analysis ON ai_model_usage(tenant_id, provider, model, cost, created_at);
```

### 3. External Integration Indexes

#### External Provider Calls (High-Volume Logging)

```sql
-- Performance monitoring
CREATE INDEX idx_provider_calls_performance ON external_provider_calls(plugin_id, successful, response_time, created_at);
CREATE INDEX idx_provider_calls_tenant_app ON external_provider_calls(tenant_id, application_id, created_at DESC);

-- Error tracking and debugging
CREATE INDEX idx_provider_calls_errors ON external_provider_calls(plugin_id, successful, created_at DESC)
  WHERE successful = false;
```

#### Verification Services

```sql
-- KYC verification lookups
CREATE INDEX idx_kyc_application_status ON kyc_verification(application_id, status, created_at DESC);
CREATE INDEX idx_kyc_provider_performance ON kyc_verification(provider, overall_result, processed_at);

-- Credit report analysis
CREATE INDEX idx_credit_reports_score ON credit_report(application_id, credit_score, generated_at DESC);
CREATE INDEX idx_credit_reports_provider ON credit_report(provider, report_type, generated_at DESC);
```

## Performance Optimization Indexes

### 1. Partial Indexes for Active Data

```sql
-- Only index active/pending records for operational queries
CREATE INDEX idx_applications_active_processing ON applications(tenant_id, stage, assigned_to)
  WHERE status IN ('PENDING', 'IN_PROGRESS', 'UNDER_REVIEW');

CREATE INDEX idx_tasks_pending ON tasks(assigned_to, priority, due_date)
  WHERE status IN ('PENDING', 'IN_PROGRESS');

CREATE INDEX idx_workflow_active ON workflow_execution(application_id, status, started_at)
  WHERE status IN ('RUNNING', 'PAUSED');
```

### 2. Covering Indexes for Read-Heavy Queries

```sql
-- Application dashboard queries (avoid table lookups)
CREATE INDEX idx_applications_dashboard_covering ON applications(tenant_id, status, created_at DESC)
  INCLUDE (application_number, business_name, risk_level, assigned_to);

-- User session validation
CREATE INDEX idx_user_session_covering ON user_session(session_token, expires_at)
  INCLUDE (user_id, device, last_active_at);

-- Document summary queries
CREATE INDEX idx_documents_summary_covering ON documents(application_id, status)
  INCLUDE (filename, document_type, uploaded_at, file_size);
```

### 3. Composite Indexes for Complex Queries

```sql
-- Risk assessment analytics
CREATE INDEX idx_risk_complex_analysis ON risk_assessment(
  application_id, overall_score DESC, risk_level, human_reviewed, created_at DESC
);

-- Workflow performance analysis
CREATE INDEX idx_workflow_performance ON workflow_execution(
  workflow_definition_id, status, started_at, completed_at
) WHERE completed_at IS NOT NULL;

-- Provider cost optimization
CREATE INDEX idx_provider_cost_optimization ON external_provider_call(
  tenant_id, plugin_id, successful, cost, response_time, created_at
);
```

## Analytics & Reporting Indexes

### 1. Time-Series Analytics

```sql
-- Daily metrics aggregation
CREATE INDEX idx_analytics_events_daily ON analytics_event(
  tenant_id, DATE(timestamp), event_type
);

-- Monthly cost analysis
CREATE INDEX idx_ai_usage_monthly ON ai_model_usage(
  tenant_id, DATE_TRUNC('month', created_at), provider
);

-- Application processing trends
CREATE INDEX idx_applications_trends ON application_status_history(
  DATE(changed_at), from_status, to_status
);
```

### 2. Business Intelligence Indexes

```sql
-- Tenant performance comparison
CREATE INDEX idx_tenant_metrics_comparison ON tenant_metrics(
  metric_date DESC, approval_rate DESC, avg_processing_time
);

-- Provider performance benchmarking
CREATE INDEX idx_provider_performance_benchmark ON provider_performance(
  plugin_id, period_start DESC, success_rate DESC, avg_response_time
);
```

## Advanced Indexing Strategies

### 1. Expression Indexes for Computed Queries

```sql
-- Age-based application queries
CREATE INDEX idx_applications_age ON applications(
  tenant_id,
  EXTRACT(days FROM (NOW() - created_at))
) WHERE status IN ('PENDING', 'IN_PROGRESS');

-- Document processing efficiency
CREATE INDEX idx_documents_processing_time ON documents(
  (EXTRACT(EPOCH FROM (updated_at - uploaded_at))/3600)::numeric(10,2)
) WHERE ocr_status = 'COMPLETED';
```

### 2. GIN Indexes for JSON and Array Data

```sql
-- Metadata search across applications
CREATE INDEX idx_applications_metadata_gin ON applications USING gin(metadata);

-- Features search in risk assessments
CREATE INDEX idx_risk_features_gin ON risk_assessment USING gin(features);

-- Plugin configuration search
CREATE INDEX idx_tenant_plugin_config_gin ON tenant_plugin USING gin(config);

-- Document tags search
CREATE INDEX idx_documents_tags_gin ON documents USING gin(tags);
```

### 3. Full-Text Search Optimization

```sql
-- Business name and description search
CREATE INDEX idx_applications_fulltext ON applications
  USING gin(to_tsvector('english', business_name || ' ' || COALESCE(metadata->>'description', '')));

-- Document content search with ranking
CREATE INDEX idx_documents_ranked_search ON documents
  USING gin(to_tsvector('english', ocr_text))
  WHERE ocr_confidence > 0.8;

-- Message content search
CREATE INDEX idx_messages_content_search ON message
  USING gin(to_tsvector('english', subject || ' ' || content));
```

## Index Maintenance Strategy

### 1. Automated Index Monitoring

```sql
-- Index usage statistics tracking
CREATE OR REPLACE VIEW v_index_usage_stats AS
SELECT
  schemaname,
  tablename,
  indexname,
  idx_scan as scans,
  idx_tup_read as tuples_read,
  idx_tup_fetch as tuples_fetched,
  pg_size_pretty(pg_relation_size(indexrelid)) as size
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- Unused index detection
CREATE OR REPLACE VIEW v_unused_indexes AS
SELECT
  schemaname,
  tablename,
  indexname,
  pg_size_pretty(pg_relation_size(indexrelid)) as size
FROM pg_stat_user_indexes
WHERE idx_scan = 0
AND indexrelid NOT IN (
  SELECT indexrelid FROM pg_index WHERE indisprimary OR indisunique
);
```

### 2. Performance Monitoring Queries

```sql
-- Slow query identification
CREATE OR REPLACE VIEW v_slow_queries AS
SELECT
  query,
  calls,
  total_time,
  mean_time,
  stddev_time,
  rows
FROM pg_stat_statements
WHERE mean_time > 100
ORDER BY mean_time DESC;

-- Index bloat monitoring
CREATE OR REPLACE VIEW v_index_bloat AS
SELECT
  schemaname,
  tablename,
  indexname,
  pg_size_pretty(pg_relation_size(indexrelid)) as current_size,
  CASE
    WHEN pg_relation_size(indexrelid) > 100000000 -- 100MB
    THEN 'Consider rebuilding'
    ELSE 'OK'
  END as recommendation
FROM pg_stat_user_indexes
ORDER BY pg_relation_size(indexrelid) DESC;
```

## Partitioning Strategy Integration

### 1. Tenant-Based Partitioning

```sql
-- Applications partitioned by tenant_id hash
CREATE TABLE applications_partitioned (LIKE applications INCLUDING ALL)
PARTITION BY HASH (tenant_id);

-- Create 16 partitions for even distribution
DO $$
BEGIN
  FOR i IN 0..15 LOOP
    EXECUTE format('CREATE TABLE applications_p%s PARTITION OF applications_partitioned
                   FOR VALUES WITH (MODULUS 16, REMAINDER %s)', i, i);

    -- Add indexes to each partition
    EXECUTE format('CREATE INDEX idx_applications_p%s_status_created
                   ON applications_p%s(tenant_id, status, created_at DESC)', i, i);
  END LOOP;
END $$;
```

### 2. Time-Based Partitioning for Analytics

```sql
-- Analytics events partitioned by month
CREATE TABLE analytics_event_partitioned (LIKE analytics_event INCLUDING ALL)
PARTITION BY RANGE (timestamp);

-- Automated monthly partition creation
CREATE OR REPLACE FUNCTION create_monthly_partition()
RETURNS void AS $$
DECLARE
  start_date date;
  end_date date;
  partition_name text;
BEGIN
  start_date := date_trunc('month', CURRENT_DATE + interval '1 month');
  end_date := start_date + interval '1 month';
  partition_name := 'analytics_event_' || to_char(start_date, 'YYYY_MM');

  EXECUTE format('CREATE TABLE %I PARTITION OF analytics_event_partitioned
                 FOR VALUES FROM (%L) TO (%L)',
                 partition_name, start_date, end_date);

  EXECUTE format('CREATE INDEX idx_%I_tenant_time ON %I(tenant_id, timestamp)',
                 partition_name, partition_name);
END $$ LANGUAGE plpgsql;
```

## Index Sizing and Capacity Planning

### 1. Storage Estimates

```sql
-- Index size projection based on row estimates
CREATE OR REPLACE VIEW v_index_size_projections AS
SELECT
  'applications' as table_name,
  100000 as estimated_rows,
  '~50MB' as primary_indexes_size,
  '~200MB' as all_indexes_size,
  '~400MB' as table_plus_indexes_size;
```

### 2. Maintenance Windows

- **Weekly**: Update index statistics and analyze query performance
- **Monthly**: Review unused indexes and bloat analysis
- **Quarterly**: Full index rebuild for high-churn tables
- **Annually**: Complete indexing strategy review and optimization

## Performance Testing Validation

### 1. Benchmark Queries

```sql
-- Application dashboard load test
EXPLAIN (ANALYZE, BUFFERS)
SELECT application_number, business_name, status, risk_level, created_at
FROM applications
WHERE tenant_id = $1 AND status IN ('PENDING', 'IN_PROGRESS')
ORDER BY created_at DESC
LIMIT 50;

-- AI cost analysis query
EXPLAIN (ANALYZE, BUFFERS)
SELECT provider, model, SUM(cost), COUNT(*), AVG(response_time)
FROM ai_model_usage
WHERE tenant_id = $1 AND created_at >= NOW() - INTERVAL '30 days'
GROUP BY provider, model
ORDER BY SUM(cost) DESC;
```

### 2. Expected Performance Targets

- **Simple lookups**: < 5ms response time
- **Complex dashboard queries**: < 50ms response time
- **Analytics aggregations**: < 500ms response time
- **Full-text searches**: < 100ms response time
- **Bulk operations**: < 2 seconds for 1000 records

## Index Implementation Priority

### Phase 1: Critical Performance Indexes (Week 1)

- Applications primary access patterns
- User authentication indexes
- Active conversation lookups

### Phase 2: Analytics and Reporting Indexes (Week 2)

- Time-series analytics indexes
- Cost tracking and optimization indexes
- Performance monitoring indexes

### Phase 3: Advanced Features (Week 3)

- Full-text search indexes
- JSON/Array search capabilities
- Complex analytical indexes

### Phase 4: Optimization and Monitoring (Week 4)

- Unused index cleanup
- Performance monitoring setup
- Automated maintenance procedures

This comprehensive indexing strategy ensures optimal performance for the AI-powered merchant
underwriting platform while maintaining scalability for enterprise growth.
