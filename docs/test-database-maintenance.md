# Test Database Maintenance Guide

## 🎯 Overview

This guide outlines best practices for maintaining your test database in sync with development schema changes.

## 🚀 Quick Commands

### Daily Development Workflow

```bash
# When you modify Prisma schema
npm run db:migrate              # Create and apply migration to dev DB
npm run test:db:sync           # Sync test DB with new migration

# Run tests
npm run test                   # All tests with synchronized DB
```

### Emergency Database Operations

```bash
# Reset test database completely (⚠️ DATA LOSS)
npm run test:db:reset

# Recreate test database from scratch
npm run test:db:setup

# Manual sync when migrations fail
./scripts/sync-test-db.sh --force-sync
```

## 📋 Development Workflows

### 1. Adding New Features with Schema Changes

**Step 1: Modify Prisma Schema**
```bash
# Edit apps/backend/prisma/schema.prisma
# Add your new models, fields, etc.
```

**Step 2: Create Migration**
```bash
npm run db:migrate  # This creates migration AND applies to dev DB
```

**Step 3: Sync Test Database**
```bash
npm run test:db:sync  # Applies same migration to test DB
```

**Step 4: Run Tests**
```bash
npm run test  # Verify everything works
```

### 2. Working with Existing Features (No Schema Changes)

Just run tests normally - no database sync needed:
```bash
npm run test
```

### 3. When Schema Changes Fail to Apply

**Option A: Fix Migration Manually**
```bash
# Edit the migration file in prisma/migrations/
# Then apply it
npm run test:db:sync
```

**Option B: Force Reset (⚠️ DATA LOSS)**
```bash
npm run test:db:reset  # Resets test DB to match current schema
```

## 🔄 Automated Sync Strategy

### Pre-Test Hook (Recommended)

Add to your `apps/backend/package.json`:
```json
{
  "scripts": {
    "pretest": "npm run test:db:sync --if-present"
  }
}
```

This automatically syncs the test database before every test run.

### Git Hooks Integration

Add to `.husky/pre-push`:
```bash
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Ensure test database is synced before push
npm run test:db:sync --if-present
```

## 📊 Database State Management

### Development Database (localhost:5433)
- **Purpose**: Primary development work
- **Updates**: Via `npm run db:migrate`
- **Data**: Persistent development data

### Test Database (localhost:5433/underwriting_test)
- **Purpose**: Automated testing
- **Updates**: Via `npm run test:db:sync`
- **Data**: Temporary test data (cleared between tests)

## 🛠 Troubleshooting

### Common Issues

**1. "Table does not exist" errors in tests**
```bash
# Solution: Sync test database
npm run test:db:sync
```

**2. Migration conflicts between team members**
```bash
# Reset and pull latest migrations
git pull origin main
npm run test:db:setup  # Recreate test DB with latest schema
```

**3. Test database gets corrupted**
```bash
# Nuclear option - complete reset
npm run test:db:setup
```

**4. Schema drift between dev and test**
```bash
# Check current schema state
npx prisma db pull --print

# Force sync test database
./scripts/sync-test-db.sh --force-sync
```

## 📈 Best Practices

### 1. **Always Create Migrations for Schema Changes**
- Don't use `db push` for feature development
- Use `db migrate` to create proper migration files
- Migrations are versioned and team-shareable

### 2. **Keep Test Database Clean**
- Test database should be stateless
- Clear data between test runs
- Never store important data in test database

### 3. **Regular Maintenance**
```bash
# Weekly maintenance routine
npm run test:db:setup    # Fresh test database
npm run test             # Verify all tests pass
```

### 4. **Team Collaboration**
- Always commit migration files
- Never edit existing migration files
- Communicate schema changes to team

### 5. **CI/CD Integration**
```yaml
# Example GitHub Actions step
- name: Setup Test Database
  run: |
    npm run test:db:setup
    npm run test
```

## 🚨 Emergency Procedures

### Test Database Complete Failure
```bash
# Step 1: Stop all running tests
pkill -f jest

# Step 2: Drop and recreate database
npm run test:db:setup

# Step 3: Verify with simple test
npm test -- --testNamePattern="simple" --maxWorkers=1
```

### Migration Rollback (if something goes wrong)
```bash
# View migration history
npx prisma migrate status

# Reset to specific migration (replace with actual migration name)
DATABASE_URL="postgresql://postgres:password@localhost:5433/underwriting_test" \
npx prisma migrate reset --to 20250720051734_init
```

## 📝 Environment Variables

Ensure these are set in your `.env`:
```bash
# Test database (already configured)
TEST_DATABASE_URL="postgresql://postgres:password@localhost:5433/underwriting_test"

# Development database (already configured)
DATABASE_URL="postgresql://postgres:password@localhost:5433/underwriting_dev"
```

## 🎯 Summary

**For Regular Development:**
1. Modify schema → `npm run db:migrate`
2. Sync test DB → `npm run test:db:sync`  
3. Run tests → `npm run test`

**For Emergency Situations:**
1. Complete reset → `npm run test:db:setup`
2. Force sync → `./scripts/sync-test-db.sh --force-sync`

Your test database is now a first-class citizen in your development workflow! 🚀