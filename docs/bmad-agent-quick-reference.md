# BMad Agent Quick Reference Guide

## Overview

This guide provides BMad agents with essential information about the quality gates system and
development workflow for the AI Underwriting Platform.

## 🎯 Quality Requirements

### BMad Quality Score Thresholds

- **Development**: 70+ BMad score acceptable for drafts
- **Story Completion**: 85+ BMad score required
- **Production Deployment**: 90+ BMad score required
- **TypeScript Policy**: **ZERO errors tolerance** - strict enforcement

### Quality Gates (Automatic on git push)

Every `git push` automatically runs:

1. **Lint Check** - ESLint validation across all packages
2. **Type Check** - TypeScript strict validation (zero errors)
3. **Tests** - All test suites must pass
4. **Build Check** - Ensures project builds successfully
5. **BMad Validation** - Code quality, architecture, security validation
6. **Auto Self-Heal** - Attempts to fix issues automatically if found

## 🤖 BMad Agent Types & Responsibilities

### Frontend Agents

- **Focus**: React components, UI/UX, accessibility
- **Quality Focus**: Component architecture, performance, WCAG compliance
- **Key Commands**: `npm run dev:frontend`, `npm run bmad:validate`
- **Commit Format**: `BMad-Agent: frontend-specialist`

### Backend Agents

- **Focus**: Express APIs, database operations, business logic
- **Quality Focus**: API design, security, performance, data validation
- **Key Commands**: `npm run dev:backend`, `npm run db:migrate`
- **Commit Format**: `BMad-Agent: backend-specialist`

### API Agents

- **Focus**: OpenRouter integration, external API connections
- **Quality Focus**: Error handling, rate limiting, cost optimization
- **Key Commands**: `npm run test:api`, `npm run bmad:validate`
- **Commit Format**: `BMad-Agent: api-specialist`

### Integration Agents

- **Focus**: System integration, data flow, middleware
- **Quality Focus**: Error handling, monitoring, observability
- **Key Commands**: `npm run ci:validate`, `npm run docker:up`
- **Commit Format**: `BMad-Agent: integration-specialist`

### Platform Agents

- **Focus**: Infrastructure, CI/CD, DevOps, monitoring
- **Quality Focus**: Deployment reliability, performance, security
- **Key Commands**: `npm run setup:all`, `npm run docker:logs`
- **Commit Format**: `BMad-Agent: platform-specialist`

## 🛠️ Essential Commands

### BMad Quality Commands

```bash
# Check BMad quality score (must be 85+)
npm run bmad:validate

# Auto-fix common issues
npm run bmad:self-heal

# View current quality score
npm run bmad:quality-score

# Run full CI validation
npm run ci:validate
```

### Development Commands

```bash
# Start development environment
npm run dev                    # All services
npm run dev:frontend          # Frontend only (:3000)
npm run dev:backend           # Backend only (:5000)

# Quality validation
npm run lint                  # Lint all packages
npm run typecheck            # TypeScript validation
npm run test                 # Run all tests
npm run build                # Build all packages
```

### Database Commands

```bash
# Database operations (from apps/backend/)
npm run db:migrate           # Run migrations
npm run db:generate          # Generate Prisma client
npm run db:studio           # Open Prisma Studio (:5555)
npm run db:reset            # Reset database
```

## 📝 BMad Commit Message Format

### Standard Format

```
feat: implement user authentication

BMad-Agent: auth-specialist
BMad-Validation: ✅ lint, typecheck, tests
BMad-Quality-Score: 95/100
```

### Commit Types

- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation updates
- `style:` - Code formatting
- `refactor:` - Code refactoring
- `test:` - Test additions
- `chore:` - Maintenance tasks

## 🔄 Development Workflow

### Story Implementation Process

1. **Story Assignment**: Receive story from Scrum Master agent
2. **Initial Validation**: Run `npm run bmad:validate` to get baseline score
3. **Implementation**: Follow story tasks and acceptance criteria
4. **Continuous Validation**: Run quality checks during development
5. **Self-Healing**: Use `npm run bmad:self-heal` for auto-fixes
6. **Quality Gate**: Ensure 85+ BMad score before completion
7. **Commit**: Use BMad commit format with agent signature
8. **Push**: Quality gates automatically run and block if failing
9. **Story Update**: Update BMad Quality Metrics in story file

### Quality Gate Failure Response

If pre-push quality gates fail:

1. **Review Errors**: Check lint, typecheck, test, build output
2. **Auto-Heal**: Run `npm run bmad:self-heal`
3. **Manual Fixes**: Address remaining issues manually
4. **Re-validate**: Run `npm run bmad:validate` to confirm fixes
5. **Retry Push**: Quality gates will re-run automatically

## 🏗️ Architecture Guidelines

### Monorepo Structure

- `apps/frontend/` - Next.js 15 + React 19 + TypeScript + Tailwind
- `apps/backend/` - Express 4.21 + Prisma 6.12 + TypeScript + PostgreSQL
- `packages/shared/` - Shared TypeScript types and utilities
- `packages/ui/` - Shared UI components (shadcn/ui based)
- `packages/config/` - Shared configurations

### Quality Standards

- **TypeScript Strict**: All code must pass strict TypeScript checks
- **ESLint Compliance**: Follow project ESLint configuration
- **Test Coverage**: Write tests for new functionality
- **Performance**: Meet performance benchmarks in epic requirements
- **Security**: Follow security best practices (no hardcoded secrets)
- **Accessibility**: WCAG 2.1 AA compliance for frontend components

## 🚨 Common Issues & Solutions

### TypeScript Errors

```bash
# Check TypeScript errors
npm run typecheck

# Common fixes
npm run bmad:self-heal     # Auto-fix simple errors
```

### Lint Errors

```bash
# Check lint errors
npm run lint

# Auto-fix lint issues
npm run lint:fix
npm run bmad:self-heal
```

### Test Failures

```bash
# Run tests
npm run test

# Update snapshots if needed
npm run bmad:self-heal     # Auto-updates snapshots
```

### Build Failures

```bash
# Check build errors
npm run build

# Clear cache and retry
npm run bmad:self-heal     # Clears build cache
```

## 📊 Quality Metrics Tracking

### Story Completion Checklist

- [ ] All tasks and subtasks completed
- [ ] BMad quality score 85+
- [ ] Zero TypeScript errors
- [ ] All tests passing
- [ ] Code follows architecture patterns
- [ ] Performance requirements met
- [ ] Security validation passed
- [ ] BMad Quality Metrics updated in story
- [ ] Commit uses BMad format
- [ ] Quality gates passed on push

### Quality Score Components

- **Code Quality**: Lint compliance, formatting, best practices
- **Architecture**: Design patterns, separation of concerns
- **Security**: No hardcoded secrets, input validation
- **Testing**: Test coverage, test quality
- **Performance**: Meets epic-specific performance requirements
- **Documentation**: Code comments, API documentation

## 🔗 Useful Links

- **Quality Gates Documentation**: `docs/bmad-quality-gates.md`
- **CLAUDE.md**: Main repository guidance
- **Epic Requirements**: `docs/epic-*.md` files
- **Story Templates**: `.bmad-core/templates/story-tmpl.yaml`
- **Agent Workflows**: `.bmad-core/workflows/`
- **Package Scripts**: `package.json` in root and workspace packages

## 🆘 Getting Help

### Quality Issues

1. Run `npm run bmad:validate` for detailed quality report
2. Use `npm run bmad:self-heal` for automatic fixes
3. Check `.bmad-validation-report.json` for detailed analysis

### Development Issues

1. Check `CLAUDE.md` for development guidance
2. Review epic-specific requirements in `docs/epic-*.md`
3. Consult agent-specific workflows in `.bmad-core/workflows/`

### Emergency Bypass (Use Sparingly)

```bash
# Skip quality gates (emergency only)
git push origin main --no-verify

# Skip CI/CD (emergency only)
git commit -m "emergency fix [skip ci]"
```

**Remember**: BMad quality gates are designed to save time by catching issues early. Work with the
system, not against it! 🎯
