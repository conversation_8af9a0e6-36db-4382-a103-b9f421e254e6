import { z } from 'zod';
import { prisma } from '../prisma/client';
import { AuditLogger } from '../utils/audit';

// Input validation schema - Always define strict types
const ServiceNameInputSchema = z.object({
  id: z.string().min(1),
  tenantId: z.string().optional(),
  // Add more fields as needed
});

const ServiceNameUpdateSchema = z.object({
  // Define update-specific fields
});

// Type definitions
export type ServiceNameInput = z.infer<typeof ServiceNameInputSchema>;
export type ServiceNameUpdate = z.infer<typeof ServiceNameUpdateSchema>;

export interface ServiceNameOutput {
  success: boolean;
  data?: unknown;
  error?: string;
}

/**
 * ServiceName - Brief description of what this service does
 *
 * BMad-Compliant Service Template:
 * - Strict TypeScript typing with zod validation
 * - Comprehensive error handling
 * - Audit logging integration
 * - Tenant isolation support
 * - Return type annotations on all methods
 */
export class ServiceName {
  /**
   * Primary service method - describe what it does
   */
  static async primaryMethod(
    input: ServiceNameInput
  ): Promise<ServiceNameOutput> {
    try {
      // 1. Input validation
      const validatedInput = ServiceNameInputSchema.parse(input);

      // 2. Business logic implementation
      const result = await prisma.modelName.findFirst({
        where: {
          id: validatedInput.id,
          ...(validatedInput.tenantId && { tenantId: validatedInput.tenantId }),
        },
      });

      // 3. Audit logging
      await AuditLogger.log({
        action: 'SERVICE_ACTION',
        entityType: 'EntityType',
        entityId: validatedInput.id,
        userId: validatedInput.userId,
        tenantId: validatedInput.tenantId,
        riskLevel: 'LOW',
      });

      // 4. Return success response
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      // 5. Error handling and logging
      console.error('ServiceName.primaryMethod failed:', error);

      if (error instanceof z.ZodError) {
        return {
          success: false,
          error: `Validation error: ${error.errors.map((e) => e.message).join(', ')}`,
        };
      }

      return {
        success: false,
        error: 'Internal service error',
      };
    }
  }

  /**
   * Secondary service method - describe what it does
   */
  static async secondaryMethod(
    input: ServiceNameInput,
    updateData: ServiceNameUpdate
  ): Promise<ServiceNameOutput> {
    try {
      // 1. Input validation
      const validatedInput = ServiceNameInputSchema.parse(input);
      const validatedUpdate = ServiceNameUpdateSchema.parse(updateData);

      // 2. Business logic
      const result = await prisma.modelName.update({
        where: {
          id: validatedInput.id,
          ...(validatedInput.tenantId && { tenantId: validatedInput.tenantId }),
        },
        data: validatedUpdate,
      });

      // 3. Audit logging
      await AuditLogger.log({
        action: 'SERVICE_UPDATE',
        entityType: 'EntityType',
        entityId: validatedInput.id,
        userId: validatedInput.userId,
        tenantId: validatedInput.tenantId,
        oldValues: input,
        newValues: updateData,
        riskLevel: 'MEDIUM',
      });

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('ServiceName.secondaryMethod failed:', error);

      if (error instanceof z.ZodError) {
        return {
          success: false,
          error: `Validation error: ${error.errors.map((e) => e.message).join(', ')}`,
        };
      }

      return {
        success: false,
        error: 'Internal service error',
      };
    }
  }

  /**
   * Utility method - helper functions
   */
  private static validateTenantAccess(
    tenantId: string,
    userTenantId?: string
  ): boolean {
    if (!userTenantId) return false;
    return tenantId === userTenantId;
  }

  /**
   * Get method - retrieve data with proper filtering
   */
  static async getById(
    id: string,
    tenantId?: string
  ): Promise<ServiceNameOutput> {
    try {
      const result = await prisma.modelName.findUnique({
        where: {
          id,
          ...(tenantId && { tenantId }),
        },
      });

      if (!result) {
        return {
          success: false,
          error: 'Record not found',
        };
      }

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('ServiceName.getById failed:', error);
      return {
        success: false,
        error: 'Failed to retrieve record',
      };
    }
  }

  /**
   * List method - retrieve multiple records with pagination
   */
  static async list(
    tenantId?: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<ServiceNameOutput> {
    try {
      const results = await prisma.modelName.findMany({
        where: {
          ...(tenantId && { tenantId }),
        },
        take: limit,
        skip: offset,
        orderBy: { createdAt: 'desc' },
      });

      const total = await prisma.modelName.count({
        where: {
          ...(tenantId && { tenantId }),
        },
      });

      return {
        success: true,
        data: {
          items: results,
          total,
          limit,
          offset,
        },
      };
    } catch (error) {
      console.error('ServiceName.list failed:', error);
      return {
        success: false,
        error: 'Failed to retrieve records',
      };
    }
  }

  /**
   * Delete method - soft delete with audit trail
   */
  static async delete(
    id: string,
    tenantId?: string,
    userId?: string
  ): Promise<ServiceNameOutput> {
    try {
      // Check if record exists
      const existing = await this.getById(id, tenantId);
      if (!existing.success) {
        return existing;
      }

      // Perform soft delete
      const result = await prisma.modelName.update({
        where: { id },
        data: {
          deletedAt: new Date(),
          updatedAt: new Date(),
        },
      });

      // Audit logging
      await AuditLogger.log({
        action: 'SERVICE_DELETE',
        entityType: 'EntityType',
        entityId: id,
        userId,
        tenantId,
        oldValues: existing.data,
        riskLevel: 'HIGH',
      });

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('ServiceName.delete failed:', error);
      return {
        success: false,
        error: 'Failed to delete record',
      };
    }
  }
}

export default ServiceName;
