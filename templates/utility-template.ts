import { z } from 'zod';

// Input validation schemas
const UtilityInputSchema = z.object({
  // Define strict input types
  value: z.string().min(1),
  options: z
    .object({
      // Define options
    })
    .optional(),
});

// Type definitions
export type UtilityInput = z.infer<typeof UtilityInputSchema>;

export interface UtilityOutput {
  success: boolean;
  data?: unknown;
  error?: string;
}

export interface UtilityConfig {
  // Configuration options
  timeout?: number;
  retries?: number;
}

/**
 * UtilityName - Brief description of utility purpose
 *
 * BMad-Compliant Utility Template:
 * - Pure functions with strict typing
 * - Comprehensive error handling
 * - Input validation with zod
 * - Return type annotations
 * - Configuration support
 * - Performance considerations
 */
export class UtilityName {
  private static readonly DEFAULT_CONFIG: Required<UtilityConfig> = {
    timeout: 5000,
    retries: 3,
  };

  /**
   * Primary utility function - describe what it does
   */
  static async process(
    input: UtilityInput,
    config: UtilityConfig = {}
  ): Promise<UtilityOutput> {
    try {
      // 1. Input validation
      const validatedInput = UtilityInputSchema.parse(input);
      const finalConfig = { ...this.DEFAULT_CONFIG, ...config };

      // 2. Processing logic
      const result = await this.performProcessing(validatedInput, finalConfig);

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('UtilityName.process failed:', error);

      if (error instanceof z.ZodError) {
        return {
          success: false,
          error: `Validation error: ${error.errors.map((e) => e.message).join(', ')}`,
        };
      }

      return {
        success: false,
        error: 'Processing failed',
      };
    }
  }

  /**
   * Synchronous utility function - for simple operations
   */
  static validate(input: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validation logic
    if (!input || input.trim().length === 0) {
      errors.push('Input cannot be empty');
    }

    if (input.length > 1000) {
      errors.push('Input too long');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Transform function - data transformation utility
   */
  static transform<T, R>(input: T, transformer: (item: T) => R): R {
    try {
      return transformer(input);
    } catch (error) {
      console.error('UtilityName.transform failed:', error);
      throw new Error('Transformation failed');
    }
  }

  /**
   * Batch processing utility - for handling multiple items
   */
  static async batchProcess<T, R>(
    items: T[],
    processor: (item: T) => Promise<R>,
    options: { batchSize?: number; parallel?: boolean } = {}
  ): Promise<{ results: R[]; errors: Error[] }> {
    const { batchSize = 10, parallel = false } = options;
    const results: R[] = [];
    const errors: Error[] = [];

    try {
      if (parallel) {
        // Parallel processing
        const chunks = this.chunkArray(items, batchSize);

        for (const chunk of chunks) {
          const promises = chunk.map(async (item) => {
            try {
              return await processor(item);
            } catch (error) {
              errors.push(
                error instanceof Error ? error : new Error(String(error))
              );
              return null;
            }
          });

          const chunkResults = await Promise.all(promises);
          results.push(...chunkResults.filter((r): r is R => r !== null));
        }
      } else {
        // Sequential processing
        for (const item of items) {
          try {
            const result = await processor(item);
            results.push(result);
          } catch (error) {
            errors.push(
              error instanceof Error ? error : new Error(String(error))
            );
          }
        }
      }

      return { results, errors };
    } catch (error) {
      console.error('UtilityName.batchProcess failed:', error);
      throw new Error('Batch processing failed');
    }
  }

  /**
   * Retry utility - for operations that might fail
   */
  static async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (attempt === maxRetries) {
          break;
        }

        // Exponential backoff
        const backoffDelay = delay * Math.pow(2, attempt - 1);
        await this.sleep(backoffDelay);
      }
    }

    throw lastError!;
  }

  /**
   * Memoization utility - for expensive operations
   */
  static memoize<Args extends unknown[], Return>(
    fn: (...args: Args) => Return,
    keyGenerator?: (...args: Args) => string
  ): (...args: Args) => Return {
    const cache = new Map<string, Return>();

    return (...args: Args): Return => {
      const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);

      if (cache.has(key)) {
        return cache.get(key)!;
      }

      const result = fn(...args);
      cache.set(key, result);
      return result;
    };
  }

  /**
   * Private helper methods
   */
  private static async performProcessing(
    input: UtilityInput,
    config: Required<UtilityConfig>
  ): Promise<unknown> {
    // Implementation details
    return input.value.toUpperCase();
  }

  private static chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  private static sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Static validation helpers
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  static sanitizeString(input: string): string {
    return input
      .trim()
      .replace(/[<>\"']/g, '') // Remove potentially dangerous characters
      .substring(0, 1000); // Limit length
  }

  /**
   * Performance measurement utility
   */
  static async measurePerformance<T>(
    operation: () => Promise<T>,
    label?: string
  ): Promise<{ result: T; duration: number }> {
    const start = performance.now();
    const result = await operation();
    const duration = performance.now() - start;

    if (label) {
      console.log(`${label} took ${duration.toFixed(2)}ms`);
    }

    return { result, duration };
  }
}

export default UtilityName;
