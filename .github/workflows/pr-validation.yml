name: 🔍 Pull Request Validation

on:
  pull_request:
    types: [opened, synchronize, reopened, ready_for_review]

env:
  NODE_VERSION: '20'

jobs:
  # ============================================================================
  # PULL REQUEST QUALITY GATES
  # ============================================================================
  pr-validation:
    name: 🛡️ PR Quality Gates
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install Dependencies
        run: npm ci

      - name: 🚫 TypeScript Zero-Error Policy Check
        run: |
          echo "🚫 Enforcing ZERO TypeScript errors policy for PR..."
          npm run typecheck
          echo "✅ PR meets TypeScript strict policy"

      - name: 📏 PR Size Check
        run: |
          echo "📏 Checking PR size..."
          CHANGED_FILES=$(git diff --name-only origin/${{ github.event.pull_request.base.ref }}..HEAD | wc -l)
          ADDED_LINES=$(git diff --numstat origin/${{ github.event.pull_request.base.ref }}..HEAD | awk '{sum += $1} END {print sum}')
          REMOVED_LINES=$(git diff --numstat origin/${{ github.event.pull_request.base.ref }}..HEAD | awk '{sum += $2} END {print sum}')
          
          echo "📊 PR Statistics:"
          echo "   📁 Files changed: $CHANGED_FILES"
          echo "   ➕ Lines added: $ADDED_LINES"
          echo "   ➖ Lines removed: $REMOVED_LINES"
          
          if [ "$CHANGED_FILES" -gt 50 ]; then
            echo "⚠️ Large PR detected (>50 files). Consider breaking into smaller PRs."
          else
            echo "✅ PR size is appropriate"
          fi

      - name: 🔍 PR Description Check
        run: |
          echo "🔍 Checking PR description..."
          PR_DESCRIPTION="${{ github.event.pull_request.body }}"
          
          if [ ${#PR_DESCRIPTION} -lt 20 ]; then
            echo "⚠️ PR description is too short. Please provide more context."
            exit 1
          else
            echo "✅ PR description is adequate"
          fi

      - name: 🏷️ Epic/Story Tracking Check
        run: |
          echo "🏷️ Checking for Epic/Story references..."
          PR_TITLE="${{ github.event.pull_request.title }}"
          PR_DESCRIPTION="${{ github.event.pull_request.body }}"
          
          if echo "$PR_TITLE $PR_DESCRIPTION" | grep -iE "(epic|story|feat|fix|refactor)" > /dev/null; then
            echo "✅ PR is properly categorized"
          else
            echo "⚠️ Consider adding Epic/Story reference or category (feat/fix/refactor)"
          fi

  # ============================================================================
  # CHANGED FILES IMPACT ANALYSIS
  # ============================================================================
  impact-analysis:
    name: 📊 Impact Analysis
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📊 Analyze Changed Components
        run: |
          echo "📊 Analyzing impact of changes..."
          
          FRONTEND_CHANGES=$(git diff --name-only origin/${{ github.event.pull_request.base.ref }}..HEAD | grep "apps/frontend" | wc -l)
          BACKEND_CHANGES=$(git diff --name-only origin/${{ github.event.pull_request.base.ref }}..HEAD | grep "apps/backend" | wc -l)
          SHARED_CHANGES=$(git diff --name-only origin/${{ github.event.pull_request.base.ref }}..HEAD | grep "packages/" | wc -l)
          CONFIG_CHANGES=$(git diff --name-only origin/${{ github.event.pull_request.base.ref }}..HEAD | grep -E "(docker-compose|package\.json|turbo\.json|tsconfig)" | wc -l)
          
          echo "🎨 Frontend changes: $FRONTEND_CHANGES files"
          echo "⚙️ Backend changes: $BACKEND_CHANGES files"
          echo "📦 Shared package changes: $SHARED_CHANGES files"
          echo "🔧 Configuration changes: $CONFIG_CHANGES files"
          
          echo "📋 Recommended reviewers:"
          if [ "$FRONTEND_CHANGES" -gt 0 ]; then
            echo "   🎨 Frontend Team (Epic 1, 3, 8 changes)"
          fi
          if [ "$BACKEND_CHANGES" -gt 0 ]; then
            echo "   ⚙️ Backend Team (Epic 2, 5, 6 changes)"
          fi
          if [ "$SHARED_CHANGES" -gt 0 ]; then
            echo "   🏗️ Platform Team (shared package changes)"
          fi
          if [ "$CONFIG_CHANGES" -gt 0 ]; then
            echo "   🔧 Platform Team (configuration changes)"
          fi

  # ============================================================================
  # BMAD AGENT CONTEXT CHECK
  # ============================================================================
  bmad-context-check:
    name: 🤖 BMad Agent Context Validation
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🤖 BMad Documentation Check
        run: |
          echo "🤖 Checking BMad agent context requirements..."
          
          # Check if documentation is updated
          DOCS_CHANGES=$(git diff --name-only origin/${{ github.event.pull_request.base.ref }}..HEAD | grep "docs/" | wc -l)
          
          if [ "$DOCS_CHANGES" -gt 0 ]; then
            echo "✅ Documentation updated in this PR"
            
            # Check specific documentation files
            if git diff --name-only origin/${{ github.event.pull_request.base.ref }}..HEAD | grep "docs/CONTEXT.md" > /dev/null; then
              echo "✅ CONTEXT.md updated"
            fi
            
            if git diff --name-only origin/${{ github.event.pull_request.base.ref }}..HEAD | grep "docs/PROGRESS.md" > /dev/null; then
              echo "✅ PROGRESS.md updated"
            fi
          else
            echo "ℹ️ No documentation changes in this PR"
          fi

      - name: 🔍 Context Integration Points Check
        run: |
          echo "🔍 Checking for cross-team integration impacts..."
          
          # Check for API changes that might affect other teams
          API_CHANGES=$(git diff origin/${{ github.event.pull_request.base.ref }}..HEAD | grep -E "(\+|\-).*interface|(\+|\-).*type|(\+|\-).*endpoint" | wc -l)
          
          if [ "$API_CHANGES" -gt 0 ]; then
            echo "🔄 API/Interface changes detected:"
            echo "   📋 Ensure all BMad agent teams are notified"
            echo "   🔗 Update API contracts if needed"
            echo "   📚 Update documentation for integration points"
          else
            echo "✅ No API contract changes detected"
          fi

  # ============================================================================
  # AUTOMATED PR QUALITY REPORT
  # ============================================================================
  pr-quality-report:
    name: 📋 PR Quality Report
    runs-on: ubuntu-latest
    needs: [pr-validation, impact-analysis, bmad-context-check]
    if: github.event.pull_request.draft == false

    steps:
      - name: 📋 Generate PR Quality Report
        run: |
          echo "📋 ============================================"
          echo "    PULL REQUEST QUALITY REPORT"
          echo "📋 ============================================"
          echo ""
          echo "✅ TypeScript Policy: COMPLIANT"
          echo "✅ Code Quality: VALIDATED"
          echo "✅ Impact Analysis: COMPLETED"
          echo "✅ BMad Context: VALIDATED"
          echo ""
          echo "🎯 PR is ready for review by appropriate teams"
          echo "🤖 BMad agent integration validated"
          echo ""
          echo "📊 Next Steps:"
          echo "   1. Team review by affected agent teams"
          echo "   2. Integration testing"
          echo "   3. Merge after approval"