# 🚀 CI/CD Pipeline Documentation

## Overview

This directory contains the GitHub Actions workflows for the AI-Powered Merchant Underwriting
Platform. The CI/CD pipeline is designed to support autonomous development by BMad agent teams while
maintaining strict quality standards.

## 🛠️ Workflows

### 1. Main CI/CD Pipeline (`ci.yml`)

**Triggers**: Push to `main`/`master`/`develop`, Pull Requests

**Features**:

- ✅ **Health Check & Validation** - Repository and environment validation
- ✅ **Code Quality** - TypeScript strict policy enforcement (ZERO errors)
- ✅ **Database & Services** - PostgreSQL and Redis integration testing
- ✅ **Unit & Integration Testing** - Comprehensive test suite
- ✅ **Build & Compilation** - Multi-workspace build validation
- ✅ **Security Scanning** - Dependency and code security checks
- ✅ **BMad Integration** - Autonomous agent team validation
- ✅ **Deployment Readiness** - Production readiness assessment

### 2. Pull Request Validation (`pr-validation.yml`)

**Triggers**: Pull Request events (opened, synchronize, reopened)

**Features**:

- 📏 **PR Size Analysis** - Prevents oversized pull requests
- 📝 **Description Validation** - Ensures adequate PR descriptions
- 🏷️ **Epic/Story Tracking** - Validates proper categorization
- 📊 **Impact Analysis** - Analyzes which teams are affected
- 🤖 **BMad Context Check** - Validates agent documentation updates
- 📋 **Quality Report** - Comprehensive PR assessment

## 🚫 TypeScript Zero-Error Policy

The CI/CD pipeline enforces a **ZERO TOLERANCE** policy for TypeScript errors:

- ❌ **Any TypeScript errors block the pipeline**
- ❌ **No exceptions or workarounds allowed**
- ✅ **All code must pass `tsc --noEmit` with zero errors**
- ✅ **ESLint TypeScript rules must pass completely**

This policy ensures maximum code quality for autonomous BMad agent development.

## 🤖 BMad Agent Team Integration

The pipeline validates infrastructure support for all BMad agent teams:

### Frontend Team (Epic 1, 3, 8)

- ✅ Next.js 15 + React 19 + TypeScript stack
- ✅ shadcn/ui component system
- ✅ Real-time capabilities (Socket.IO)
- ✅ Mobile responsiveness & accessibility

### Backend Team (Epic 2, 5, 6)

- ✅ Express + TypeScript + Prisma stack
- ✅ AI service integration (OpenRouter + Ollama)
- ✅ Database multi-tenancy support
- ✅ Plugin architecture foundation

### Integration Team (Epic 4, 7)

- ✅ Docker containerization
- ✅ Plugin adapter architecture
- ✅ External service mock framework
- ✅ API integration capabilities

### Platform Team (Cross-epic)

- ✅ Turborepo monorepo coordination
- ✅ Cross-team dependency management
- ✅ Infrastructure monitoring
- ✅ Database schema coordination

## 📊 Quality Gates

### Pre-commit (Local)

- 🚫 TypeScript zero-error validation
- 🧹 ESLint + Prettier formatting
- 📝 Commit message format validation

### CI Pipeline (Remote)

- 🔍 Code quality validation
- 🧪 Comprehensive testing
- 🏗️ Build verification
- 🔒 Security scanning
- 🤖 BMad integration validation

### Pull Request

- 📏 Size and complexity analysis
- 📊 Impact assessment
- 🏷️ Proper categorization
- 🤖 Documentation updates

## 🛠️ Local Development Commands

```bash
# Full CI/CD validation locally
npm run ci:validate

# Quick local validation
./scripts/validate-local.sh

# BMad agent integration check
./scripts/bmad-validate.sh

# Setup CI/CD (one-time)
./scripts/setup-ci-cd.sh
```

## 🔧 Configuration Files

- `.github/workflows/ci.yml` - Main CI/CD pipeline
- `.github/workflows/pr-validation.yml` - PR validation
- `.husky/pre-commit` - Pre-commit hooks
- `.husky/commit-msg` - Commit message validation
- `.prettierrc.json` - Code formatting rules
- `lint-staged` config in `package.json`

## 🚀 Deployment Process

### Development

1. **Local development** with pre-commit validation
2. **Push to feature branch** triggers CI validation
3. **Create pull request** triggers PR validation
4. **Merge to main** triggers full CI/CD pipeline

### Production (Future)

1. **Tagged releases** trigger deployment pipeline
2. **Staging environment** deployment and testing
3. **Production deployment** with blue-green strategy
4. **Post-deployment monitoring** and validation

## 🎯 Success Metrics

The CI/CD pipeline tracks these key metrics:

- **Build Success Rate**: Target >95%
- **TypeScript Error Rate**: Target 0% (zero tolerance)
- **Test Coverage**: Target >90%
- **Security Vulnerabilities**: Target 0 critical/high
- **Pipeline Duration**: Target <10 minutes
- **BMad Integration Health**: Target 100%

## 🔍 Troubleshooting

### Common Issues

#### TypeScript Errors Block Pipeline

```bash
# Fix locally first
npm run typecheck
# Address all errors shown
# Then commit and push
```

#### Lint Errors

```bash
# Auto-fix most issues
npm run lint:fix
# Manual fixes for complex issues
```

#### Build Failures

```bash
# Clean and rebuild
npm run clean
npm install
npm run build
```

#### Test Failures

```bash
# Run tests locally
npm run test
# Fix failing tests
# Ensure database is running for integration tests
```

### Getting Help

1. **Check the failed job logs** in GitHub Actions
2. **Run local validation** to reproduce issues
3. **Review TypeScript strict policy** documentation
4. **Validate BMad integration** requirements

## 🔐 Security Features

- **Dependency scanning** for vulnerabilities
- **Secret detection** in code
- **Security best practices** validation
- **Access control** validation
- **Audit logging** for all changes

## 📈 Future Enhancements

- **Performance testing** integration
- **End-to-end testing** automation
- **Advanced security scanning** (SAST/DAST)
- **Deployment automation** to staging/production
- **Monitoring integration** for observability
- **Cost optimization** tracking

---

**The CI/CD pipeline is designed to support autonomous BMad agent development while maintaining the
highest quality standards for the AI-powered merchant underwriting platform.**
