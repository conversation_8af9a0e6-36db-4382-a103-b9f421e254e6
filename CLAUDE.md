# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this
repository.

## 🚨 CRITICAL QUALITY INTEGRITY GUARANTEE 🚨

**ABSOLUTE PROHIBITION:** Claude Code SHALL NEVER game, manipulate, circumvent, or artificially inflate quality control systems, BMad validation, or any quality metrics. This includes but is not limited to:

- ❌ Removing files to avoid fixing TypeScript/ESLint errors
- ❌ Disabling validation rules to pass checks
- ❌ Manipulating test results or coverage metrics
- ❌ Making superficial changes to satisfy automated checkers
- ❌ Claiming quality achievements without genuine code improvements
- ❌ Any form of metric manipulation or validation system gaming

**COMMITMENT:** All quality improvements must be achieved through legitimate code fixes, proper type safety, genuine error resolution, and authentic architectural improvements. Quality scores must reflect actual code quality, not validation system manipulation.

**VIOLATION CONSEQUENCES:** Any attempt to game quality systems is a fundamental breach of software engineering integrity and is absolutely unacceptable.

## Project Overview

AI-Powered Merchant Underwriting Platform - A TypeScript monorepo using Turborepo with Next.js
frontend and Express backend for AI-powered financial underwriting.

## Essential Commands

### Development

```bash
# Start all development servers
npm run dev

# Start specific workspaces
npm run dev:frontend  # Next.js on :3000
npm run dev:backend   # Express on :5000
```

### Building & Testing

```bash
# Build entire monorepo
npm run build

# Run tests across workspaces
npm run test

# Type checking
npm run typecheck

# Linting
npm run lint
npm run lint:fix
```

### Database Operations

```bash
# Setup database with Docker services
npm run setup:db

# Prisma operations (run from apps/backend/)
npm run db:migrate    # Run migrations
npm run db:generate   # Generate Prisma client
npm run db:studio     # Open Prisma Studio on :5555
npm run db:reset      # Reset database
```

### Docker Services

```bash
# Start PostgreSQL, Redis, Ollama
npm run docker:up

# Stop services
npm run docker:down

# View logs
npm run docker:logs
```

### Quality Gates & BMad Validation

```bash
# Run BMad quality validation (required 85+ score)
npm run bmad:validate

# Auto-fix common issues
npm run bmad:self-heal

# Get current quality score
npm run bmad:quality-score

# Run CI validation pipeline
npm run ci:validate
```

### BMad Pre-Push Quality Gates (Automatic)

**Every `git push` automatically runs:**

1. **Lint Check** - ESLint validation across all packages
2. **Type Check** - TypeScript strict validation (zero errors)
3. **Tests** - All test suites must pass
4. **Build Check** - Ensures project builds successfully
5. **BMad Validation** - Code quality, architecture, security validation
6. **Auto Self-Heal** - Attempts to fix issues automatically if found

**Quality gates will block push if any validation fails.**

## Monorepo Architecture

### Workspace Structure

- `apps/frontend/` - Next.js 15 + React 19 + TypeScript + Tailwind + shadcn/ui
- `apps/backend/` - Express 4.21 + Prisma 6.12 + TypeScript + PostgreSQL
- `packages/shared/` - Shared TypeScript types and utilities
- `packages/ui/` - Shared UI components (shadcn/ui based)
- `packages/config/` - Shared configurations

### Technology Stack

- **Frontend**: Next.js 15, React 19, TypeScript 5.8, Tailwind CSS, shadcn/ui, Framer Motion
- **Backend**: Express 4.21, Prisma ORM, TypeScript 5.8, JWT auth, Redis caching
- **Database**: PostgreSQL 17 (Docker)
- **AI Services**: Ollama (local LLM) + OpenRouter (cloud)
- **Tooling**: Turborepo, ESLint 9, Prettier, Husky, lint-staged

### Service URLs

- Frontend: http://localhost:3000
- Backend API: http://localhost:5000
- Prisma Studio: http://localhost:5555
- PostgreSQL: localhost:5433
- Redis: localhost:6380
- Ollama: http://localhost:11434

## Development Workflow

### TypeScript Strict Policy

- Zero tolerance for TypeScript errors
- Strict configuration enforced
- All code must pass `npm run typecheck`
- Pre-commit hooks validate TypeScript

### Quality Requirements

- ESLint compliance with project config
- Prettier formatting enforced
- BMad quality score 85+ required
- All tests must pass before deployment

### Key Files to Understand

- `turbo.json` - Turborepo task configuration
- `docker-compose.yml` - Development services
- `apps/backend/prisma/schema.prisma` - Database schema
- `package.json` scripts - All available commands
- `.husky/pre-push` - Pre-push validation hooks

### BMad Integration

This project uses BMad (autonomous AI development agents) with:

- **Pre-push quality validation** - Automatic quality gates on every git push
- **Auto-healing for common issues** - Self-fixing of lint, format, TypeScript errors
- **Quality scoring system (0-100)** - Must maintain 85+ score for deployment
- **Context preservation for AI agents** - Agent teams maintain project understanding
- **Specialized agent teams** - Frontend, Backend, Integration, Platform teams
- **Local-first strategy** - Issues caught locally to save GitHub Actions minutes

### Adding New Features

1. Understand existing patterns in relevant workspace
2. Check shared packages for reusable components/utilities
3. Follow TypeScript strict policy (zero errors tolerance)
4. Run `npm run bmad:validate` before committing (85+ score required)
5. Use BMad commit message format with agent signatures
6. Quality gates will automatically run on `git push`
7. Use `npm run bmad:self-heal` to auto-fix common issues

### BMad Agent Commit Format

```
feat: implement user authentication

BMad-Agent: auth-specialist
BMad-Validation: ✅ lint, typecheck, tests
BMad-Quality-Score: 95/100
```

### Environment Setup

Required environment variables in `.env`:

- `DATABASE_URL` for PostgreSQL connection
- `REDIS_URL` for Redis connection
- `OPENROUTER_API_KEY` for cloud AI services
- `JWT_SECRET` for authentication
- Service ports and Docker container names

Run `npm run setup:all` for complete environment setup.
