# Complete Self-Hosted Architecture

## Custom Authentication System

### JWT-Based Auth (No Auth0)

```typescript
// Custom authentication service
class AuthService {
  generateJWT(user: User): string {
    return jwt.sign({ userId: user.id, tenantId: user.tenantId }, process.env.JWT_SECRET, {
      expiresIn: '24h',
    });
  }

  async validatePassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  async generateTOTP(secret: string): Promise<string> {
    return speakeasy.totp({ secret, encoding: 'base32' });
  }
}
```

## Self-Hosted AI Stack

### Local LLM with Ollama

```yaml
# docker-compose.yml for AI services
services:
  ollama:
    image: ollama/ollama:latest
    volumes:
      - ./models:/root/.ollama
    ports:
      - '11434:11434'

  qdrant:
    image: qdrant/qdrant:v1.7.4
    ports:
      - '6333:6333'
    volumes:
      - ./qdrant_storage:/qdrant/storage
```

### Custom Conversational AI

```typescript
// Self-hosted conversational AI
class ConversationalAI {
  private ollama: Ollama;
  private vectorDB: QdrantClient;

  async processMessage(message: string, context: string[]): Promise<string> {
    // Use local Llama model instead of OpenAI
    const response = await this.ollama.generate({
      model: 'llama3:8b',
      prompt: this.buildPrompt(message, context),
      stream: false,
    });

    return response.response;
  }
}
```

## Self-Hosted Email System

### Postal Email Server

```yaml
# Self-hosted email with Postal
services:
  postal:
    image: postal/postal:latest
    environment:
      - POSTAL_DATABASE_URL=************************************/postal
    ports:
      - '25:25'
      - '587:587'
    volumes:
      - ./postal-config:/config
```

## Self-Hosted Document Processing

### OCR and Document Analysis

```typescript
// Self-hosted document processing
class DocumentProcessor {
  async processDocument(file: Buffer): Promise<DocumentData> {
    // Use Tesseract for OCR
    const text = await tesseract.recognize(file, 'eng');

    // Use local ML models for classification
    const classification = await this.classifyDocument(text);

    // Use local fraud detection
    const fraudScore = await this.detectFraud(file, text);

    return { text, classification, fraudScore };
  }
}
```

## Self-Hosted Infrastructure

### Complete Docker Stack

```yaml
# Complete self-hosted infrastructure
version: '3.8'
services:
  # Application
  app:
    build: .
    ports:
      - '3000:3000'
    depends_on:
      - postgres
      - redis
      - ollama
      - qdrant

  # Database
  postgres:
    image: postgres:17.2
    environment:
      POSTGRES_DB: underwriting_platform
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data

  # Cache & Sessions
  redis:
    image: redis:7.4.1
    volumes:
      - redis_data:/data

  # Object Storage
  minio:
    image: minio/minio:latest
    command: server /data --console-address ":9001"
    ports:
      - '9000:9000'
      - '9001:9001'
    volumes:
      - minio_data:/data

  # AI Services
  ollama:
    image: ollama/ollama:latest
    volumes:
      - ollama_models:/root/.ollama

  qdrant:
    image: qdrant/qdrant:v1.7.4
    volumes:
      - qdrant_data:/qdrant/storage

  # Monitoring
  grafana:
    image: grafana/grafana:11.3.1
    ports:
      - '3001:3000'
    volumes:
      - grafana_data:/var/lib/grafana

  prometheus:
    image: prom/prometheus:v2.55.1
    ports:
      - '9090:9090'
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus

volumes:
  postgres_data:
  redis_data:
  minio_data:
  ollama_models:
  qdrant_data:
  grafana_data:
  prometheus_data:
```
