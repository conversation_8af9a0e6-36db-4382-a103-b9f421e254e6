---

### **Product Requirements Document (PRD): AI-Powered Underwriting-as-a-Service (UaaS) Platform V1**

#### **Section 1 of 6: Goals and Background Context**

##### **Goals**
* Reduce merchant application abandonment by a target of 40% through a superior, AI-guided onboarding experience.
* Decrease underwriting operational costs by 50% for tenants.
* Reduce the average time-to-decision for 80% of merchant applications to under one hour.
* Achieve an 80% straight-through processing (fully automated decision) rate for all submitted applications.
* V1 will provide a frictionless adoption path for new tenants by supporting parallel "shadow mode" operations, enabling phased activation of automation features, and offering simple data import tools, thereby minimizing the operational risk of migrating from legacy manual processes.

##### **Background Context**
The payments industry is at an inflection point where legacy, manual underwriting processes are a direct impediment to growth for ISOs, ISVs, and MSPs. These organizations require a technology-first solution to manage escalating fraud and compliance risks while meeting merchant demands for speed and efficiency. This PRD outlines the requirements for a V1 SaaS platform that addresses this need by providing an AI-driven, end-to-end onboarding and underwriting engine.

##### **Change Log**
| Date | Version | Description | Author |
| :--- | :--- | :--- | :--- |
| 2025-07-19 | 1.0 | Initial draft of PRD from Project Brief and Vision Document. | John (PM) |

#### **Section 2 of 6: Requirements**

##### **Functional Requirements (FR)**
1.  **FR1**: The system shall provide a secure, public-facing portal for merchant applicants to register and verify an account via email.
2.  **FR2**: The applicant portal shall feature an AI-Assisted conversational UI for application submission.
3.  **FR3**: The applicant portal shall provide a traditional form-based UI as an alternative to the AI assistant.
4.  **FR4**: The system shall support seamless toggling between the AI and form views without data loss.
5.  **FR5**: The applicant portal shall display a visual tracker for the application's current status.
6.  **FR6**: The applicant portal shall include a secure messaging center for communication with the tenant's underwriters.
7.  **FR7**: The applicant portal shall provide a vault for applicants to upload and view their required documents.
8.  **FR8**: The system shall provide a tenant-facing portal with a dashboard to view and manage their applicant pipeline.
9.  **FR9**: The tenant portal shall allow for the filtering and searching of applicants.
10. **FR10**: The tenant portal shall provide a case management view displaying all aggregated data and AI risk analysis for a single applicant.
11. **FR11**: The system shall allow authorized tenant users to perform decisioning actions (Approve, Decline, Request Information).
12. **FR12**: The system shall provide a UI for Tenant Admins to create and manage custom HITL checkpoint rules.
13. **FR13**: The system shall be capable of sending event-driven webhooks to tenant-specified URLs.
14. **FR14**: The system shall expose a private REST API for tenants to programmatically manage applicants.

##### **Non-Functional Requirements (NFR)**
1.  **NFR1**: The system must be a multi-tenant architecture with strict logical data segregation between tenants.
2.  **NFR2**: The system must maintain a complete and immutable audit trail of all actions and decisions made on an application, by both AI agents and human users.
3.  **NFR3**: The AI-driven decisioning process, from submission to an automated decision or human escalation, must complete in under 5 minutes for 90% of applications.
4.  **NFR4**: The UI for both applicant and tenant portals must be responsive and fully functional on current versions of major web browsers (Chrome, Firefox, Safari) on both desktop and mobile devices.
5.  **NFR5**: All sensitive data must be encrypted in transit (TLS 1.2+) and at rest (AES-256).
6.  **NFR6**: The application's hosting environment must be architected to achieve Level 1 PCI DSS certification.
7.  **NFR7**: The application software must be designed and built to meet the Payment Application Data Security Standard (PA-DSS), GDPR, SOC 2, and COPPA standards.

#### **Section 3 of 6: User Interface Design Goals**

##### **Overall UX Vision**
The platform's user experience must be defined by three core principles: clarity, efficiency, and trust. For the merchant applicant, the interface will be a guided, frictionless experience that reduces the cognitive load of a complex application. For the tenant, the interface will be a data-rich, powerful, and intuitive command center for managing their entire underwriting pipeline with speed and confidence.

##### **Key Interaction Paradigms**
* **Conversational UI**: The AI-assisted path for applicants will be the primary, guided experience.
* **Progressive Disclosure Wizard**: The traditional form path for applicants, and for complex configuration settings for tenants, will break down tasks into logical, manageable steps.
* **Dashboard & Widgets**: The home screen for both applicants and tenants will be a dashboard that provides an immediate, at-a-glance summary of status, tasks, and key metrics.
* **Kanban / List Views**: The tenant's sales pipeline will be a flexible view that can be toggled between a visual Kanban board and a dense list for efficient management.

##### **Core Screens and Views**
* **Applicant Portal**: Registration/Login, Main Dashboard, The Application View (with AI/Form toggling), Secure Messaging Center, Document Vault.
* **Tenant Portal**: Login, Sales Pipeline Dashboard, Case Management View, HITL Rules Engine UI, User Management.

##### **Accessibility**
The platform will adhere to **WCAG 2.1 AA** standards to ensure it is usable by people with a wide range of disabilities.

##### **Branding**
As a white-label platform, the entire user-facing interface must be fully themeable by the tenant, including Logo, Color Palette, and Fonts.

##### **Target Device and Platforms**
The platform will be a **Web Responsive** application, designed with a mobile-first approach for the applicant portal and a desktop-first approach for the data-dense tenant portal.

#### **Section 4 of 6: Technical Assumptions**

##### **Repository Structure: Monorepo**
The project will be developed within a single monorepo to simplify code sharing, dependency management, and build processes.

##### **Service Architecture: Self-Hosted Containerized Services**
The platform will be built as a set of self-hosted, self-managed, containerized services (using Docker) running within the user's existing Level 1 PCI DSS certified environment. All core components, including the database (e.g., self-managed PostgreSQL on dedicated servers) and the user authentication service, will be built and hosted in-house to maintain full control, avoid cloud provider lock-in, and integrate with the existing certified infrastructure.

##### **Testing Requirements: Full Testing Pyramid**
The project will require a comprehensive testing strategy that includes unit, integration, and end-to-end (E2E) tests to ensure reliability and support the PA-DSS validation process.

##### **Additional Technical Assumptions and Requests**
* **Compliance & Security Mandates**: The application's hosting environment will be the user's existing Level 1 PCI DSS certified infrastructure. The application software must be designed and built to meet PA-DSS, GDPR, SOC 2, and COPPA standards.
* **Required Third-Party API Integrations**: The platform will be built with mock implementations of all third-party APIs (KYB, KYC, etc.), with the architecture supporting integration with live services once contracts are in place.

#### **Section 5 of 6: Epic List**

**Epic 1: The Foundational Platform & Self-Hosted Services**
* **Goal**: Establish the secure infrastructure within the existing PCI environment, build and deploy the production-hardened database and authentication services, define the core data models, and implement a basic, end-to-end application submission flow.

**Epic 2: The Intelligence Core & Mock Service Layer**
* **Goal**: Build the integration interfaces (service connectors) for all required third-party data sources, implement mock versions of these services for development and testing, and deploy the initial AI Risk Engine and the data-rich Case Management View.

**Epic 3: The AI-Powered Applicant Experience**
* **Goal**: Build the complete, AI-assisted conversational onboarding portal for merchant applicants to dramatically reduce friction and improve data quality.

**Epic 4: Tenant Empowerment & Platform Integration**
* **Goal**: Deliver advanced tenant-facing features, including the configurable HITL Rules Engine and adoption tools, and expose the platform's capabilities via Webhooks and the private REST API.

#### **Section 6 of 6: Epic Details**

##### **Epic 1: The Foundational Platform & Self-Hosted Services**
* **Expanded Goal**: This foundational epic establishes the secure infrastructure within the existing PCI environment, builds and deploys the production-hardened database and authentication services, defines the core data models, and implements a basic, end-to-end "steel thread" of the core workflow.
* **Stories**:
    * **1.1: Project Setup & Infrastructure Integration**: Set up the monorepo and integrate a CI/CD pipeline with the existing PCI-compliant container orchestration environment.
    * **1.2: Production-Hardened PostgreSQL Instance**: Build and configure a production-grade, self-hosted PostgreSQL instance to meet PCI DSS standards.
    * **1.3: Custom Authentication Service**: Build a secure, self-hosted authentication service for user identity management.
    * **1.4: Multi-Tenant Schema & Core Models**: Implement the core database schema for multi-tenancy on the new PostgreSQL instance.
    * **1.5: Applicant Portal UI & Registration Flow**: Create the applicant-facing UI to integrate with the custom authentication service.
    * **1.6: Tenant Portal Scaffolding & User Invites**: Create the tenant-facing UI for login and user management, integrating with the custom auth service.
    * **1.7: End-to-End Application "Steel Thread"**: Implement a basic application form and submission flow to prove the end-to-end functionality of the self-hosted stack.

##### **Epic 2: The Intelligence Core & Mock Service Layer**
* **Expanded Goal**: This epic builds the platform's intelligence core. We will build the service connectors for all third-party data sources, implement mock versions of these services first, and build the underwriting workbench and a foundational AI risk model without being blocked by external vendor contracts.
* **Stories**:
    * **2.1: Service Connector & Mocking Framework**: Define a standardized framework for creating third-party service connectors and their mock implementations.
    * **2.2: KYB/KYC Service Connector & Mock**: Create the service connector and mock implementation for a KYB/KYC provider.
    * **2.3: Credit Check Service Connector & Mock**: Create the service connector and mock implementation for a credit bureau provider.
    * **2.4: AML/Sanctions Service Connector & Mock**: Create the service connector and mock implementation for an AML/Sanctions data provider.
    * **2.5: Foundational AI Risk Engine**: Create a service that ingests data from the mock connectors and produces a V1 risk score and reason codes.
    * **2.6: Data-Rich Case Management View**: Build the comprehensive "workbench" UI for an underwriter to view all aggregated mock data and the AI risk analysis.

##### **Epic 3: The AI-Powered Applicant Experience**
* **Expanded Goal**: This epic focuses on creating a world-class front-end experience for the merchant applicant, making the complex application process feel simple, guided, and transparent.
* **Stories**:
    * **3.1: Applicant Dashboard & Status Tracker**: Build the central dashboard for a logged-in applicant, featuring a clear, visual status tracker.
    * **3.2: AI Conversational UI Framework**: Build the reusable framework for the chat-like user interface.
    * **3.3: AI-Guided Data Collection Flow**: Implement the conversational AI to guide a user through the application, saving data to the backend.
    * **3.4: Seamless View Toggling**: Implement the feature to switch between the AI and a traditional form view without data loss.
    * **3.5: Secure Messaging Center UI**: Build the front-end for the secure messaging feature for communication with underwriters.
    * **3.6: Document Vault UI**: Build the UI for applicants to view the status of their uploaded documents.

##### **Epic 4: Tenant Empowerment & Platform Integration**
* **Expanded Goal**: This final V1 epic delivers the key features that empower tenants and make the platform a truly integrated part of their workflow, transforming the product from a powerful tool into an extensible platform.
* **Stories**:
    * **4.1: HITL Rules Engine UI**: Build the user interface for Tenant Admins to create and manage custom escalation rules.
    * **4.2: HITL Rules Engine Backend**: Build the backend service that executes the tenant-defined rules against applications.
    * **4.3: Tenant Adoption Tools (Shadow Mode & Phased Activation)**: Implement the UI and backend logic for the migration tools.
    * **4.4: Webhook Management UI**: Build the UI for tenants to manage their webhook subscriptions.
    * **4.5: Webhook Delivery Service**: Build the backend service that securely sends event payloads to subscribed URLs.
    * **4.6: Private REST API (Core Endpoints & Auth)**: Build the initial set of API endpoints for programmatic access, secured by API Keys.
    * **4.7: API Key Management UI**: Build the UI for Tenant Admins to securely generate and revoke API keys.

---
