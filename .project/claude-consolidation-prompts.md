# Exact Claude Prompts for Document Consolidation

## Prompt 1: PRD Creation

```
I'm building an AI-powered MERCHANT UNDERWRITING platform and have 14 source documents (including actual-project-personas.md). I need you to create a comprehensive Product Requirements Document (PRD) that consolidates all business requirements.

SOURCE DOCUMENTS:
[Attach all 14 documents with @ syntax, including @project/actual-project-personas.md]

PROJECT SCOPE:
- AI-powered merchant underwriting platform (NOT loan processing)
- Multi-tenant SaaS serving financial services companies
- Merchant applicants get AI-guided application experience
- Tenant admins get underwriting management tools
- System administrators manage multi-tenant platform

REQUIREMENTS:
- 100% self-hosted (no 3rd party services like Auth0, OpenAI, Stripe)
- Multi-tenant SaaS architecture
- Conversational AI interface using local LLMs (Ollama + Llama)
- Document processing with OCR and ML for merchant applications
- Real-time underwriting decision engine
- Comprehensive tenant management portal
- HITL (Human-in-the-Loop) configuration capabilities

PRD STRUCTURE NEEDED:
1. Executive Summary & Vision (merchant underwriting focus)
2. User Personas & Journeys (from actual-project-personas.md)
3. Feature Requirements organized into Epics
4. Acceptance Criteria for each feature
5. Success Metrics & KPIs
6. Technical Constraints (self-hosted requirement)

OUTPUT: Single comprehensive PRD document ready for BMad story creation process.
```

## Prompt 2: Architecture Creation

```
Using the PRD and technical documents, create a comprehensive architecture document for the AI-powered MERCHANT UNDERWRITING platform.

CONTEXT:
- Self-hosted AI platform with local LLMs (Ollama + Llama models)
- Custom JWT authentication system
- Multi-tenant merchant underwriting workflow
- Complete infrastructure control (no 3rd party services)

ARCHITECTURE DOCUMENT NEEDED:
1. System Overview & High-Level Architecture
2. Complete Self-Hosted Tech Stack (PostgreSQL, Redis, MinIO, Ollama, Qdrant, etc.)
3. Multi-Tenant Component Architecture & Interactions
4. Data Models & Database Schema (merchants, tenants, applications, decisions)
5. API Specifications (REST + WebSocket for real-time updates)
6. Security Architecture (custom JWT auth, tenant isolation)
7. AI/ML Architecture (Ollama + local Llama models for conversational AI)
8. Document Processing Pipeline (OCR, classification, fraud detection)
9. Underwriting Decision Engine Architecture
10. Infrastructure & Deployment (Docker Swarm, self-hosted monitoring)
11. Tenant Management & HITL Configuration System
12. Scalability & Performance

CRITICAL: Everything must be self-hosted - no external services (no Auth0, OpenAI, Stripe, SendGrid, etc.)

OUTPUT: Complete architecture document ready for BMad development process.
```

## Prompt 3: Document Validation

```
Review both the PRD and Architecture documents to ensure:

1. Complete alignment between business requirements and technical architecture
2. All self-hosted constraints are properly addressed
3. No references to 3rd party services (Auth0, OpenAI, Stripe, etc.)
4. Clear epic structure ready for story creation
5. Technical specifications detailed enough for autonomous development
6. Proper multi-tenant architecture with tenant isolation
7. Merchant underwriting workflow properly defined
8. AI conversational interface architecture is complete
9. HITL and shadow mode capabilities are included
10. Integration points between components are clear

Focus areas:
- Merchant application journey (AI-guided)
- Tenant admin dashboard and configuration
- System admin multi-tenant management
- Self-hosted AI stack integration
- Document processing and decision engine

Identify any gaps or inconsistencies that need to be resolved before BMad activation.
```
