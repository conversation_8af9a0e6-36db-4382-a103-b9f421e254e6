# Complete BMad Activation - Full AI Underwriting Platform

## Step 1: Load Complete Project Context

**Type this to activate orchestrator with FULL context:**

```
*bmad-orchestrator

Load complete project context:
- PRD: @prd.md (Epic 3: AI-Powered Applicant Experience)
- Specs: @manu-ola-specs.md (AI-Assisted Underwriting Workflow)
- UX Spec: @front-end-spec.md (Dual interface design)
- Market Analysis: @comprehensive-market-analysis.md
- Roadmap: @product-roadmap-feature-prioritization.md

Key Missing Components I Need Built:
1. AI Conversational Engine for application guidance
2. Dual Interface (AI Chat + Traditional Forms) with seamless switching
3. Document Processing AI with OCR and fraud detection
4. Tenant HITL Rules Engine with visual configuration
5. Real-time secure messaging system
6. Advanced analytics dashboard with predictive insights
7. Complete external API integration framework
8. Shadow mode and phased automation tools
```

## Step 2: Agent Team Deployment (All Specialized Roles)

**The orchestrator will deploy these specialized agents:**

### Core Platform Agents

- **architect**: Multi-tenant platform architecture
- **dev**: Full-stack implementation
- **qa**: Comprehensive testing strategy

### AI/ML Specialized Agents

- **data**: ML models, AI decision engine, continuous learning
- **nlp-expert**: Conversational AI, document processing
- **ml-ops**: Model deployment, monitoring, optimization

### UX/Frontend Agents

- **ux-expert**: Dual interface design, user experience
- **frontend-dev**: React/Next.js implementation
- **mobile-dev**: Mobile-responsive interfaces

### Integration & DevOps Agents

- **integration-expert**: External API framework
- **devops**: Infrastructure, deployment, monitoring
- **security**: Compliance, encryption, audit trails

### Business Logic Agents

- **pm**: Product strategy, feature prioritization
- **business-analyst**: Workflow optimization, requirements
- **compliance-expert**: Regulatory requirements, audit preparation

## Step 3: Parallel Development Streams

**Stream A: AI Conversational Experience**

```
Agents: nlp-expert + ux-expert + frontend-dev
Timeline: Months 1-4
Deliverables:
- Natural language processing engine
- Conversational UI framework
- AI-guided application flow
- Seamless interface switching
- Real-time validation system
```

**Stream B: AI Decision & Document Processing**

```
Agents: data + ml-ops + integration-expert
Timeline: Months 1-6
Deliverables:
- Multi-model AI decision engine
- Document OCR and fraud detection
- External API integration framework
- Real-time risk scoring
- Explainable AI system
```

**Stream C: Tenant Management Platform**

```
Agents: dev + ux-expert + business-analyst
Timeline: Months 2-5
Deliverables:
- HITL Rules Engine with visual builder
- Case management dashboard
- Advanced analytics and reporting
- Shadow mode implementation
- White-label customization system
```

**Stream D: Communication & Workflow**

```
Agents: dev + security + integration-expert
Timeline: Months 2-4
Deliverables:
- Real-time secure messaging
- Workflow automation engine
- Notification system
- Audit trail implementation
- Status tracking system
```

## Step 4: Complete Autonomous Workflow

**Activate full autonomous development:**

```
*workflow-guidance

Project: Complete AI-Powered Underwriting Platform
Scope: All requirements from PRD, specs, and UX documents
Timeline: 18-month market leadership with full feature set
Team: Complete specialized AI agent team
Mode: Autonomous 24/7 development

Critical Features:
1. AI Conversational Application Interface
2. Dual UI (Chat + Forms) with seamless switching
3. AI Document Processing with OCR
4. Tenant HITL Rules Configuration
5. Real-time Secure Messaging
6. Advanced Predictive Analytics
7. Complete External API Integration
8. Shadow Mode & Phased Automation
9. Multi-tenant White-label System
10. Explainable AI with Compliance Reporting

*yolo
*party-mode
*workflow ai-underwriting-platform-complete
```
