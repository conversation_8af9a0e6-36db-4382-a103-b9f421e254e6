### **UI/UX Specification**

#### **Introduction**

This document defines the user experience goals, information architecture, user flows, and visual
design specifications for the AI-Powered Underwriting-as-a-Service (UaaS) Platform's user interface.
It serves as the foundation for visual design and frontend development, ensuring a cohesive and
user-centered experience.

#### **Overall UX Goals & Principles**

##### **Target User Personas**

- **The Merchant Applicant**: A user who may be time-poor and unfamiliar with underwriting. Their
  primary goal is to complete the application as quickly and easily as possible.
- **The Tenant User (Underwriter/Admin)**: A power user who will be in the platform daily. Their
  primary goal is to process a high volume of applications with maximum efficiency and accuracy.

##### **Usability Goals**

- **For the Applicant**:
  - **Ease of Learning**: An applicant should be able to complete the entire application in their
    first session without needing a tutorial or help document.
  - **Error Prevention**: The interface should proactively prevent mistakes and provide clear
    guidance to correct them.
  - **High Confidence**: Applicants should feel reassured throughout the process that their
    sensitive data is secure and their application is progressing.
- **For the Tenant**:
  - **Efficiency of Use**: An expert tenant user should be able to navigate their pipeline, analyze
    a case, and make a decision with the minimum number of clicks and context switches.
  - **Low Error Rate**: The clarity of the data presentation should minimize the risk of human
    underwriters making mistakes.

##### **Core Design Principles**

1.  **Clarity Through Simplicity (Applicant-Facing)**: We will prioritize a clean, uncluttered
    interface that uses progressive disclosure to reduce cognitive load and guide the user through
    one task at a time.
2.  **Efficiency Through Density (Tenant-Facing)**: For our expert tenant users, we will prioritize
    an organized, data-dense interface that makes complex information easy to scan and act upon.
3.  **Guidance & Assurance**: The system will provide constant, clear feedback on user actions and
    transparently communicate the status of processes to build user trust and confidence.
4.  **Intuitive by Default**: We will leverage familiar, best-practice UI patterns and maintain
    strict consistency across both portals to ensure the platform feels predictable and easy to
    learn for all users.

#### **Information Architecture (IA)**

##### **Site Map / Screen Inventory**

This diagram shows the relationship between all the major screens and views in the V1 platform for
both the Applicant and Tenant portals.

```mermaid
graph TD
    subgraph Public
        A[/"Homepage/Login"] --> B["/register (Applicant Registration)"];
    end

    subgraph Applicant Portal
        C["/app (Dashboard)"] --> D["/app/application (Application View)"];
        C --> E["/app/messages (Messaging Center)"];
        C --> F["/app/documents (Document Vault)"];
        C --> G["/app/profile (Profile/Settings)"];
    end

    subgraph Tenant Portal
        H["/tenant (Pipeline Dashboard)"] --> I["/tenant/case/{id} (Case Management View)"];
        H --> J["/tenant/rules (Rules Engine)"];
        H --> K["/tenant/users (User Management)"];
        H --> L["/tenant/settings (Tenant Settings)"];
    end

    A -- Applicant Login --> C;
    A -- Tenant Login --> H;
```

##### **Navigation Structure**

- **Applicant Portal Navigation**: The primary navigation will be simple and persistent, likely in a
  top header or sidebar, containing links to:
  - Dashboard
  - Messages
  - Documents
  - Profile
- **Tenant Portal Navigation**: The primary navigation will provide access to the main work areas. A
  breadcrumb system will be used for navigating into specific items like a case.
  - **Primary Navigation**: Pipeline, Rules, User Management, Settings.
  - **Breadcrumbs**: A breadcrumb trail will be visible on nested pages (e.g.,
    `Home > Pipeline > Case #12345`).

#### **User Flows**

##### **User Flow: New Merchant Application Submission (Revised)**

- **User Goal**: A new merchant wants to successfully create an account, complete their application
  (including uploading document images), consent to necessary disclosures, and submit it for review.
- **Flow Diagram**:
  ```mermaid
  graph TD
      A[Public Login Page] --> B[Clicks Register];
      B --> C[Fills Registration Form <br>(Email, Password)];
      C --> D[System Sends Verification Email];
      D --> E{User Clicks <br>Verification Link};
      E --> F[Logs In];
      F --> G[Welcome Screen <br> Greets User by Name];
      G --> H{Chooses Onboarding Path};
      H -- AI-Assisted --> I[AI Guides User <br> Through Application & <br> Document Image Uploads];
      H -- Traditional Form --> J[User Fills Out <br> Multi-Step Form & <br> Uploads Document Images];
      I --> JA[Review Disclosures & Consent];
      J --> JA;
      JA --> K{User Consents?};
      K -- Yes --> L[Submits Application];
      K -- No --> M[Submission Blocked];
      L --> N{System Runs <br> Automated Pre-Screening};
      N -- Pass --> O[Success Confirmation Page];
      O --> P[Redirect to Applicant Dashboard];
      P --> Q[Status Tracker Shows 'In Review'];
      N -- Fail --> R[Declined/Hold Page <br> (Provides Reason & Next Steps)];
  ```

##### **User Flow: Tenant Reviews an Escalated Case**

- **User Goal**: A tenant underwriter wants to efficiently review all the AI-gathered data for an
  application that was flagged for manual review, and make a confident, auditable final decision.
- **Flow Diagram**:

  ```mermaid
  graph TD
      A[Tenant Logs In] --> B[Views Pipeline Dashboard];
      B --> C[Selects an Application <br> with 'In Review' status];
      C --> D[Case Management View Loads];
      D --> E[Reviews Consolidated Data <br> (Applicant Info, KYC/KYB, Credit, AML, AI Score & Reasons)];
      E --> F{Decision: Sufficient Info?};
      F -- No --> G[Clicks 'Request More Info'];
      G --> H[Types message/request <br> in Secure Messaging UI];
      H --> I[Sends Message to Applicant];
      I --> J[Application Status updated to 'Action Required'];

      F -- Yes --> K{Decision: Approve?};
      K -- Yes --> L[Clicks 'Approve'];
      L --> M[Logs Decision & Updates Status to 'Approved'];
      M --> N[Applicant & Webhooks Notified];
      N --> O[Returns to Pipeline Dashboard];

      K -- No --> P[Clicks 'Decline'];
      P --> Q[System Prompts for <br> Mandatory Decline Reason];
      Q --> R[Enters Reason];
      R --> S[Logs Decision & Updates Status to 'Declined'];
      S --> N;

      J --> O;
  ```

#### **Wireframes & Mockups**

##### **Primary Design Files:**

- **Design Tool:** N/A. The conceptual wireframes below will be used to create prompts for AI
  generation tools (e.g., v0, Lovable).

##### **Screen: Tenant Case Management View**

- **Purpose**: To provide a tenant underwriter with a single, comprehensive view of all data and AI
  analysis for an escalated application, enabling a fast and confident decision.
- **Low-Fidelity Wireframe**:

  ```text
  [====================================================================================]
  | BREADCRUMB: / Pipeline / Case #12345 - John's Widgets Inc.                           |
  |------------------------------------------------------------------------------------|
  | APPLICANT NAME: John's Widgets Inc.                       [ Request Info ] [ Decline ] [ Approve ] |
  [====================================================================================]

  +--------------------------------------+ +-------------------------------------------+
  | KEY INFO                             | | TABS: [ Application Data | Documents | Notes/History ]      |
  |--------------------------------------| |-------------------------------------------|
  |                                      | |                                           |
  | [ Applicant Details Card ]           | | [ Content for the active tab is shown here. ]      |
  |  - Name: John's Widgets Inc.         | |                                           |
  |  - Status: In Review                 | |                                           |
  |  - Submitted: 2025-07-19             | |                                           |
  |                                      | |                                           |
  | [ AI Analysis Card ]                 | |                                           |
  |  - RISK SCORE: 65 (Medium)           | |                                           |
  |  - Reason Codes:                     | |                                           |
  |    - High Chargeback History         | |                                           |
  |    - Adverse Media Found             | |                                           |
  |                                      | |                                           |
  | [ Compliance Flags Card ]            | |                                           |
  |  - AML/Sanctions: Clear              | |                                           |
  |  - KYC/KYB Status: Verified          | |                                           |
  +--------------------------------------+ +-------------------------------------------+
  ```

- **AI Generator Prompt**:
  > "Create a case management dashboard for an underwriting application using React and Tailwind
  > CSS. The page should have a main header with a breadcrumb component on the top left. Below the
  > breadcrumb, display the applicant's name on the left and a group of three buttons on the right:
  > 'Request Info' (secondary style), 'Decline' (destructive style), and 'Approve' (primary style).
  > The main content area should be a two-column grid. The left column should be one-third of the
  > width and contain three stacked cards: 'Key Info', 'AI Analysis', and 'Compliance Flags'. The
  > right column should be two-thirds of the width and contain a tabbed interface with three tabs:
  > 'Application Data', 'Documents', and 'Notes/History'."

##### **Screen: Applicant Dashboard**

- **Purpose**: To provide a logged-in applicant with a clear, immediate overview of their
  application status and to highlight any actions they need to take.
- **Low-Fidelity Wireframe**:

  ```text
  [====================================================================================]
  | HEADER: Welcome, Jane!                                       [ Profile ] [ Log Out ] |
  [====================================================================================]

  +------------------------------------------------------------------------------------+
  |                                                                                    |
  | [ VISUAL STATUS TRACKER WIDGET ]                                                   |
  |                                                                                    |
  |   (✔) Account Verified -> (●) Application In Review -> ( ) Final Decision          |
  |   Last Update: July 19, 2025                                                       |
  |                                                                                    |
  +------------------------------------------------------------------------------------+

  +------------------------------------------------------------------------------------+
  |                                                                                    |
  | [ ACTION REQUIRED WIDGET (Only shown if needed) ]                                  |
  |                                                                                    |
  |   ! ACTION REQUIRED: The underwriting team has requested more information.         |
  |                                                                                    |
  |   "Please provide a bank statement from the last 30 days..." [ View Full Message ] |
  |                                                                                    |
  +------------------------------------------------------------------------------------+

  +----------------------------------+ +-----------------------------------------------+
  | [ RECENT MESSAGES WIDGET ]       | | [ MY DOCUMENTS WIDGET ]                       |
  |----------------------------------| |-----------------------------------------------|
  | > From: Underwriting Team        | | - articles_of_inc.pdf (Verified)              |
  |   "Welcome to the portal..."     | | - janes_id.jpg (Verified)                     |
  |                                  | |                                               |
  | [ View All Messages ]            | | [ Go to Document Vault ]                      |
  +----------------------------------+ +-----------------------------------------------+
  ```

- **AI Generator Prompt**:
  > "Create a modern and clean applicant dashboard using React and Tailwind CSS. The page should
  > have a main heading that says 'Welcome, [User Name]\!'. The layout should be a single column of
  > components. The first component is a visual status tracker, designed as a horizontal stepper
  > with three steps: 'Account Verified', 'Application In Review', and 'Final Decision'. Use icons
  > to show completed, active, and incomplete steps. The second component is an 'Action Required'
  > card with a prominent warning color border. It should only be visible when there is a pending
  > action. It contains a title, a short text snippet, and a primary action button. Below that,
  > create a two-column grid. The left column contains a 'Recent Messages' card with a preview of
  > the latest message and a link to view all messages. The right column contains a 'My Documents'
  > card that shows a list of uploaded documents and a link to the document vault."

#### **Component Library / Design System**

##### **Design System Approach**

The platform will **use an existing, best-in-class component library** rather than building one from
the ground up. Specifically, a **headless component library** (such as Radix UI or Headless UI) will
be paired with a utility-first CSS framework like **Tailwind CSS**. This approach provides maximum
speed, accessibility, and the visual flexibility required for a white-label product.

##### **Core Components**

The V1 platform will rely on the following foundational components: Buttons, Input Fields, Cards,
Tabs, Modals, Dropdowns, Notifications, and Data Tables.
