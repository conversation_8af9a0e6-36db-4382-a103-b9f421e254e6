# BMad Document Organization Process

## Step 1: Analyze Your 14 Documents

**First, let's see what you have:**

```bash
ls -la docs/
ls -la project/
```

**Your documents should include:**

- `project/actual-project-personas.md` (the correct personas)
- Business requirements docs
- Technical specifications
- Architecture/Infrastructure docs
- Self-hosted tech stack requirements

## Step 2: Create Master PRD Document

**In your separate Claude terminal, use the revised prompt above with all 14 docs**

**Key focus areas for PRD:**

- Merchant underwriting platform (not loan processing)
- AI-guided merchant application experience
- Tenant admin underwriting management
- Multi-tenant SaaS architecture
- 100% self-hosted constraints

## Step 3: Create Master Architecture Document

**Focus on:**

- Self-hosted AI stack (Ollama + Llama)
- Custom JWT authentication
- Multi-tenant data isolation
- Document processing pipeline
- Real-time decision engine
- Tenant configuration system

## Step 4: Expected Sharded Structure

**After consolidation and sharding:**

```
docs/
├── prd.md (master PRD)
├── architecture.md (master architecture)
├── prd/
│   ├── index.md
│   ├── epic-1-foundation-auth.md
│   ├── epic-2-merchant-application.md
│   ├── epic-3-ai-conversational-interface.md
│   ├── epic-4-underwriting-engine.md
│   ├── epic-5-tenant-management.md
│   └── epic-6-system-admin.md
└── architecture/
    ├── index.md
    ├── self-hosted-tech-stack.md
    ├── multi-tenant-architecture.md
    ├── ai-ml-architecture.md
    ├── data-models.md
    ├── api-specifications.md
    └── deployment-infrastructure.md
```

## Step 5: Activate BMad with Correct Context

```
*bmad-orchestrator

Project: AI-Powered Merchant Underwriting Platform
Architecture: 100% self-hosted multi-tenant SaaS
Core Users: Merchant applicants, Tenant admins, System administrators
AI Stack: Local Ollama + Llama models, Qdrant vector DB
Documentation: Complete sharded PRD and Architecture available

Request: Deploy full BMad team for merchant underwriting platform development
```
