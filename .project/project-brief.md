### **Project Brief (REVISED): AI-Powered Underwriting-as-a-Service (UaaS) Platform V1**

#### **Executive Summary**

This document outlines the project brief for the Version 1 launch of the AI-Powered
Underwriting-as-a-Service (UaaS) Platform. This V1 is a multi-tenant, white-label SaaS solution
designed to provide a complete, end-to-end automated underwriting workflow for the payments
ecosystem (ISOs, ISVs, MSPs). The V1 focuses on delivering a superior applicant experience through
an AI-guided portal, while empowering tenants with an efficient management dashboard, configurable
rules, a private API for deep integration, and critically, a **frictionless adoption path to migrate
from their legacy manual processes.**

#### **Problem Statement**

Independent Sales Organizations (ISOs), Merchant Service Providers (MSPs), and Independent Software
Vendors (ISVs) face immense pressure to accelerate merchant onboarding while managing complex risks.
Legacy, manual underwriting processes are a direct impediment to growth, creating high merchant
abandonment and significant operational overhead. Furthermore, the risk and complexity of switching
to a new, fully automated system can be a major barrier to adoption for these tenants.

#### **Proposed Solution (V1)**

The UaaS platform is a multi-tenant SaaS solution providing a complete, automated underwriting
workflow. V1 will feature an AI-assisted applicant portal to improve data quality and reduce
friction. For tenants, it will provide a secure dashboard to manage their application pipeline,
review AI-escalated cases, and configure custom Human-in-the-Loop checkpoints. The platform will
support a gradual and low-risk migration by enabling tenants to run the system in a parallel "shadow
mode" and incrementally activate automation features as their confidence grows. Integration will be
supported via event-driven webhooks and a private, key-based REST API for early partners.

#### **Target Users**

- **Primary User Segment: The Tenant**: ISOs, MSPs, and ISVs who will be the direct customers.
- **Secondary User Segment: The Merchant Applicant**: The end-user business applying for services
  from a Tenant.

#### **Goals & Success Metrics (V1)**

1.  Reduce merchant application abandonment by a target of 40% through a superior, AI-guided
    onboarding experience.
2.  Decrease underwriting operational costs by 50% for tenants.
3.  Reduce the average time-to-decision for 80% of merchant applications to under one hour.
4.  Achieve an 80% straight-through processing (fully automated decision) rate for all submitted
    applications.
5.  **Provide a frictionless adoption path for new tenants by supporting parallel "shadow mode"
    operations, enabling phased activation of automation features, and offering simple data import
    tools, thereby minimizing the operational risk of migrating from legacy manual processes.**

#### **V1 Scope**

##### **Pillar 1: The Generative Experience (GX) Layer**

- **Applicant Dashboard**: Central hub showing status, tasks, and messages.
- **AI-Assisted Onboarding**: Conversational AI guides applicants through the form.
- **Seamless View Toggling**: Instantly switch between AI and traditional form views with no data
  loss.
- **Visual Status Tracker**: Graphical timeline of the application's progress.
- **Secure Messaging Center**: Asynchronous, auditable communication between applicant and tenant.
- **Secure Document Vault**: A central place for applicants to upload and manage their required
  documents.

##### **Pillar 2: The "Glass Box" Intelligence Engine**

- **Core AI Risk Model**: A foundational machine learning model that analyzes verified data to
  produce a risk score.
- **Explainable AI (XAI) Decision Narratives**: The `@agent` will generate human-readable
  explanations for its risk assessments.

##### **Pillar 3: The Proactive Risk & Compliance Framework**

- **Foundational Continuous Monitoring**: The `@agent` will perform post-onboarding monitoring of
  merchant transaction behavior to flag significant anomalies.

##### **Pillar 4: The Tenant Empowerment Platform**

- **Simple Roles & Permissions**: Predefined user roles (`Admin`, `Underwriter`, `Viewer`).
- **Configurable Human-in-the-Loop (HITL) Checkpoints**: A UI for Tenant Admins to create custom
  rules that automatically escalate certain applications for human review.
- **Tenant Adoption Tools**:
  - **"Shadow Mode" Capability**: Ability to process an application in the system in parallel with a
    tenant's existing manual process.
  - **Phased Automation Controls**: Toggles in the tenant dashboard to incrementally enable features
    like AI scoring and auto-approval.
  - **Data Import Wizard**: A tool to import in-flight applicant data via CSV.

##### **Pillar 5: The Integration & Interoperability Framework**

- **Event-Driven Webhooks**: A secure system for tenants to subscribe to real-time event
  notifications.
- **Private REST API**: A secure, versioned, and well-documented API for early ISV partners and
  enterprise clients, using API Key authentication for V1.

##### **Out of Scope for V1**

- Real-Time Chat with underwriters.
- The "Agent Creator" Studio and custom tenant workflow builders.
- The official Zapier/Make application.
- The public-facing Developer Portal with OAuth 2.0 and multiple SDKs.
- Advanced AI models like Graph Neural Networks for fraud ring detection.
