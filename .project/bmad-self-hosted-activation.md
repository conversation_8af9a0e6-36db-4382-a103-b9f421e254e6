# BMad Activation - Complete Self-Hosted Platform

## Step 1: Activate with Self-Hosted Context

```
*bmad-orchestrator

Project: AI-Powered Underwriting Platform
Architecture: 100% SELF-HOSTED - NO 3rd party services
Tech Stack:
- Custom JWT authentication (NO Auth0)
- Local Ollama + Llama models (NO OpenAI)
- Self-hosted Qdrant vector DB (NO Pinecone)
- Self-hosted Postal email (NO SendGrid)
- Self-hosted MinIO storage (NO AWS S3)
- Self-hosted monitoring (Grafana + Prometheus)
- Direct bank API integration (NO Stripe)

Critical Requirement: Everything must run on our own infrastructure
```

## Step 2: Self-Hosted Development Priorities

**Infrastructure First:**

1. Docker Swarm orchestration setup
2. PostgreSQL 17 with replication
3. Redis cluster for sessions/cache
4. MinIO for document storage
5. Traefik for load balancing

**Custom Services:**

1. JWT-based authentication system
2. Local LLM integration with Ollama
3. Self-hosted vector search with Qdrant
4. Custom email system with Postal
5. Self-hosted monitoring stack

**AI Components:**

1. Local Llama 3 for conversational AI
2. Self-hosted document OCR with Tesseract
3. Custom ML models with TensorFlow
4. Local fraud detection algorithms
5. Self-hosted vector embeddings

## Step 3: Deploy Self-Hosted Team

```
*workflow self-hosted-ai-platform

Team Focus:
- DevOps: Self-hosted infrastructure setup
- Security: Custom authentication & encryption
- AI: Local LLM integration and optimization
- Backend: Custom services development
- Integration: Direct API integrations (no 3rd party)

Mode: Autonomous development with self-hosted constraints
```
