# Self-Hosted Tech Stack - AI Underwriting Platform (December 2024)

## Core Self-Hosted Infrastructure

| Technology       | Version | Purpose             | Self-Hosted Approach                |
| ---------------- | ------- | ------------------- | ----------------------------------- |
| **PostgreSQL**   | 17.2    | Primary Database    | Self-hosted with replication        |
| **Redis**        | 7.4.1   | Cache/Sessions      | Self-hosted cluster                 |
| **MinIO**        | Latest  | Object Storage      | S3-compatible self-hosted           |
| **Traefik**      | 3.1     | Load Balancer/Proxy | Self-hosted reverse proxy           |
| **Docker Swarm** | 27.3.1  | Orchestration       | Self-hosted container orchestration |

## Authentication & Security (Self-Hosted)

| Technology                | Version | Purpose          | Implementation                  |
| ------------------------- | ------- | ---------------- | ------------------------------- |
| **Custom JWT Auth**       | -       | Authentication   | Self-built with Node.js         |
| **bcrypt**                | 5.1.1   | Password Hashing | Self-hosted password security   |
| **node-2fa**              | 2.0.3   | Two-Factor Auth  | Self-hosted TOTP implementation |
| **Helmet**                | 8.0.0   | Security Headers | Self-hosted security middleware |
| **rate-limiter-flexible** | 5.0.3   | Rate Limiting    | Self-hosted rate limiting       |

## AI/ML Stack (Self-Hosted)

| Technology       | Version | Purpose               | Self-Hosted Approach        |
| ---------------- | ------- | --------------------- | --------------------------- |
| **Ollama**       | 0.1.17  | Local LLM Runtime     | Self-hosted LLM inference   |
| **Llama 2/3**    | Latest  | Conversational AI     | Local model deployment      |
| **Qdrant**       | 1.7.4   | Vector Database       | Self-hosted vector search   |
| **Weaviate**     | 1.22.4  | Alternative Vector DB | Self-hosted knowledge graph |
| **TensorFlow**   | 2.15.0  | ML Framework          | Self-hosted model training  |
| **scikit-learn** | 1.5.2   | Traditional ML        | Self-hosted algorithms      |

## Document Processing (Self-Hosted)

| Technology      | Version | Purpose             | Self-Hosted Implementation      |
| --------------- | ------- | ------------------- | ------------------------------- |
| **Tesseract**   | 5.3.3   | OCR Engine          | Self-hosted OCR processing      |
| **ImageMagick** | 7.1.1   | Image Processing    | Self-hosted image manipulation  |
| **LibreOffice** | 7.6.4   | Document Conversion | Self-hosted document processing |
| **Pandoc**      | 3.1.9   | Document Conversion | Self-hosted format conversion   |

## Communication & Notifications (Self-Hosted)

| Technology    | Version | Purpose                 | Self-Hosted Approach         |
| ------------- | ------- | ----------------------- | ---------------------------- |
| **Postal**    | Latest  | Email Server            | Self-hosted email delivery   |
| **Socket.io** | 4.7.5   | Real-time Communication | Self-hosted WebSocket server |
| **Node-cron** | 3.0.3   | Scheduled Tasks         | Self-hosted job scheduling   |
| **Bull MQ**   | 5.15.0  | Job Queue               | Self-hosted with Redis       |

## Monitoring & Logging (Self-Hosted)

| Technology     | Version | Purpose             | Self-Hosted Implementation  |
| -------------- | ------- | ------------------- | --------------------------- |
| **Grafana**    | 11.3.1  | Dashboards          | Self-hosted monitoring      |
| **Prometheus** | 2.55.1  | Metrics Collection  | Self-hosted metrics         |
| **Loki**       | 2.9.4   | Log Aggregation     | Self-hosted log management  |
| **Jaeger**     | 1.52.0  | Distributed Tracing | Self-hosted tracing         |
| **Winston**    | 3.11.0  | Application Logging | Self-hosted logging library |

## Payment Processing (Self-Hosted)

| Technology             | Version | Purpose              | Self-Hosted Approach          |
| ---------------------- | ------- | -------------------- | ----------------------------- |
| **Custom Payment API** | -       | Payment Processing   | Direct bank API integration   |
| **Crypto Payment**     | -       | Alternative Payments | Self-hosted crypto processing |
| **Invoice Generation** | -       | Billing              | Self-hosted PDF generation    |
