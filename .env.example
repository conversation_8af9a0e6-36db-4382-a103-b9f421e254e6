# AI Underwriting Platform - Environment Configuration
# Copy this file to .env and update the values

# ==========================================
# DOCKER SERVICES CONFIGURATION
# ==========================================

# PostgreSQL Database
POSTGRES_PORT=5433
POSTGRES_DB=underwriting_dev
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_CONTAINER_NAME=postgres-dev

# Redis Cache
REDIS_PORT=6380
REDIS_CONTAINER_NAME=redis-dev

# Ollama (Local LLM)
OLLAMA_PORT=11434
OLLAMA_CONTAINER_NAME=ollama-dev
OLLAMA_MODEL=llama3.1:8b

# ==========================================
# APPLICATION PORTS
# ==========================================

# Frontend (Next.js)
FRONTEND_PORT=3000

# Backend API
BACKEND_PORT=3001

# Prisma Studio
PRISMA_STUDIO_PORT=5555

# ==========================================
# DATABASE URLS
# ==========================================

# Main database connection
DATABASE_URL="postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@localhost:${POSTGRES_PORT}/${POSTGRES_DB}"

# Test database connection
TEST_DATABASE_URL="postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@localhost:${POSTGRES_PORT}/underwriting_test"

# Redis connection
REDIS_URL="redis://localhost:${REDIS_PORT}"
TEST_REDIS_URL="redis://localhost:${REDIS_PORT}/1"

# ==========================================
# AI SERVICES CONFIGURATION
# ==========================================

# OpenRouter API
OPENROUTER_API_KEY=YOUR_OPENROUTER_API_KEY_HERE
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Local Ollama
OLLAMA_URL=http://localhost:${OLLAMA_PORT}

# ==========================================
# AUTHENTICATION & SECURITY
# ==========================================

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=12

# Session
SESSION_SECRET=your-session-secret-change-in-production

# CORS
CORS_ORIGINS=http://localhost:${FRONTEND_PORT},http://localhost:${BACKEND_PORT}

# API Rate Limiting
API_RATE_LIMIT=100

# ==========================================
# EXTERNAL SERVICE APIs (Mock for Development)
# ==========================================

# Identity Verification
JUMIO_API_KEY=mock_jumio_key
JUMIO_API_SECRET=mock_jumio_secret

# Credit Bureau
EXPERIAN_API_KEY=mock_experian_key

# Banking/Financial
PLAID_CLIENT_ID=mock_plaid_client
PLAID_SECRET=mock_plaid_secret

# Email Service
POSTAL_API_KEY=development_postal_key
POSTAL_BASE_URL=http://localhost:9000

# ==========================================
# DEVELOPMENT CONFIGURATION
# ==========================================

# Environment
NODE_ENV=development
NEXT_PUBLIC_APP_ENV=development

# Logging
LOG_LEVEL=debug
ENABLE_ANALYTICS=true

# Mock Services
MOCK_EXTERNAL_SERVICES=true

# ==========================================
# FRONTEND ENVIRONMENT VARIABLES
# ==========================================

# API URLs
NEXT_PUBLIC_API_URL=http://localhost:${BACKEND_PORT}/api
NEXT_PUBLIC_WS_URL=ws://localhost:${BACKEND_PORT}

# OpenRouter (Public)
NEXT_PUBLIC_OPENROUTER_API_URL=${OPENROUTER_BASE_URL}

# ==========================================
# MONITORING & OBSERVABILITY
# ==========================================

# Application Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# Health Check
HEALTH_CHECK_INTERVAL=30000

# ==========================================
# PRODUCTION OVERRIDES (Leave empty for dev)
# ==========================================

# Production Database (override in production)
PROD_DATABASE_URL=
PROD_REDIS_URL=

# Production API Keys (override in production)
PROD_OPENROUTER_API_KEY=
PROD_JUMIO_API_KEY=
PROD_EXPERIAN_API_KEY=
PROD_PLAID_CLIENT_ID=