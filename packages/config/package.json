{"name": "@underwriting/config", "version": "1.0.0", "description": "Shared configuration for AI Underwriting Platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit"}, "dependencies": {"zod": "^4.0.5"}, "devDependencies": {"typescript": "^5.8.3"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./env": {"types": "./dist/env.d.ts", "default": "./dist/env.js"}, "./database": {"types": "./dist/database.d.ts", "default": "./dist/database.js"}}}