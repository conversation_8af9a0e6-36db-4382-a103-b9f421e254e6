import { z } from 'zod';

// Environment variable validation schema
const envSchema = z.object({
  // Application
  NODE_ENV: z
    .enum(['development', 'production', 'test'])
    .default('development'),

  // Ports
  FRONTEND_PORT: z.coerce.number().default(3000),
  BACKEND_PORT: z.coerce.number().default(3001),
  POSTGRES_PORT: z.coerce.number().default(5433),
  REDIS_PORT: z.coerce.number().default(6380),
  OLLAMA_PORT: z.coerce.number().default(11434),
  PRISMA_STUDIO_PORT: z.coerce.number().default(5555),

  // Database
  POSTGRES_DB: z.string().default('underwriting_dev'),
  POSTGRES_USER: z.string().default('postgres'),
  POSTGRES_PASSWORD: z.string().default('password'),
  DATABASE_URL: z.string(),
  REDIS_URL: z.string(),

  // Authentication
  JWT_SECRET: z.string().min(32),
  JWT_EXPIRES_IN: z.string().default('24h'),
  BCRYPT_ROUNDS: z.coerce.number().default(12),
  SESSION_SECRET: z.string().min(32),

  // AI Services
  OPENROUTER_API_KEY: z.string().optional(),
  OPENROUTER_BASE_URL: z.string().url().default('https://openrouter.ai/api/v1'),
  OLLAMA_URL: z.string().url(),
  OLLAMA_MODEL: z.string().default('llama3.1:8b'),

  // External Services
  JUMIO_API_KEY: z.string().optional(),
  JUMIO_API_SECRET: z.string().optional(),
  EXPERIAN_API_KEY: z.string().optional(),
  PLAID_CLIENT_ID: z.string().optional(),
  PLAID_SECRET: z.string().optional(),
  POSTAL_API_KEY: z.string().optional(),
  POSTAL_BASE_URL: z.string().url().optional(),

  // Security
  CORS_ORIGINS: z
    .string()
    .default('http://localhost:3000,http://localhost:3001'),
  API_RATE_LIMIT: z.coerce.number().default(100),
  SYSTEM_JWT_SECRET: z.string().optional(),
  RATE_LIMIT_WINDOW: z.coerce.number().default(15 * 60 * 1000),
  RATE_LIMIT_MAX: z.coerce.number().default(100),

  // Database Configuration
  DB_MAX_CONNECTIONS: z.coerce.number().default(10),
  DB_CONNECTION_TIMEOUT: z.coerce.number().default(30000),
  DB_QUERY_TIMEOUT: z.coerce.number().default(15000),

  // Redis Configuration  
  REDIS_MAX_CONNECTIONS: z.coerce.number().default(10),
  REDIS_CONNECT_TIMEOUT: z.coerce.number().default(5000),
  REDIS_COMMAND_TIMEOUT: z.coerce.number().default(3000),

  // Email/SMTP Configuration
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z.coerce.number().optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASS: z.string().optional(),
  SMTP_FROM: z.string().optional(),
  SMTP_SECURE: z.coerce.boolean().default(true),

  // External Service Configuration
  JUMIO_BASE_URL: z.string().url().optional(),
  PLAID_ENVIRONMENT: z.enum(['sandbox', 'development', 'production']).default('sandbox'),

  // Monitoring
  LOG_LEVEL: z.enum(['debug', 'info', 'warn', 'error']).default('debug'),
  ENABLE_ANALYTICS: z.coerce.boolean().default(true),
  ENABLE_METRICS: z.coerce.boolean().default(true),
  ENABLE_SWAGGER: z.coerce.boolean().default(true),
  MOCK_EXTERNAL_SERVICES: z.coerce.boolean().default(true),
});

export type EnvConfig = z.infer<typeof envSchema>;

let cachedEnv: EnvConfig | null = null;

export function validateEnv(): EnvConfig {
  if (cachedEnv) {
    return cachedEnv;
  }

  try {
    cachedEnv = envSchema.parse(process.env);
    return cachedEnv;
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.issues.map(
        (err: any) => `${err.path.join('.')}: ${err.message}`
      );
      throw new Error(
        `Environment validation failed:\n${missingVars.join('\n')}`
      );
    }
    throw error;
  }
}

export function getEnv(): EnvConfig {
  return validateEnv();
}
