import { getEnv } from './env';

export interface DatabaseConfig {
  url: string;
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
}

export interface RedisConfig {
  url: string;
  host: string;
  port: number;
}

export function getDatabaseConfig(): DatabaseConfig {
  const env = getEnv();

  return {
    url: env.DATABASE_URL,
    host: 'localhost',
    port: env.POSTGRES_PORT,
    database: env.POSTGRES_DB,
    username: env.POSTGRES_USER,
    password: env.POSTGRES_PASSWORD,
  };
}

export function getRedisConfig(): RedisConfig {
  const env = getEnv();

  return {
    url: env.REDIS_URL,
    host: 'localhost',
    port: env.REDIS_PORT,
  };
}

export function getTestDatabaseConfig(): DatabaseConfig {
  const config = getDatabaseConfig();
  return {
    ...config,
    database: 'underwriting_test',
    url: config.url.replace(config.database, 'underwriting_test'),
  };
}
