// Shared types for AI Underwriting Platform

// Base entity interface
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

// User and Authentication types
export interface User extends BaseEntity {
  email: string;
  name: string;
  role: UserRole;
  isActive: boolean;
}

export type UserRole = 'admin' | 'underwriter' | 'viewer';

// Application types
export interface Application extends BaseEntity {
  applicantId: string;
  businessName: string;
  status: ApplicationStatus;
  riskScore: number;
  documentIds: string[];
  underwriterId?: string;
  notes?: string;
}

export type ApplicationStatus =
  | 'draft'
  | 'submitted'
  | 'under_review'
  | 'approved'
  | 'rejected'
  | 'requires_documents';

// Document types
export interface Document extends BaseEntity {
  fileName: string;
  fileType: string;
  fileSize: number;
  applicationId: string;
  extractedData?: Record<string, any>;
  ocrText?: string;
  isProcessed: boolean;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// AI and Analysis types
export interface RiskAnalysis {
  score: number;
  factors: RiskFactor[];
  recommendation: string;
  confidence: number;
}

export interface RiskFactor {
  category: string;
  impact: number;
  description: string;
}
