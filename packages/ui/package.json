{"name": "@underwriting/ui", "version": "1.0.0", "description": "Shared UI components for AI Underwriting Platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "typescript": "^5.8.3"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}