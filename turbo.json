{"$schema": "https://turbo.build/schema.json", "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"], "env": ["NODE_ENV"]}, "dev": {"dependsOn": ["^build"], "cache": false, "persistent": true}, "lint": {"dependsOn": ["^build"], "outputs": []}, "lint:fix": {"dependsOn": ["^build"], "outputs": []}, "typecheck": {"dependsOn": ["^build"], "outputs": []}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "clean": {"cache": false}}, "globalDependencies": ["**/.env*"]}